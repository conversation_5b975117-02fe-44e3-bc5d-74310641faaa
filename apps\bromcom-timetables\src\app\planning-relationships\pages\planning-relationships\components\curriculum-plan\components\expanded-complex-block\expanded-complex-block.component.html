<div class="expanded-complex-block-container">

  <bromcom-expanded-header [selectedBlock]="selectedBlock"
                           [selectedYearGroupId]="selectedYearGroupId"
                           [timetableId]="timetableId"
                           (openEditClassCodesModalEvent)="openEditClassCodesModal($event)"
                           (openLinkBlocksModalComponentEvent)="openLinkBlocksModal($event)">
  </bromcom-expanded-header>

  <div class="subject-selector">
    <table *ngIf="selectedBlock?.subjectToYearGroups">
      <thead>
      <tr>
        <th class="border"
            [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}"
            [ngStyle]="{'background-color' : subject?.subjectId | getSubjectColor: subjects}"
            *ngFor="let subject of selectedBlock.subjectToYearGroups | filterToContainsSubject">
          <div class="block">
            {{subject.subjectId | getSubjectCode:subjects }}
          </div>
        </th>
      </tr>
      </thead>
    </table>
  </div>

  <div class="actions" [formGroup]="form">
    <div>
      <label for="periodCount">
        {{'Total Period Count' | translate}}
      </label>
      <input id="periodCount"
             class="input readonly"
             formControlName="periodCount"
             readonly/>
    </div>

    <div>
      <label>
        {{'Number of Classes' | translate}}
      </label>
      <input class="input readonly"
             formControlName="classCount"
             readonly/>
    </div>

    <div>
      <label>
        {{'Single Periods' | translate}}
      </label>
      <input class="input readonly"
             formControlName="singlePeriodCount"
             readonly/>
    </div>

    <div>
      <label>
        {{'Double Periods' | translate}}
      </label>
      <input class="input readonly"
             formControlName="doublePeriodCount"
             readonly/>
    </div>
  </div>

  <div class="panel">
    <bromcom-expanded-detailed-complex-block [staffs]="staffs"
                                             [rooms]="rooms"
                                             [form]="form"
                                             [projectId]="projectId"
                                             [missingStaffRoomSessionId]="missingStaffRoomSessionId"
                                             [staffToSessionActiveId]="staffToSessionActiveId"
                                             [roomToSessionActiveId]="roomToSessionActiveId"
                                             [periodMenuRef]="periodMenuRef"
                                             [sessionMenuRef]="sessionMenuRef"
                                             [classMenuRef]="classMenuRef"
                                             [resetSelectedPeriodIds$]="resetSelectedPeriodIds$"
                                             [resetSelectedClassIds$]="resetSelectedClassIds$"
                                             [setScrollToPeriodIndex$]="setScrollToPeriodIndex$"
                                             (sessionMenuOpenedEvent)="sessionMenuOpened($event)"
                                             (updateAnExistingSubjectToYearGroup)="updateAnExistingSubjectToYearGroup()"
                                             [subjectMenuRef]="subjectMenuRef"
                                             (subjectMenuOpenedEvent)="subjectMenuOpened($event)"
                                             (periodMenuOpenedEvent)="periodMenuOpened($event)"
                                             (classMenuOpenedEvent)="classMenuOpened($event)"
    ></bromcom-expanded-detailed-complex-block>
  </div>

</div>
