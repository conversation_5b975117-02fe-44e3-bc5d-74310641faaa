import { transformToAGGridConfig} from '@bromcom/core';
import { AgGridNoDataComponent } from '@bromcom/ui';
import { ICellRendererParams } from 'ag-grid-community';
import { AutoScheduleNccIssuesComponent } from './auto-schedule-ncc-issues.component';
import {
  NccStaffIssueCellRendererComponent
} from './ncc-staff-issue-cell-renderer/ncc-staff-issue-cell-renderer.component';


export function gridOptions(this: AutoScheduleNccIssuesComponent) {
  return transformToAGGridConfig({
    animateRows: false,
    suppressRowClickSelection: true,
    suppressClickEdit: true,
    suppressCellFocus: true,
    tooltipShowDelay: 500,
    noRowsOverlayComponent: AgGridNoDataComponent,
    noRowsOverlayComponentParams: {
      style: 'display: flex; justify-content: center; align-items: center; height: 100%; width: 100%; min-height: 162px; padding: 48px 0 0 0',
      noRowsMessageFunc: () => this.translate.instant(`No data available!`)
    },
    enableCellChangeFlash: false,
    defaultColDef: {
      wrapText: true,
      autoHeight: true,
      flex: 1,
      sortable: false,
    },
    columnDefs: [
      {
        field: 'groupAlias',
        headerName: this.translate.instant('Group Alias'),
        wrapHeaderText: true,
        minWidth: 160,
        menuTabs: [],
      },
      {
        field: 'nonContactCodeName',
        headerName: this.translate.instant('Non-Contact Code Name'),
        minWidth: 120,
        flex: 2.5,
        editable: true,
        wrapText: true,
        menuTabs: [],
      },
      {
        field: 'staffInfo',
        headerName: this.translate.instant('Staff Members'),
        minWidth: 180,
        cellRenderer: NccStaffIssueCellRendererComponent,
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            staffNames: params.data.staffNames,
          }
        },
        editable: false,
        menuTabs: [],
      },
      {
        field: 'roomName',
        headerName: this.translate.instant('Room'),
        wrapHeaderText: true,
        wrapText: true,
        minWidth: 120,
        menuTabs: [],
        cellStyle: {
          wordBreak: 'break-word',
        },
      },
      {
        field: 'periodNames',
        headerName: this.translate.instant('Period'),
        minWidth: 120,
        flex: 1.5,
        tooltipValueGetter: params => params.data.periodNames.join(', '),
        cellRenderer: (params: ICellRendererParams) => {
          if (Array.isArray(params.value)) {
            return params.value.map(name => `<div>${name}</div>`).join('');
          }
          return '';
        },
        menuTabs: [],
      },
      {
        field: 'reasons',
        headerName: this.translate.instant('Reason'),
        minWidth: 250,
        wrapText: true,
        flex: 3,
        menuTabs: [],
        cellStyle: {
          wordBreak: 'break-word',
        },
        tooltipValueGetter: params => params.data.reasons.join(', '),
        cellRenderer: (params: ICellRendererParams) => {
          if (Array.isArray(params.value)) {
            return params.value.map(reason => `<div>${reason}</div>`).join('');
          }
          return '';
        },
      },
    ]
  })
}
