import { Component, OnInit } from '@angular/core';
import { BaseBlockComponent } from "../../_shared/base-block-component";
import { distinctUntilChanged, switchMap, takeUntil } from "rxjs";
import { CurriculumPlanService } from "../../../../../../services/curriculum-plan.service";
import { SnackbarService } from "@bromcom/ui";
import { CurriculumPlanBlocksService } from "../../../../../../services/curriculum-plan-blocks";
import { CdkDragDrop } from "@angular/cdk/drag-drop";
import { updateLastVisibleIndexForBlocks } from "../../_shared/base-helper";

@Component({
  selector: 'bromcom-expanded-linear-block',
  templateUrl: './expanded-linear-block.component.html',
  styleUrls: ['./expanded-linear-block.component.scss']
})
export class ExpandedLinearBlockComponent extends BaseBlockComponent implements OnInit {
  isHovered = false;

  constructor(curriculumPlanService: CurriculumPlanService,
              snackbarService: SnackbarService,
              curriculumPlanBlocksService: CurriculumPlanBlocksService) {
    super(curriculumPlanService, snackbarService, curriculumPlanBlocksService);
  }

  override ngOnInit() {
    super.ngOnInit();
    this.curriculumPlanBlocksService.isSubjectDraggingActive$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(isSubjectDraggingActive => {
        this.isSubjectDraggingActive = isSubjectDraggingActive;
      })

    this.curriculumPlanBlocksService.expandedSelectedSubjectIndex$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(index => {
        const selectedSubjectId = this.selectedBlock.subjectToYearGroups[index]?.id
        if (selectedSubjectId) {
          this.selectedSubjectId = selectedSubjectId;
        }
      })
  }

  onMouseEnter() {
    this.isHovered = true;
    this.curriculumPlanBlocksService.isAnyBlockHovered$.next(true);
  }

  onMouseLeave() {
    this.isHovered = false;
    this.curriculumPlanBlocksService.isAnyBlockHovered$.next(false);
  }

  subjectClicked(selectedSubjectId: number): void {
    this.selectedSubjectId = selectedSubjectId;
    this.subjectIndex = this.selectedBlock.subjectToYearGroups.findIndex(item => item.id === selectedSubjectId);
    this.curriculumPlanBlocksService.expandedSelectedSubjectIndex$.next(this.subjectIndex);
    this.updateForm(this.selectedBlock);
  }

  dropSubject(event: CdkDragDrop<any, any>): void {
    if (!event.isPointerOverContainer) {
      return;
    }
    if (this.selectedBlock?.id) {
      this.isAddSubject = true;
      this.curriculumPlanBlocksService
        .addSubjectToBlock({
          blockId: this.selectedBlock.id,
          subjectId: event.item.data.id,
          sessionIds: []
        })
        .pipe(switchMap(() => this.curriculumPlanService.getBlock(this.selectedBlock.id)))
        .subscribe(block => {
          this.curriculumPlanBlocksService.blockSideStateChanged$.next();
          this.curriculumPlanBlocksService.stateChanged$.next();
          this.updateAnExistingSubjectToYearGroup();
          updateLastVisibleIndexForBlocks(block, this.curriculumPlanBlocksService.lastVisibleIndexForBlocks);
        });
    }
  }
}
