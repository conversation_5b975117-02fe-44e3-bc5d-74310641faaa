@use '../../../../../../styles/expanded-detailed-block' as style;
@import '../../../../../../../../assets/styles/variables';

.detailed-container {
  @include style.container;

  .block {
    @include style.block;

    .period-count {
      display: flex;

      .simple {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-right: 10px;

      }

      .double {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding-left: 10px;

      }
    }

    .title {
      @include style.title;
      height: 40px;
      display: flex;
      flex-direction: column;
    }

    .footer {
      @include style.footer;
    }

    table {
      @include style.table;
    }

    .left-action {
      border-top-left-radius: 4px;
      pointer-events: all;
    }

    .right-action {
      border-top-right-radius: 4px;
    }

    .session-td {
      @include style.session-td;
    }

    table, th, tr, td {
      @include style.table-th-tr-td;
    }

    .period-cell {
      position: relative;
      text-align: center;
      padding: 0.5em 1em;
    }

    .link-chip {
      position: absolute;
      top: 50%;
      right: 2px;
      width: 30px;
      transform: translateY(-50%);
    }
  }

  .session-room-icon {
    @include style.session-room-icon;
  }

  .disable .icon, .disable {
    color: $color-blue-grey-300 !important;
    pointer-events: none;
  }

  .white-color {
    color: $color-white-0 !important;
  }

  .footer {
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
  }

  .footer.dashed,
  .class-name.dashed,
  .staff-side.dashed,
  .room-side.dashed {
    border: 2px dashed $color-blue-800 !important;

    &:hover {
      background-color: $color-blue-grey-300 !important;
    }
  }

  .room-side.room-filter-active-border {
    border: 2px dashed $color-blue-500;
    margin: 0 -2px;
  }

  .staff-side.staff-filter-active-border {
    border: 2px dashed $color-blue-500;
    margin: 0 -2px;
  }

}

thead {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: $color-blue-grey-100;
}
