@import 'apps/bromcom-timetables/src/assets/styles/variables';

.diagram-container {
  position: relative;
  height: calc(100% - 10px);
  padding-left: 10px;
  padding-right: 10px;

  bromcom-context-menu {
    position: absolute;
    display: block;
  }

  .diagram {
    background: $color-white-0;
    height: calc(100% - 62px);

    .gojs-tooltip {
      position: absolute;
      z-index: 1000;
      color: $color-white-0;
      background-color: $color-black-0;
      display: block;
      font-size: 12px;
      padding: 3px;
      border-radius: 3px;
      white-space: nowrap; 

      .arrow-bottom {
        left: 50%;
        bottom: -4px;
        opacity: 1;
        visibility: visible;
        transform: rotate(45deg);
        position: absolute;
        width: 12px;
        height: 12px;
        background-color: $color-black-0;
        z-index: -1;
      }
    }

    canvas {
      border: 1px solid $color-blue-grey-200;
      border-radius: 8px;
    }

    canvas + div {
      width: 25vw;
      min-width: 150px;
      background: $color-white-0;
      overflow-y: scroll;

      /* total width */
      &::-webkit-scrollbar {
        background-color: $color-white-0;
        width: 16px;
      }

      /* background of the scrollbar except button or resizer */
      &::-webkit-scrollbar-track {
        background-color: $color-white-0;
      }

      &::-webkit-scrollbar-track:hover {
        background-color: $color-white-0;
      }

      /* scrollbar itself */
      &::-webkit-scrollbar-thumb {
        background-color: $color-blue-grey-300;
        border-radius: 16px;
        border: 5px solid $color-white-0;
      }

      &::-webkit-scrollbar-thumb:hover {
        background-color: $color-blue-grey-400;
        border: 4px solid $color-blue-grey-100;
      }

      /* set button(top and bottom of the scrollbar) */
      &::-webkit-scrollbar-button {
        display: none;
      }
    }
  }

  .palette {
    display: flex;
    flex-direction: column;
    background: $color-white-0;
    height: 100%;

    .placeholder {
      height: 0;
      overflow: hidden;
    }

    .blocks {
      display: flex;
    }
  }

  .box {
    align-items: center;
    justify-content: center;
    padding: 10px;
    margin: 10px;
  }
}
