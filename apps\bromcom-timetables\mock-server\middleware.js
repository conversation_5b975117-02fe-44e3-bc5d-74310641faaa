const jsonServer = require('json-server');
const mockDb = require('./mock-db');
const mockRoutes = require('./routes');
// const pause = require('connect-pause');

const SERVER_PORT = 9988;

const server = jsonServer.create();
const router = jsonServer.router(mockDb);
const middlewares = jsonServer.defaults();
const db = router.db;

// setup json-server middleware
server.use(middlewares);
server.use(jsonServer.bodyParser);

// Use this if you wan't to delay the requests
// server.use(pause(3000));
// server.use((req, res, next) => {
//   res.header('X-Context-Id', 'Vf8zKzSmTi4LWRMtDHM8');
//   res.header('Access-Control-Allow-Headers', 'X-Context-Id');
//   res.header('Access-Control-Expose-Headers', 'X-Context-Id');
//   next();
// });

mockRoutes(server, db);

router.render = (req, res) => {
  res.header('Access-Control-Expose-Headers', '*')
  res.send(res.locals.data)
}

server.use(router);
server.listen(SERVER_PORT, () => {
  console.log(`MOCK SERVER is running on ${SERVER_PORT}`);
});
