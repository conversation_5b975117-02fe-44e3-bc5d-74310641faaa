@use '../../../../../../styles/expanded-block' as style;
@import '../../../../../../../../assets/styles/variables';

.expanded-complex-block-container {
  background-color: $color-blue-grey-100;
  min-height: 100%;

  .subject-selector {
    overflow: auto;
    
    table {
      margin-bottom: 16px;
      width: 100%;

      th {
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 20px;
        color: $color-blue-grey-600;

        .block {
          display: flex;
          flex-direction: column;
          flex-grow: 1;
        }
      }

      td, th {
        border: 1px solid $color-blue-grey-200;
        height: 24px;
        width: 220px;

        &.class-name {
          font-style: normal;
          font-weight: 500;
          font-size: 12px;
          line-height: 20px;
          color: $color-blue-grey-600;
        }
      }
    }

    table, th, tr, td {
      border-spacing: 0;
      text-align: center;

      &.border {
        background-color: $color-blue-grey-100;
      }
    }
  }

  .disable .icon, .disable {
    color: $color-blue-grey-300 !important;
    pointer-events: none;
  }

  .disabled {
    pointer-events: none;
    color: $color-blue-grey-300;
  }

  .actions {
    @include style.actions;

    div {
      &:last-child {
        padding-top: 0;
        margin-right: 8px;
      }
    }
  }

  .error {
    @include style.error;
  }

  .white-color {
    color: $color-white-0 !important;
  }
}

@media screen and (max-width: 1279px) {
  .expanded-complex-block-container {
    .actions {
      flex-wrap: wrap;

      div {
        max-width: 138px !important;
      }
    }
  }
}
