<div class="detailed-container"
     (mouseenter)="onMouseEnter()"
     (mouseleave)="onMouseLeave()"
     #detailedContainer>

  <div class="block">
    <table>
      <thead>
      <tr>
        <th class="action border left-action"
            (click)="leftClick($event)"
            [class.disable]="isLeftStepperDisabled()">
          <bcm-icon class="icon"
                    icon="far fa-arrow-to-left">
          </bcm-icon>
        </th>

        <th class="border period"
            *ngFor="let item of (selectedBlock! | getComplexBlockTransformedSessions)?.[0]?.[0] | slice:firstIndex:lastIndex; let i = index;"
            [class.selected]="selectedPeriodIndexes.includes(firstIndex + i)"
            (contextmenu)="addToSelectedPeriods($event, firstIndex + i)"
            [matMenuTriggerData]="{data: {disableDeletion: (selectedBlock! | getComplexBlockTransformedSessions)[0][0].length === 1 || selectedPeriodIndexes.length === (selectedBlock! | getComplexBlockTransformedSessions)[0][0].length, blockMenuId: firstIndex + i, selectedPeriodIndexes: (selectedPeriodIndexes.length > 0 ? selectedPeriodIndexes : [firstIndex + i]), selectedPeriodIndexes$, blockId: selectedBlock!.id, subjectToYearGroupId: item.subjectToYearGroupId, currentIndex: firstIndex + i, lastIndex}}"
            [matMenuTriggerFor]="periodMenuRef"
            (menuOpened)="periodMenuOpened({block: selectedBlock, subjectIndex, selectedPeriodIndexes, index: i +1, blockId: selectedBlock!.id, sessions: selectedBlock?.sessions, onlySessionRelated: true, showExpandedViewButtons: true, isCombineSessionsEnabled: isSessionsNextToEachOther, selectedSessionIndexes, selectedSessionIndexes$, selectedSessionIds, selectedSessionIds$})"
            [class.dashed]="isSubjectDraggingActive"
            cdkDropList
            [id]="'expandedComplexFullPeriod-' + selectedBlock?.id + '-' + (i + 1)"
            (cdkDropListDropped)="dropToPeriodOrClass($event, 'period', firstIndex + i + 1)">
          P{{firstIndex + i + 1}}
        </th>

        <th [class.hidden-column]="!isSubjectDraggingActive"
            [class.dashed]="isSubjectDraggingActive"
            cdkDropList
            [id]="'expandedComplexNewFullPeriod' + selectedBlock?.id"
            (cdkDropListDropped)="dropSubjectNewFullRowOrColumn($event, 'period')">
          P{{(selectedBlock! | getComplexBlockTransformedSessions)?.[0]?.[0]?.length! + 1}}</th>

        <th class="action border right-action"
            (click)="rightClick()"
            [class.disable]="(selectedBlock! | getComplexBlockTransformedSessions)?.[0]?.[0] && lastIndex >= (selectedBlock! | getComplexBlockTransformedSessions)[0][0].length">
          <bcm-icon class="icon"
                    icon="far fa-arrow-to-right">
          </bcm-icon>
        </th>
      </tr>
      </thead>

      <tbody>

      <ng-container
        *ngFor="let row of (selectedBlock! | getComplexBlockTransformedSessions); let rowIndex = index">
        <tr *ngFor="let classArray of row; let i = index" style="position: relative">
          <ng-container *ngIf="i === 0">
            <td class="class-name border"></td>
            <td
              *ngFor="let session of classArray | slice:firstIndex:lastIndex, index as sessionIndex"
              class="no-padding"
              [class.selected]="selectedSubjectSessionIds.includes(session.id)"
              [class.dashed]="isSubjectDraggingActive &&
              (!(selectedBlock! | getSubjectToSession: session.subjectToYearGroupId).subjectId ||
              (selectedBlock! | getSubjectToSession: session.subjectToYearGroupId).subjectId === freeSubjectId)"
              cdkDropList
              [id]="'expandedEmptyComplexSubjectPlaceholder' + session.id"
              (cdkDropListDropped)="dropToExistingSubject($event, session)">
              <div class="title clickable"
                   (contextmenu)="addToSelectedSubjectSessions(session.id)"
                   [matMenuTriggerFor]="subjectMenuRef"
                   (menuOpened)="subjectMenuOpened({data: {block: selectedBlock!, blockId: selectedBlock!.id, subjectToYearGroupId: session.subjectToYearGroupId, isComplexBlock: true, sessionIndex, className: session.className, expandedSelectedSubjectIndex: subjectIndex, showExpandedViewButtons: true, classNameIndex: +session.className.split('-')[0], periodIndex: session.periodIndex, selectedSubjectSessionIds$, selectedSubjectSessionIds, lastIndex}})"
                   [id]="expandedSubjectString + session.id"
                   [ngClass]="{
                   'white-color': isColorDarkHelper(session.subjectToYearGroupId | getSubjectColorBySubjectToYearGroupId : selectedBlock?.subjectToYearGroups)
                   }"
                   [ngStyle]="{'background-color' : '#' + (session.subjectToYearGroupId | getSubjectColorBySubjectToYearGroupId : selectedBlock?.subjectToYearGroups)  || '#fff'}">

                {{ session.subjectToYearGroupId | getSubjectCodeBySubjectToYearGroupId: selectedBlock?.subjectToYearGroups}}

                <div class="subject-periods">
                  <div class="single-period">
                    {{'S' | translate}}: {{session.joinedSessions.length === 1 ? 1 : 0 }}
                  </div>
                  <div class="double-period">
                    {{'D' | translate}}: {{session.joinedSessions.length === 2 ? 1 : 0 }}
                  </div>
                </div>
              </div>
            </td>

            <td style="width: 100px;"
                [class.hidden]="!(isSubjectDraggingActive)"
                [class.dashed]="isSubjectDraggingActive"
                cdkDropList
                [id]="'expandedEmptyComplexSubjectPlaceholder:h:' + classArray[0].id"
                (cdkDropListDropped)="dropSubjectHelper($event)">
              <div>--</div>
              <div class="subject-periods">
                <div class="single-period">
                  {{'S' | translate}}: --
                </div>
                <div class="double-period">
                  {{'D' | translate}}: --
                </div>
              </div>
            </td>

            <td class="action border"
                [class.hidden-action]="!(isSubjectDraggingActive)">
            </td>
          </ng-container>

        </tr>

        <tr *ngFor="let classArray of row, index as classArrayIndex;">
          <td class="class-name border"
              [class.dashed]="isSubjectDraggingActive || isStaffDraggingActive || isRoomDraggingActive"
              [class.selected]="selectedClassIndexes.includes(firstIndex + classArray[0].classIndex)"
              cdkDropList
              [id]="'expandedComplexFullClass-' + selectedBlock?.id + '-' + (classArray[0].classIndex)"
              (cdkDropListDropped)="dropToPeriodOrClass($event, 'class', classArray[0].classIndex)"
              (contextmenu)="addToSelectedClasses($event, firstIndex + classArray[0].classIndex, classArray[0].className)"
              [matMenuTriggerFor]="classMenuRef"
              [matMenuTriggerData]="{data: {currentIndex:  firstIndex + classArray[0].classIndex}}"
              (menuOpened)="classMenuOpened({block: selectedBlock, subjectIndex, selectedClassIndexes, currentIndex: firstIndex + classArray[0].classIndex, blockId: selectedBlock!.id, sessions: selectedBlock?.sessions, classData, isComplex: true})">
            {{ classArray[0].className | getClassName }}
          </td>

          <td
            *ngFor="let session of classArray | slice:firstIndex:lastIndex, index as sessionIndex;"
            class="session-td"
            [id]="session.id.toString()"
            [class.selected]="selectedSessionIds.includes(session.id)"
            [class.conflict]="staffConflictedSessionIds.includes(session.id) || roomConflictedSessionIds.includes(session.id)"
            [class.missing]="missingStaffRoomSessionId === session.id"
            [ngClass]="{
              'left-double': session.joinedSessions.length > 1 && selectedBlock && (selectedBlock.sessions | getSessionPeriodIndex:session.joinedSessions[1]) > session.periodIndex!,
               'right-double': session.joinedSessions.length > 1 && selectedBlock && (selectedBlock.sessions | getSessionPeriodIndex:session.joinedSessions[1]) < session.periodIndex!
               }">
            <div class="block-body"
                 [id]="expandedSessionString + session.id"
                 (contextmenu)="addToSelectedSessions(session.id, session.periodIndex)"
                 [matMenuTriggerFor]="sessionMenuRef"
                 (menuOpened)="sessionMenuOpened({block: selectedBlock, blockId: selectedBlock!.id, blockMenuId: expandedSessionString + session.id, session, onlySessionRelated: true, showExpandedViewButtons: true, classNameIndex: +session.className.split('-')[0], periodIndex: session.periodIndex, isCombineSessionsEnabled: isSessionsNextToEachOther, selectedSessionIndexes, selectedSessionIndexes$, selectedSessionIds, selectedSessionIds$})">
              <div class="session"
                   [class.edited]="session.hasClassNameDefinition">
                {{ session | getSessionName: selectedBlock?.subjectToYearGroups}}
              </div>

              <div class="period">
                {{session.periodName || '--'}}
                <div class="lock-icon">
                  <bcm-icon *ngIf="session.isLocked" icon="fa fa-lock"></bcm-icon>
                </div>
              </div>

              <div class="line staff-side"
                   [class.dashed]="isHovered && isStaffDraggingActive && (!session.mainStaffId || shiftKeyPressed) &&
                   (selectedBlock! | getSubjectToSession:session.subjectToYearGroupId).subjectId &&
                   (selectedBlock! | getSubjectToSession: session.subjectToYearGroupId).subjectId !== freeSubjectId"
                   [class.staff-filter-active-border]="staffToSessionActiveId === session.id"
                   cdkDropList
                   [id]="expandedStaffString + session.id"
                   (cdkDropListDropped)="dropStaff($event)"
                   (mouseenter)="staffDragEnterInformation($event, selectedBlock!.sessions, expandedStaffString, !shiftKeyPressed)"
                   (mouseleave)="staffDragLeaveInformation()">
                <bromcom-expanded-view-staff-section [session]="session"
                                                     [staffConflictedSessionIds]="staffConflictedSessionIds"
                                                     [staffs]="staffs"
                                                     [staffToSessionActiveId]="staffToSessionActiveId"
                                                     [isFreeSession]="session.subjectToYearGroupId === selectedBlock!.subjectToYearGroups[0]!.id"
                ></bromcom-expanded-view-staff-section>
              </div>

              <div class="line room-side"
                   [class.dashed]="isHovered && isRoomDraggingActive && !session.roomId &&
                   (selectedBlock! | getSubjectToSession:session.subjectToYearGroupId).subjectId &&
                   (selectedBlock! | getSubjectToSession: session.subjectToYearGroupId).subjectId !== freeSubjectId"
                   [class.room-filter-active-border]="roomToSessionActiveId === session.id"
                   cdkDropList
                   [id]="expandedRoomString + session.id"
                   (cdkDropListDropped)="dropRoom($event)"
                   (mouseenter)="roomDragEnterInformation($event, selectedBlock!.sessions, expandedRoomString)"
                   (mouseleave)="roomDragLeaveInformation()">
                <div class="session-room-icon marker">
                  <bcm-icon class="icon" icon="far fa-map-marker-alt"
                            [class.room-conflict]="roomConflictedSessionIds.includes(session.id)"
                            [class.room-filter-active]="roomToSessionActiveId === session.id"
                            (click)="roomToSession($event, session)"></bcm-icon>
                </div>
                <div class="text">
                  <bcm-tooltip *ngIf="session.roomId; else empyRoomIdTemplate"
                    [message]="'Room: ' + (session.roomId | roomPipe: rooms)"
                    trigger="hover">
                      <div class="session-room">{{session.roomId | roomPipe: rooms}}</div>
                    </bcm-tooltip>
                    <ng-template #empyRoomIdTemplate>
                      <bcm-tooltip 
                          [message]="'Room: --'"
                          trigger="hover">
                          <div class="session-room">--</div>
                      </bcm-tooltip>
                    </ng-template>
                </div>
              </div>
            </div>
          </td>

          <td *ngIf="(isSubjectDraggingActive)" class="session-td">
            <div class="block-body">
              <div class="session">
                --
              </div>

              <div class="period">
                --
              </div>

              <div class="line staff-side">
                <div class="session-staff-icon">
                  <bcm-icon class="icon" icon="far fa-users-class"></bcm-icon>
                </div>

                <div class="text">
                  <div>--</div>
                </div>
              </div>

              <div class="line room-side">
                <div class="session-room-icon marker">
                  <bcm-icon class="icon" icon="far fa-map-marker-alt"></bcm-icon>
                </div>
                <div class="text">
                  <div>--</div>
                </div>
              </div>
            </div>

          </td>

          <td class="action border">
            <bcm-icon class="icon"
                      icon="far fa-ellipsis-h"
                      [id]="expandedBulkSessionString + row[classArrayIndex][0].className + row[classArrayIndex][0].id"
                      [matMenuTriggerFor]="sessionMenuRef"
                      (menuOpened)="sessionMenuOpened({block: selectedBlock, blockId: selectedBlock!.id, blockMenuId: expandedBulkSessionString + row[classArrayIndex][0].className + row[classArrayIndex][0].id, session: transformSessionsByClass(selectedBlock?.sessions ?? [], row[classArrayIndex][0].subjectToYearGroupId, row[classArrayIndex][0].className, true), showExpandedViewButtons: true, isComplexBlock: true, onlySessionRelated: false})">
            </bcm-icon>
          </td>
        </tr>
      </ng-container>

      <tr style="position: relative">
        <th *ngIf="(isSubjectDraggingActive)"></th>
        <th
          *ngFor="let item of (selectedBlock! | getComplexBlockTransformedSessions)?.[0]?.[0] | slice:firstIndex:lastIndex; let i = index;"
          style="width: 100px; height: 48px"
          [ngStyle]="{left: (i * 100) - 22 + 'px'}"
          [class.hidden-bottom]="!(isSubjectDraggingActive)"
          [class.dashed]="isSubjectDraggingActive"
          cdkDropList
          [id]="'expandedEmptyComplexSubjectPlaceholder:v:' + item.id"
          (cdkDropListDropped)="dropSubjectHelper($event)">
          <div>--</div>
          <div class="subject-periods">
            <div class="single-period">
              {{'S' | translate}}: --
            </div>
            <div class="double-period">
              {{'D' | translate}}: --
            </div>
          </div>
        </th>
        <th *ngIf="( isSubjectDraggingActive)"></th>
        <th *ngIf="(isSubjectDraggingActive)"></th>
      </tr>

      <tr [class.hidden-row]="!isSubjectDraggingActive">
        <th [class.dashed]="isSubjectDraggingActive"
            [class.hide-th]="!isSubjectDraggingActive"
            cdkDropList
            [id]="'expandedComplexNewFullClass' + selectedBlock?.id"
            (cdkDropListDropped)="dropSubjectNewFullRowOrColumn($event, 'class')">
        </th>
        <th
          *ngFor="let item of (selectedBlock! | getComplexBlockTransformedSessions)?.[0]?.[0] | slice:firstIndex:lastIndex; let i = index;"
          [class.hidden-row]="!isSubjectDraggingActive"
          class="session-td">

          <div class="block-body">
            <div class="session">
              --
            </div>

            <div class="period">
              --
            </div>

            <div class="line staff-side">
              <div class="session-staff-icon">
                <bcm-icon class="icon" icon="far fa-users-class"></bcm-icon>
              </div>

              <div class="text">
                <div>--</div>
              </div>
            </div>

            <div class="line room-side">
              <div class="session-room-icon marker">
                <bcm-icon class="icon" icon="far fa-map-marker-alt"></bcm-icon>
              </div>
              <div class="text">
                <div>--</div>
              </div>
            </div>
          </div>

        </th>
        <th [class.hidden-row]="!isSubjectDraggingActive"></th>
        <th [class.hidden-row]="!isSubjectDraggingActive"></th>
      </tr>

      </tbody>
    </table>
    <div class="footer"></div>
  </div>
</div>

