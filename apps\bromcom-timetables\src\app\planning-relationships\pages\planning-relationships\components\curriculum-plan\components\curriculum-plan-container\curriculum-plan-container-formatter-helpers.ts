import { ICurriculumPlanBlock } from '../../../../../../../_shared/models/ICurriculumPlanBlock';
import { CurriculumPlanContainerComponent } from './curriculum-plan-container.component';
import { ICurriculumPlanNewBlockResponse } from '../../../../../../../_shared/models/ICurriculumPlanNewBlockResponse';
import { BLOCK_TYPE } from '../../../../../../../_shared/enums/BlockType';
import { ISubject } from '../../../../../../../_shared/models/ISubject';
import { IRelationshipsStaff } from '../../../../../../../_shared/models/IRelationshipsStaff';
import { IRelationshipsRoom } from '../../../../../../../_shared/models/IRelationshipsRoom';

export function formatBlock(this: any, blocks: ICurriculumPlanBlock[]): ICurriculumPlanBlock[] {
  const subjects = this.relationships?.subjectsData$.getValue() ?? this.relationshipsService.subjectsData$.getValue();
  const staffs = this.relationships?.originalStaffsData$.getValue() ?? this.relationshipsService.originalStaffsData$.getValue();
  const rooms = this.relationships?.originalRoomsData$.getValue() ?? this.relationshipsService.originalRoomsData$.getValue();

  return blocks.map(block => {
    return blockFormatter(block, subjects, staffs, rooms);
  })
}

export const blockFormatter = (block: ICurriculumPlanBlock, subjects: ISubject[], staffs: IRelationshipsStaff[], rooms: IRelationshipsRoom[]): ICurriculumPlanBlock => {
  return {
    ...block,
    subjectToYearGroups: block.subjectToYearGroups.map(blockSubject => {
      const {
        color,
        code,
        textColor
      } = subjects.find(subject => subject.id === blockSubject.subjectId) ?? {};
      return {
        ...blockSubject,
        color,
        textColor,
        code
      }
    }),
    sessions: block.sessions.map(session => ({
      ...session,
      staffIds: ([] as number[]).concat(session.additionalStaffIds, ...(session.mainStaffId ? [session.mainStaffId] : [])),
      mainStaffCode: staffs.find(staff => staff.id === session.mainStaffId)?.code ?? null,
      additionalStaffCodes: session.additionalStaffIds
        .map(staffId => staffs.find(staff => staff.id === staffId)?.code)
        .filter(staffCode => staffCode !== undefined)
        .map(staffCode => staffCode!),
      staffCodes: [session.mainStaffId, ...session.additionalStaffIds]
        .map(staffId => staffs.find(staff => staff.id === staffId)?.code)
        .filter(staffCode => staffCode !== undefined)
        .map(staffCode => staffCode!),
      roomCode: rooms.find(room => room.id === session.roomId)?.code
    }))
  }
}

export function createEmptyBlock(this: CurriculumPlanContainerComponent, response: ICurriculumPlanNewBlockResponse, blockTypeId: number) {
  return {
    id: response.blockId,
    blockTypeId,
    blockName: response.blockName,
    blockCode: response.blockCode,
    bandIds: [this.selectedBandId],
    periodCount: 1,
    subjectToYearGroups: [
      {
        id: response.subjectToYearGroupId,
        yearGroupId: this.selectedYearGroup,
        subjectId: null,
        classCount: null,
        periodCount: 1,
        singlePeriodCount: 1,
        doublePeriodCount: 0
      }
    ],
    sessions: [
      {
        id: response.sessionId,
        subjectToYearGroupId: response.subjectToYearGroupId,
        staffIds: [],
        mainStaffId: null,
        additionalStaffIds: [],
        mainStaffCode: '',
        additionalStaffCodes: [],
        allMainStaffIds: [],
        allMainStaffCodes: [],
        allAdditionalStaffCodes: [],
        staffCodes: [],
        roomId: null,
        allRoomIds: [],
        allRoomCodes: [],
        isLocked: false,
        periodId: null,
        periodName: null,
        periodIndex: null,
        sessionName: null,
        className: blockTypeId === BLOCK_TYPE.Complex ? '1-C1' : 'C1',
        classIndex: 0,
        classRelatedSessionIds: [],
        joinedSessions: [],
        hasClassNameDefinition: false,
        isLunch: false
      }
    ]
  };
}
