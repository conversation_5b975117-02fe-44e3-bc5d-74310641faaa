[{"id": 185, "projectName": "2", "createdDate": "2023-03-02T16:41:54.6", "createdBy": "BromcomUser", "modifiedDate": "2023-03-02T16:41:54.6", "modifiedBy": "BromcomUser", "wizardStep": 2, "isWizardFinished": false, "importMISData": false, "importFromDate": null, "lastImportFinishedDate": null}, {"id": 58, "projectName": "3", "createdDate": "2023-03-02T16:42:48.353", "createdBy": "BromcomUser", "modifiedDate": "2023-03-02T16:42:48.353", "modifiedBy": "BromcomUser", "wizardStep": 3, "isWizardFinished": false, "importMISData": false, "importFromDate": null, "lastImportFinishedDate": null}, {"id": 59, "projectName": "4", "createdDate": "2023-03-02T16:46:12.003", "createdBy": "BromcomUser", "modifiedDate": "2023-03-02T16:46:12.003", "modifiedBy": "BromcomUser", "wizardStep": 4, "isWizardFinished": false, "importMISData": false, "importFromDate": null, "lastImportFinishedDate": null}, {"id": 60, "projectName": "5", "createdDate": "2023-03-02T16:50:25.977", "createdBy": "BromcomUser", "modifiedDate": "2023-03-02T16:50:25.977", "modifiedBy": "BromcomUser", "wizardStep": 5, "isWizardFinished": false, "importMISData": false, "importFromDate": null, "lastImportFinishedDate": null}, {"id": 61, "projectName": "6", "createdDate": "2023-03-02T16:53:57.463", "createdBy": "BromcomUser", "modifiedDate": "2023-03-02T16:54:30.207", "modifiedBy": "BromcomUser", "wizardStep": 6, "isWizardFinished": false, "importMISData": false, "importFromDate": null, "lastImportFinishedDate": null}, {"id": 62, "projectName": "6 and finished", "createdDate": "2023-03-02T16:56:09.077", "createdBy": "BromcomUser", "modifiedDate": "2023-03-02T16:56:09.077", "modifiedBy": "BromcomUser", "wizardStep": 6, "isWizardFinished": true, "importMISData": false, "importFromDate": null, "lastImportFinishedDate": null}, {"id": 63, "projectName": "wizardStep null", "createdDate": "2023-03-02T16:57:04.483", "createdBy": "BromcomUser", "modifiedDate": "2023-03-02T16:57:04.483", "modifiedBy": "BromcomUser", "wizardStep": 1, "isWizardFinished": false, "importMISData": true, "importFromDate": "2023-03-02T16:56:09.077", "lastImportFinishedDate": null}, {"id": 64, "projectName": "test whole procedure", "createdDate": "2023-03-07T16:57:04.483", "createdBy": "BromcomUser", "modifiedDate": "2023-03-07T16:57:04.483", "modifiedBy": "BromcomUser", "wizardStep": 1, "isWizardFinished": false, "importMISData": true, "importFromDate": "2023-03-07T16:56:09.077", "lastImportFinishedDate": null}]