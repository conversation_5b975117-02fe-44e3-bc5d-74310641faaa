import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { DeleteConfirmationComponent } from '../../../common/delete-confirmation/delete-confirmation.component';
import { ICurriculumPlanBlock } from '../../../../../../../_shared/models/ICurriculumPlanBlock';
import { BLOCK_TYPE } from '../../../../../../../_shared/enums/BlockType';
import { ICurriculumSessions } from '../../../../../../../_shared/models/ICurriculumSessions';
import { BandService } from '../../../../../../services/band.service';
import { IBand } from '../../../../../../../_shared/models/IBand';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';
import { PlanningRelationshipsService } from '../../../../../../services/planning-relationships.service';
import { CurriculumPlanService } from '../../../../../../services/curriculum-plan.service';
import { equals, uniqWith } from 'rambda';
import { Subject, switchMap, takeUntil } from 'rxjs';
import { MatMenuTrigger } from '@angular/material/menu';
import { GeneralModalComponent } from '../../../../../../../_shared/components/general-modal/general-modal.component';
import { SnackbarService } from '@bromcom/ui';
import { TranslateService } from '@ngx-translate/core';
import { LinkBlocksService } from '../../../../../../services/link-blocks.service';

@Component({
  selector: 'bromcom-curriculum-plan-block-actions',
  templateUrl: './curriculum-plan-block-actions.component.html',
  styleUrls: ['./curriculum-plan-block-actions.component.scss']
})
export class CurriculumPlanBlockActionsComponent implements OnInit, OnDestroy {
  @ViewChild('blockDeleteConfirmationComponent') blockDeleteConfirmationComponent!: DeleteConfirmationComponent;
  @ViewChild('linkBlocksWarningModal') linkBlocksWarningModal!: GeneralModalComponent;
  @ViewChild(MatMenuTrigger) trigger!: MatMenuTrigger;

  @Input() block!: ICurriculumPlanBlock;
  @Input() showBasicActions = false;
  @Input() isSidePanel = false;
  @Input() complexBlockTransformedSessions: ICurriculumSessions[][][] = [];
  @Output() deleteClicked: EventEmitter<boolean> = new EventEmitter();
  @Output() transformClicked: EventEmitter<{ blockId: number, blockTypeToId: number }> = new EventEmitter();
  @Output() copyClicked: EventEmitter<{ blockId: number, targetBandId: number }> = new EventEmitter();
  @Output() unScheduleClicked: EventEmitter<{ blockId: number, sessionIds: number[] }> = new EventEmitter();
  @Output() spreadToBandsClicked: EventEmitter<{ blockId: number, bandIds: number[] }> = new EventEmitter();
  @Output() splitToBandsClicked: EventEmitter<{ blockId: number }> = new EventEmitter();
  @Output() openEditClassCodesModalEvent: EventEmitter<any> = new EventEmitter();
  @Output() openLinkBlocksModalComponentEvent = new EventEmitter();

  @Input() openBlockActionsMenu$: Subject<number> = new Subject();

  bands: IBand[] = [];
  selectedYearName = '';
  spreadToBandsValidCombinations: IBand[][] = [];
  spreadToBandsOptions: string[] = [];
  BLOCK_TYPE = BLOCK_TYPE;
  isComplexWithOnlyOneRow = false;
  isComplexWithOnlyOneColumn = false;
  noSubjectAdded = false;
  hasScheduled = false;
  disableSimpleBlock = false;
  disableLinearBlock = false;
  disableOptionBlock = false;
  disableComplexBlock = false;
  isAllLocked = false;
  readonly unsubscribe$: Subject<void> = new Subject();

  constructor(
    private band: BandService,
    private curriculumPlan: CurriculumPlanService,
    private curriculumPlanBlocks: CurriculumPlanBlocksService,
    private planningRelationships: PlanningRelationshipsService,
    private linkBlocksService: LinkBlocksService,
    private snackbar: SnackbarService,
    private translate: TranslateService
  ) {
  }

  ngOnInit() {
    this.spreadToBandsOptions = [];
    this.openBlockActionsMenu$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((blockId) => {
        if (this.block.blockTypeId === BLOCK_TYPE.Complex) {
          const transformedData: ICurriculumSessions[][][] = [];

          this.block.sessions.forEach(session => {
            const className = session.className;
            const splitIndex = className.indexOf('-');
            const parentIndex = parseInt(className.substring(0, splitIndex)) - 1;
            const childIndex = parseInt(className.substring(splitIndex + 2)) - 1;
            if (!transformedData[parentIndex]) {
              transformedData[parentIndex] = [];
            }

            if (!transformedData[parentIndex][childIndex]) {
              transformedData[parentIndex][childIndex] = [];
            }
            transformedData[parentIndex][childIndex].push(session);
          });
          this.complexBlockTransformedSessions = transformedData.map(rowArray => rowArray.filter(element => element));
        }

        this.isComplexWithOnlyOneRow = this.block.blockTypeId === BLOCK_TYPE.Complex &&
          this.block.subjectToYearGroups.length > 2 &&
          this.complexBlockTransformedSessions.length === 1;

        this.isComplexWithOnlyOneColumn = this.block.blockTypeId === BLOCK_TYPE.Complex &&
          this.block.subjectToYearGroups.length > 2 &&
          this.complexBlockTransformedSessions.every(sessions => sessions[0].length === 1)

        this.noSubjectAdded = !this.block.subjectToYearGroups[this.block.blockTypeId === BLOCK_TYPE.Complex ? 1 : 0]?.subjectId;
        this.hasScheduled = this.block.sessions.some(session => session.periodId);

        this.disableSimpleBlock = this.block.blockTypeId === BLOCK_TYPE.Simple ||
          (this.block.blockTypeId !== BLOCK_TYPE.Complex && this.block.subjectToYearGroups.length > 1) ||
          (this.block.blockTypeId === BLOCK_TYPE.Complex && this.block.subjectToYearGroups.length > 1 && this.block.sessions.some(session => session.subjectToYearGroupId === this.block.subjectToYearGroups[0].id)) ||
          (this.block.blockTypeId === BLOCK_TYPE.Complex && this.block.subjectToYearGroups.length > 2 && (!this.isComplexWithOnlyOneRow || !this.isComplexWithOnlyOneColumn));

        const enableLinearBlock =
          this.block.blockTypeId !== BLOCK_TYPE.Linear &&
          this.block.subjectToYearGroups.length === 1 || (this.block.blockTypeId === BLOCK_TYPE.Complex && this.block.subjectToYearGroups.length === 2) ||
          this.block.blockTypeId === BLOCK_TYPE.Complex && this.isComplexWithOnlyOneRow ||
          this.block.blockTypeId === BLOCK_TYPE.Complex && !this.isComplexWithOnlyOneRow && this.checkSessionNameEqualityForOptionBlock(this.complexBlockTransformedSessions) ||
          this.block.blockTypeId === BLOCK_TYPE.Complex && !this.isComplexWithOnlyOneColumn && !this.checkSessionNameEqualityForLinearBlock(this.complexBlockTransformedSessions);

        this.disableLinearBlock = !enableLinearBlock ||
          this.block.blockTypeId === BLOCK_TYPE.Complex && this.block.subjectToYearGroups.length > 2 && !this.isComplexWithOnlyOneRow && !this.isComplexWithOnlyOneColumn && !this.checkSessionNameEqualityForOptionBlock(this.complexBlockTransformedSessions);

        this.disableOptionBlock = this.block.blockTypeId === BLOCK_TYPE.Options ||
          (this.block.blockTypeId !== BLOCK_TYPE.Complex && this.block.subjectToYearGroups.length > 1) ||
          (this.block.blockTypeId === BLOCK_TYPE.Complex && this.block.subjectToYearGroups.length > 2 && !this.isComplexWithOnlyOneRow && !this.isComplexWithOnlyOneColumn && this.checkSessionNameEqualityForOptionBlock(this.complexBlockTransformedSessions)) ||
          this.isComplexWithOnlyOneRow || !this.checkSessionNameEqualityForLinearBlock(this.complexBlockTransformedSessions);

        this.disableComplexBlock = this.block.blockTypeId === BLOCK_TYPE.Complex;
        this.selectedYearName = this.planningRelationships.yearGroupsData$.getValue()
          .find(yearGroup => yearGroup.id === this.curriculumPlan.selectedYearGroup$.getValue())?.description ?? ''

        this.isAllLocked = !this.block.sessions
          .find((session: ICurriculumSessions) => !session.isLocked && !!session.periodId);

        if (this.block.id === blockId) {
          this.trigger.openMenu();
        }
      })

    this.bands = this.band.fetchedBands$.getValue()?.map(band => ({
      ...band,
      bandName: this.selectedYearName + band.bandName,
      shortName: band.bandName
    })) ?? []
    this.spreadToBandsOptions = [];
    this.curriculumPlan.selectedBand$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(bandId => {
        const selectedBand = this.bands.find(band => band.id === bandId)
        if (selectedBand) {
          const allBands = [...this.bands];
          this.spreadToBandsValidCombinations = uniqWith(equals, this.getValidBandCombinations(selectedBand, allBands));
          this.spreadToBandsValidCombinations = this.spreadToBandsValidCombinations.filter(combination => combination.length);
          this.spreadToBandsValidCombinations.sort((a, b) => a.length - b.length)
          this.spreadToBandsOptions = this.spreadToBandsValidCombinations.map(option => {
            option.sort((bandA, bandB) => bandA.order - bandB.order);
            return option.map(band => band.shortName).join(', ')
          })
        }
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  getValidBandCombinations(selectedBand: IBand, allBands: IBand[]): IBand[][] {
    const result: IBand[][] = [];

    result.push(...this.generateOptionsBothWay(allBands.indexOf(selectedBand), allBands));

    result.push(...this.generateOptionsNearEachOther(allBands.indexOf(selectedBand), allBands));
    allBands.reverse();
    result.push(...this.generateOptionsNearEachOther(allBands.indexOf(selectedBand), allBands));
    return result
  }

  generateOptionsNearEachOther(selectedBandIndex: number, allBands: IBand[]): IBand[][] {
    const result: IBand[][] = [];

    // Collect all options which has connection until the selected band.
    // We have 4 bands (A, B, C, D) and the selected block is in band C. Then it gives the following options:
    // ['B', 'A, B'] or in reversed array ['D']
    for (let i = selectedBandIndex; i > selectedBandIndex - 1; i = i - 1) {
      const array = Array(i).fill(null)
      array.forEach((_, index) => {
        const collectElements = []
        for (let j = selectedBandIndex - 1; j >= index; j = j - 1) {
          if (!this.block.bandIds.includes(allBands[j]?.id)) {
            collectElements.push(allBands[j]);
          }
        }
        result.push(collectElements);
      })
    }

    return result
  }

  generateOptionsBothWay(selectedBandIndex: number, allBands: IBand[]): IBand[][] {
    const result: IBand[][] = [];

    // How many elements to reach the furthest end of band list
    const differenceToEnd = allBands.length - selectedBandIndex - 1;
    const longestWay = differenceToEnd > selectedBandIndex ? differenceToEnd : selectedBandIndex;
    const arrayToIterate = Array(longestWay).fill(null)

    // Start iterate to get the index for both direction
    arrayToIterate.forEach((_, index) => {
      const bandNames = []

      // Iterate to add all band names if e.g. longestWay is more than 1.
      // We have 4 bands (A, B, C, D) and the selected block is in band B. Then it gives the following options:
      // ['A, C', 'C, D', 'A , C, D']
      for (let i = 0; i <= index; i++) {
        const leftBand = allBands[selectedBandIndex - (i + 1)];
        const rightBand = allBands[selectedBandIndex + (i + 1)];

        if (!this.block.bandIds.includes(leftBand?.id)) {
          bandNames.push(leftBand);
        }

        if (!this.block.bandIds.includes(rightBand?.id)) {
          bandNames.push(rightBand);
        }
      }
      const clearedBands = bandNames.filter(item => item !== undefined && item !== null)
      result.push(clearedBands);
    })

    return result
  }

  expandBlock() {
    this.curriculumPlan.isExpanded$.next({ isExpanded: true, sessionId: null });
    this.curriculumPlan.currentSelectedBlock$.next(this.block);
    this.curriculumPlanBlocks.expandedSelectedSubjectIndex$.next(0);
  }

  onCopyClicked(targetBandId: number): void {
    this.copyClicked.emit({
      blockId: this.block.id,
      targetBandId
    });
  }

  onTransformClicked(blockTypeToId: number): void {
    this.transformClicked.emit({
      blockId: this.block.id,
      blockTypeToId
    });
  }

  onSpreadToBandsClicked(optionIndex: number): void {
    this.spreadToBandsClicked.emit({
      blockId: this.block.id,
      bandIds: this.spreadToBandsValidCombinations[optionIndex].map(band => band.id)
    });
  }

  onSplitToBands(): void {
    this.splitToBandsClicked.emit({
      blockId: this.block.id
    });
  }

  showDeleteBlock(): void {
    this.blockDeleteConfirmationComponent.show();
  }

  onDeleteClicked(isDelete: boolean): void {
    if (!isDelete) {
      return;
    }

    this.deleteClicked.emit(true);
  }

  onUnScheduleClicked(): void {
    const sessionIds = this.block.sessions
      .filter((session: ICurriculumSessions) => !session.isLocked)
      .map((session: ICurriculumSessions) => session.id);

    this.unScheduleClicked.emit({
      blockId: this.block.id,
      sessionIds
    });
  }

  private checkSessionNameEqualityForOptionBlock(array: ICurriculumSessions[][][]) {
    const groupedArray: ICurriculumSessions[][] = [];

    array.forEach(subArray1 => {
      subArray1.forEach(subArray2 => {
        subArray2.forEach((element, elementIndex) => {
          if (groupedArray[elementIndex]) {
            groupedArray[elementIndex].push(element)
          } else {
            groupedArray[elementIndex] = [element]
          }
        });
      });
    });

    return groupedArray.every(column => column.every(session => session.sessionName === column[0].sessionName))
  }

  private checkSessionNameEqualityForLinearBlock(array: ICurriculumSessions[][][]) {
    return array.flat(1).every(column => column.every(session => session.sessionName === column[0].sessionName))
  }

  openEditClassCodesModal() {
    this.openEditClassCodesModalEvent.emit({
      block: this.block,
      blockId: this.block.id
    });
  }

  onLinkBlocks(): void {
    this.hasScheduled
      ? this.linkBlocksWarningModal.show()
      : this.openLinkBlocksModalComponentEvent.emit(this.block.id);
  }

  onOpenLinkBlocks(): void {
    const data = {
      blockId: this.block.id,
      sessionIds: this.block.sessions
        .filter((session: ICurriculumSessions) => !session.isLocked)
        .map((session: ICurriculumSessions) => session.id)
    }

    this.curriculumPlanBlocks.unscheduleSessions(data)
      .pipe(switchMap(() => this.curriculumPlan.getBlock(this.block.id)))
      .subscribe((block) => {
        this.snackbar.success(this.translate.instant('Sessions were unscheduled successfully.'));
        this.curriculumPlanBlocks.stateChanged$.next();
        this.curriculumPlanBlocks.blockSideStateChanged$.next();
        this.curriculumPlan.currentSelectedBlock$.next(block);
        this.openLinkBlocksModalComponentEvent.emit(this.block.id);
      });
  }

  onUnlinkBlocks(): void {
    this.linkBlocksService.unLinkBlocks(this.block.id)
      .pipe(switchMap(() => this.curriculumPlan.getBlock(this.block.id)))
      .subscribe((block) => {
        this.snackbar.success(this.translate.instant('Block was unlinked successfully.'));
        this.curriculumPlanBlocks.stateChanged$.next();
        this.curriculumPlanBlocks.blockSideStateChanged$.next();
        this.curriculumPlan.currentSelectedBlock$.next(block);
      })
  }
}
