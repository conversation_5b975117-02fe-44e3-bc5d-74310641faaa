@import 'apps/bromcom-timetables/src/assets/styles/variables';
@import 'apps/bromcom-timetables/src/app/planning-relationships/pages/planning-relationships/components/scheduling/components/timetable-specification/styles/tts-ag-grid';

.filter-popup {
  padding: 12px;
  width: calc(100% + 19px);
  background-color: white;
  border: 1px solid lightgray;
  margin-top: -383px;

  .filter-header {
    display: flex;
    height: 25px;
    justify-content: center;
    align-items: center;
    border-bottom: 2px solid $color-primary-blue-600;
    margin-bottom: 8px;

    .filter-icon {
      margin-bottom: 6px;

      i {
        font-size: 14px;
        color: $color-blue-500;
      }
    }
  }

  ::ng-deep .mdc-checkbox__ripple, ::ng-deep .mat-mdc-checkbox-ripple {
    display: none;
  }
  
  ::ng-deep .mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background, .mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background, .mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled ~ .mdc-checkbox__background {
    background-color: $color-primary-blue-600 !important;
    border-color: $color-primary-blue-600 !important;
  }
  

  ul {
    padding-left: 0;
    list-style: none;
  }

  .staff-selector-modal-container{
    height: 230px;
    min-height: 230px;

  }

  .header-menu {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid $color-blue-grey-900;
  }

  .line {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
  }
  
  .expand-icons {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
  }
  
  .tree-invisible {
    display: none;
  }
  
  .tree ul,
  .tree li {
    margin-top: 0;
    margin-bottom: 0;
    list-style-type: none;
  }
  
  .tree div[role=group] > .mat-tree-node {
    padding-left: 40px;
  }

  .search {
    margin-bottom: 3px;
  }

  .filter-options {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    align-self: stretch;
    font-size: 14px;
    color: $color-primary-blue-600;
    border-bottom: 1px solid $color-blue-200;

    span {
      margin-bottom: 8px;
      cursor: pointer;
    }
  }

  .vertical {
    margin-top: 4px;
    max-height: 238px;
    overflow-y: auto;
    gap: 2px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }

  .filter-buttons {
    display: flex;
    height: 32px;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    margin-top: 16px;

    .button {
      width: auto !important;
    }
  }

  .data-item {
    display: flex;
  }
}

::ng-deep .ag-popup-child {
  box-shadow: none !important;
}

::ng-deep .ag-popup-editor {
  border: none !important;
}

::ng-deep .mat-expansion-panel-content {
  z-index: 100000;
  position: fixed;
}

::ng-deep .mat-expansion-panel {
  box-shadow: none !important;
  border: 1px solid $color-blue-grey-300;
  color: $color-blue-grey-600 !important;
}

::ng-deep .mat-expansion-panel:hover {
  border: 1px solid $color-blue-500;
}

::ng-deep .mat-expansion-panel-header,
::ng-deep .mat-expansion-panel-header.mat-expanded {
  height: 30px !important;
  transition: none !important;
  padding: 0 0 0 12px !important;

  .mat-content.mat-content-hide-toggle {
    margin: 0 !important;
  }

  .list-items {
    padding-right: 8px;
    max-width: 187px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    ::ng-deep .chip {
      margin-right: 8px;

      ::ng-deep .bcm-chip__size-medium {
        font-size: 12px !important;
      }
    }
  }
}

::ng-deep .mat-expansion-panel-header-title {
  color: $color-grey-300 !important;
  font-family: "Inter", sans-serif;
  flex-basis: unset !important;
}

::ng-deep .mat-expansion-panel-header:hover {
  background-color: transparent !important;
}

::ng-deep .mat-expansion-panel-body {
  margin-top: 1px;
  padding: 0 !important;
}

::ng-deep .mat-expansion-panel-header-description {
  justify-content: flex-end;
  margin: 0;

  .icon {
    font-size: 16px;
    transform: rotate(360deg);
    transition: transform 0.2s ease-in-out;
  }

  .icon.is-open {
    transform: rotate(180deg);
  }
}

::ng-deep .mat-expansion-panel .mat-expansion-panel-header.cdk-program-focused:not([aria-disabled=true]) {
  background-color: $color-white-0;
}

::ng-deep .mdc-tooltip__surface {
  max-height: 500px !important;
  overflow-y: auto !important;
}

mat-tree-node.hidden {
  min-height: 0 !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden;
  visibility: hidden;
  display: none;
}
