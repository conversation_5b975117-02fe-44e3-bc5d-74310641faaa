<mat-accordion [displayMode]="'flat'">
    <mat-expansion-panel (opened)="panelOpenState = true"
                         (closed)="panelOpenState = false"
                         [expanded]="panelOpenState"
                         [class.new-row]="params.data.id === 'newRowId'"
                         hideToggle>
      <mat-expansion-panel-header [expandedHeight]="'48px'">
        <mat-panel-title>
          <div *ngIf="!selectedItemTexts.length" class="title-text">{{placeholder}}</div>
  
          <div class="list-items"
               *ngIf="selectedItemTexts.length"
               #tooltip="matTooltip"
               [matTooltipDisabled]="!selectedItemTexts.length"
               [matTooltip]="selectedItemTexts.join('\n')"
               [matTooltipPosition]="'left'">

            <bcm-chip *ngFor="let text of selectedItemTexts" color="{{color}}" class="chip">
              {{text}}
              
              <bcm-icon class="icon" icon="fa fa-times" (click)="uncheckItem(text)"></bcm-icon>
            </bcm-chip>
          </div>
        </mat-panel-title>
        <mat-panel-description>
          <bcm-icon class="icon" [class.is-open]="panelOpenState === true" icon="far fa-angle-down"></bcm-icon>
        </mat-panel-description>
      </mat-expansion-panel-header>
  
      <div *ngIf="panelOpenState === true" class="block-vertical filter-popup" id="selectItems" #container>
        <div class="search">
          <bromcom-input-field [formControl]="searchControl"
                               [icon]="'fal fa-search'"
                               [iconPosition]="'prefix'"
                               [placeholder]="'Search' | translate"
          ></bromcom-input-field>
        </div>
        
        <div class="header-menu">

            <mat-checkbox disableRipple (change)="selectAll()" [formControl]="selectAllSubjectsForm">
              {{ 'Select All' | translate }}
            </mat-checkbox>
            <bcm-button kind="link" (click)="clearAll()">
              {{ 'Clear All' | translate }}
            </bcm-button>
        </div>

        <div class="vertical staff-selector-modal-container" [formGroup]="subjectsForm">
            <ul formArrayName="subjectList">
              <ng-container>
                <li>
                  <mat-tree [dataSource]="dataSource" [treeControl]="treeControl" class="tree">
                    <mat-nested-tree-node *matTreeNodeDef="let node; when: hasChild" matTreeNodePadding>
                      <div class="mat-tree-node" *ngIf="!node.isHide">
                        <div matTreeNodeToggle [attr.aria-label]="'Toggle ' + node.name">
                          <div class="expand-icons">
                            <bcm-icon class="icon" *ngIf="!treeControl.isExpanded(node)"
                              icon="far fa-chevron-right"></bcm-icon>
                            <bcm-icon class="icon" *ngIf="treeControl.isExpanded(node)"
                              icon="far fa-chevron-down"></bcm-icon>
                          </div>
                        </div>
                        <mat-checkbox *ngIf="node.isDepartment && node.isSelected !== undefined" [formControl]="node.isSelected"
                          (change)="toggleDepartmentSelection(node)"></mat-checkbox>
                        {{ node.name }}
                      </div>
                      <div [class.tree-invisible]="!treeControl.isExpanded(node)" role="group">
                        <ng-container matTreeNodeOutlet></ng-container>
                      </div>
                    </mat-nested-tree-node>

                    

                    <mat-tree-node *matTreeNodeDef="let subjectNode" matTreeNodePadding [class.hidden]="subjectNode.isHide" >
                      <div class="line" *ngIf="!subjectNode.isHide">

                        <mat-checkbox *ngIf="!subjectNode.isDepartment"
                          (change)="toggleSubject(subjectNode)"
                          [formControl]="subjectNode.isSelected">
                        </mat-checkbox>
                        {{ subjectNode.name }}
                      </div>

                    </mat-tree-node>

                  </mat-tree>
                </li>
              </ng-container>
            </ul>
          </div>
      </div>
    </mat-expansion-panel>
  </mat-accordion>
