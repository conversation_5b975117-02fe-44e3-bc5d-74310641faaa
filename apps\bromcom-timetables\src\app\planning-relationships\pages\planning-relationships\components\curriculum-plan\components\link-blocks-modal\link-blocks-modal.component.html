<ng-container *ngIf="isOpen">
  <bcm-modal size="xxlarge" class="link-blocks-modal" backdrop (bcm-modal-before-close)="onClose()"
             #linkBlocksModal>
    <bcm-modal-header>
      <div *ngIf="step === 1">{{ 'Link Blocks' | translate }}</div>
      <div *ngIf="step === 2">{{ 'Match Classes' | translate }}</div>
    </bcm-modal-header>

    <div class="modal-body-step-one" *ngIf="step === 1">
      <div class="actions">
        <bromcom-input-field class="search-field"
                             [formControl]="searchControl"
                             [icon]="'fal fa-search'"
                             [placeholder]="'Search' | translate "
        ></bromcom-input-field>
      </div>

      <div>
        <div class="table-header"></div>

        <div class="table-center" *ngIf="!dataSource.data.length && searchControl.value">
          <bcm-empty class="no-data"
                     icon="fad fa-folder-open">{{'Sorry no results found. Please amend your search criteria.' | translate }}</bcm-empty>
        </div>

        <div class="table-center" *ngIf="dataSource.data.length">
          <mat-tree [dataSource]="dataSource" [treeControl]="treeControl" class="tree">
            <mat-tree-node *matTreeNodeDef="let node"
                           matTreeNodeToggle
                           [class.disabled]="node.isDisabled"
                           [class.selected]="node.blockId === selectedBlockId"
                           (click)="selectedBlockToLink(node)">
              {{node.name}}
            </mat-tree-node>

            <mat-nested-tree-node *matTreeNodeDef="let node; when: hasChild">
              <div class="mat-tree-node">
                <div matTreeNodeToggle
                     [attr.aria-label]="'Toggle ' + node.name">

                  <div class="expand-icons">
                    <bcm-icon class="icon"
                              *ngIf="!treeControl.isExpanded(node)"
                              icon="far fa-chevron-right">
                    </bcm-icon>

                    <bcm-icon class="icon"
                              *ngIf="treeControl.isExpanded(node)"
                              icon="far fa-chevron-up">
                    </bcm-icon>
                  </div>
                </div>
                {{node.name}}
              </div>

              <div [class.tree-invisible]="!treeControl.isExpanded(node)" role="group">
                <ng-container matTreeNodeOutlet></ng-container>
              </div>
            </mat-nested-tree-node>
          </mat-tree>
        </div>

        <div class="table-footer"></div>
      </div>
    </div>

    <div class="modal-body-step-two" *ngIf="step === 2">
      <div class="information-box">
        <div class="information-icon">
          <bcm-icon icon="far fa-info-circle"></bcm-icon>
        </div>

        {{'Any further alteration to the selected blocks will cause the link to break. Please make sure your blocks are constructed correctly.' | translate}}
      </div>

      <div class="block-names">
        <bromcom-input-field class="block-name" [formControl]="blockFromNameControl"></bromcom-input-field>
        <bcm-icon class="icon" icon="far fa-link"></bcm-icon>
        <bromcom-input-field class="block-name" [formControl]="blockToNameControl"></bromcom-input-field>
      </div>

      <div class="drag-to-match">
        <div class="left">{{'Drag to match periods.' | translate}}</div>
        <div class="right">{{'Drag excluded periods here:' | translate}}</div>
      </div>

      <div class="drag-containers">
        <div class="drag-area-container">
          <div class="drag-area">
            <div class="container"
                 *ngIf="linkBlocksInfo?.sourceBlock?.periods"
                 cdkDropList
                 cdkDropListOrientation="horizontal"
                 [cdkDropListData]="linkBlocksInfo.sourceBlock.periods"
                 (cdkDropListDropped)="drop($event, linkBlocksInfo.sourceBlock.periods)">

              <div *ngFor="let period of linkBlocksInfo.sourceBlock.periods, let i = index" class="period"
                   [class.isSingle]="period.joinedPeriods.length === 1"
                   [class.isDouble]="period.joinedPeriods.length === 2"
                   cdkDrag>
                <div *ngIf="period.joinedPeriods.length === 1 else doublePeriod" class="single">
                  <div class="row period-name">{{"Period"}} {{period.periodIndex}}</div>
                  <div *ngFor="let subject of period.subjects" class="subject">
                    <div class="row"
                         [style]="{backgroundColor: '#' + subject.color}"
                         [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                      {{subject.subjectCode}}</div>
                    <div *ngFor="let session of subject.sessions">
                      <div class="row border-top"
                           [matTooltipDisabled]="session.sessionName!.length < 12"
                           [matTooltip]="session.sessionName!"
                           [matTooltipPosition]="'above'">{{session.sessionName}}</div>
                    </div>
                  </div>
                </div>

                <ng-template #doublePeriod>
                  <div class="double" *ngIf="period.joinedPeriods[0] === period.periodIndex">
                    <div
                      [class.isDoubleLeft]="period.joinedPeriods.length === 2 && period.joinedPeriods[0] === period.periodIndex">
                      <div class="row period-name">{{"Period"}} {{period.periodIndex}}</div>
                      <div *ngFor="let subject of period.subjects" class="subject">
                        <div class="row"
                             [style]="{backgroundColor: '#' + subject.color}"
                             [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                          {{subject.subjectCode}}</div>
                        <div *ngFor="let session of subject.sessions">
                          <div class="row border-top"
                               [matTooltipDisabled]="session.sessionName!.length < 12"
                               [matTooltip]="session.sessionName!"
                               [matTooltipPosition]="'above'">{{session.sessionName}}</div>
                        </div>
                      </div>
                    </div>

                    <div class="isDoubleRight">
                      <div class="row period-name">{{"Period"}} {{period.periodIndex + 1}}</div>
                      <div class="subject"
                           *ngFor="let subject of (linkBlocksInfo.sourceBlock.periods | getSubjectsForPeriodIndex:period.periodIndex + 1)">
                        <div class="row"
                             [style]="{backgroundColor: '#' + subject.color}"
                             [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                          {{subject.subjectCode}}</div>
                        <div *ngFor="let session of subject.sessions">
                          <div class="row border-top"
                               [matTooltipDisabled]="session.sessionName!.length < 12"
                               [matTooltip]="session.sessionName!"
                               [matTooltipPosition]="'above'">{{session.sessionName}}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-template>
              </div>

              <div *cdkDragPreview>
                <div *ngFor="let period of linkBlocksInfo.sourceBlock.periods" class="period">
                  <div *ngIf="period.joinedPeriods.length === 1 else doublePeriod" class="single">
                    <div class="row period-name">{{"Period"}} {{period.periodIndex}}</div>
                    <div *ngFor="let subject of period.subjects" class="subject">
                      <div class="row"
                           [style]="{backgroundColor: '#' + subject.color}"
                           [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                        {{subject.subjectCode}}</div>
                      <div *ngFor="let session of subject.sessions">
                        <div class="row border-top">{{session.sessionName}}</div>
                      </div>
                    </div>
                  </div>

                  <ng-template #doublePeriod>
                    <div class="double" *ngIf="period.joinedPeriods[0] === period.periodIndex">
                      <div
                        [class.isDoubleLeft]="period.joinedPeriods.length === 2 && period.joinedPeriods[0] === period.periodIndex">
                        <div class="row period-name">{{"Period"}} {{period.periodIndex}}</div>
                        <div *ngFor="let subject of period.subjects" class="subject">
                          <div class="row"
                               [style]="{backgroundColor: '#' + subject.color}"
                               [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                            {{subject.subjectCode}}</div>
                          <div *ngFor="let session of subject.sessions">
                            <div class="row border-top">{{session.sessionName}}</div>
                          </div>
                        </div>
                      </div>

                      <div class="isDoubleRight">
                        <div class="row period-name">{{"Period"}} {{period.periodIndex + 1}}</div>
                        <div class="subject"
                             *ngFor="let subject of (linkBlocksInfo.sourceBlock.periods | getSubjectsForPeriodIndex:period.periodIndex + 1)">
                          <div class="row"
                               [style]="{backgroundColor: '#' + subject.color}"
                               [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                            {{subject.subjectCode}}</div>
                          <div *ngFor="let session of subject.sessions">
                            <div class="row border-top">{{session.sessionName}}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </div>
              </div>
            </div>

            <div class="check-container">
              <div *ngFor="let periodControl of matchingPeriodCountArray.controls" class="check-box">
                <bcm-icon *ngIf="periodControl.getRawValue() === true" class="icon check"
                          icon="far fa-check"></bcm-icon>
                <bcm-icon *ngIf="periodControl.getRawValue() === false" class="icon times"
                          icon="far fa-times"></bcm-icon>
              </div>
            </div>

            <div class="container"
                 *ngIf="linkBlocksInfo?.destinationBlock?.periods"
                 cdkDropList
                 cdkDropListOrientation="horizontal"
                 [ngStyle]="{width: linkBlocksInfo.sourceBlock.periods.length * 112 + 'px'}"
                 [id]="'destinationBlocks'"
                 [cdkDropListConnectedTo]="['excludeContainer']"
                 [cdkDropListData]="linkBlocksInfo.destinationBlock.periods"
                 (cdkDropListDropped)="drop($event, linkBlocksInfo.destinationBlock.periods)">
              <div *ngFor="let period of linkBlocksInfo.destinationBlock.periods, let i = index" class="period"
                   [class.isSingle]="period.joinedPeriods.length === 1"
                   [class.isDouble]="period.joinedPeriods.length === 2"
                   cdkDrag>
                <div *ngIf="period.joinedPeriods.length === 1 else doublePeriod" class="single">
                  <div class="row period-name">{{"Period"}} {{period.periodIndex}}</div>
                  <div *ngFor="let subject of period.subjects" class="subject">
                    <div class="row"
                         [style]="{backgroundColor: '#' + subject.color}"
                         [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                      {{subject.subjectCode}}</div>
                    <div *ngFor="let session of subject.sessions">
                      <div class="row border-top"
                           [matTooltipDisabled]="session.sessionName && session.sessionName.length < 12"
                           [matTooltip]="session.sessionName"
                           [matTooltipPosition]="'above'">{{session.sessionName}}</div>
                    </div>
                  </div>
                </div>

                <ng-template #doublePeriod>
                  <div class="double" *ngIf="period.joinedPeriods[0] === period.periodIndex">
                    <div
                      [class.isDoubleLeft]="period.joinedPeriods.length === 2 && period.joinedPeriods[0] === period.periodIndex">
                      <div class="row period-name">{{"Period"}} {{period.periodIndex}}</div>
                      <div *ngFor="let subject of period.subjects" class="subject">
                        <div class="row"
                             [style]="{backgroundColor: '#' + subject.color}"
                             [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                          {{subject.subjectCode}}</div>
                        <div *ngFor="let session of subject.sessions">
                          <div class="row border-top"
                               [matTooltipDisabled]="session.sessionName!.length < 12"
                               [matTooltip]="session.sessionName!"
                               [matTooltipPosition]="'above'">{{session.sessionName}}</div>
                        </div>
                      </div>
                    </div>

                    <div class="isDoubleRight">
                      <div class="row period-name">{{"Period"}} {{period.periodIndex + 1}}</div>
                      <div class="subject"
                           *ngFor="let subject of (linkBlocksInfo.destinationBlock.periods | getSubjectsForPeriodIndex:period.periodIndex + 1)">
                        <div class="row"
                             [style]="{backgroundColor: '#' + subject.color}"
                             [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                          {{subject.subjectCode}}</div>
                        <div *ngFor="let session of subject.sessions">
                          <div class="row border-top"
                               [matTooltipDisabled]="session.sessionName!.length < 12"
                               [matTooltip]="session.sessionName!"
                               [matTooltipPosition]="'above'">{{session.sessionName}}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-template>
              </div>

              <div *cdkDragPreview>
                <div *ngFor="let period of linkBlocksInfo.destinationBlock.periods" class="period">
                  <div *ngIf="period.joinedPeriods.length === 1 else doublePeriod" class="single">
                    <div class="row period-name">{{"Period"}} {{period.periodIndex}}</div>
                    <div *ngFor="let subject of period.subjects" class="subject">
                      <div class="row"
                           [style]="{backgroundColor: '#' + subject.color}"
                           [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                        {{subject.subjectCode}}</div>
                      <div *ngFor="let session of subject.sessions">
                        <div class="row border-top">{{session.sessionName}}</div>
                      </div>
                    </div>
                  </div>

                  <ng-template #doublePeriod>
                    <div class="double" *ngIf="period.joinedPeriods[0] === period.periodIndex">
                      <div
                        [class.isDoubleLeft]="period.joinedPeriods.length === 2 && period.joinedPeriods[0] === period.periodIndex">
                        <div class="row period-name">{{"Period"}} {{period.periodIndex}}</div>
                        <div *ngFor="let subject of period.subjects" class="subject">
                          <div class="row"
                               [style]="{backgroundColor: '#' + subject.color}"
                               [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                            {{subject.subjectCode}}</div>
                          <div *ngFor="let session of subject.sessions">
                            <div class="row border-top">{{session.sessionName}}</div>
                          </div>
                        </div>
                      </div>

                      <div class="isDoubleRight">
                        <div class="row period-name">{{"Period"}} {{period.periodIndex + 1}}</div>
                        <div class="subject"
                             *ngFor="let subject of (linkBlocksInfo.destinationBlock.periods | getSubjectsForPeriodIndex:period.periodIndex + 1)">
                          <div class="row"
                               [style]="{backgroundColor: '#' + subject.color}"
                               [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                            {{subject.subjectCode}}</div>
                          <div *ngFor="let session of subject.sessions">
                            <div class="row border-top">{{session.sessionName}}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="drag-area-exclude-container">
          <div class="drag-area">
            <div class="container"
                 cdkDropList
                 cdkDropListOrientation="horizontal"
                 [id]="'excludeContainer'"
                 [cdkDropListConnectedTo]="['destinationBlocks']"
                 [cdkDropListData]="excludedPeriods"
                 (cdkDropListDropped)="drop($event, excludedPeriods)">
              <div *ngFor="let period of excludedPeriods, let i = index" class="period"
                   [class.isSingle]="period.joinedPeriods.length === 1"
                   [class.isDouble]="period.joinedPeriods.length === 2"
                   cdkDrag>
                <div *ngIf="period.joinedPeriods.length === 1 else doublePeriod" class="single">
                  <div class="row period-name">{{"Period"}} {{period.periodIndex}}</div>
                  <div *ngFor="let subject of period.subjects" class="subject">
                    <div class="row"
                         [style]="{backgroundColor: '#' + subject.color}"
                         [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                      {{subject.subjectCode}}</div>
                    <div *ngFor="let session of subject.sessions">
                      <div class="row border-top">{{session.sessionName}}</div>
                    </div>
                  </div>
                </div>

                <ng-template #doublePeriod>
                  <div class="double" *ngIf="period.joinedPeriods[0] === period.periodIndex">
                    <div
                      [class.isDoubleLeft]="period.joinedPeriods.length === 2 && period.joinedPeriods[0] === period.periodIndex">
                      <div class="row period-name">{{"Period"}} {{period.periodIndex}}</div>
                      <div *ngFor="let subject of period.subjects" class="subject">
                        <div class="row"
                             [style]="{backgroundColor: '#' + subject.color}"
                             [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                          {{subject.subjectCode}}</div>
                        <div *ngFor="let session of subject.sessions">
                          <div class="row border-top">{{session.sessionName}}</div>
                        </div>
                      </div>
                    </div>

                    <div class="isDoubleRight">
                      <div class="row period-name">{{"Period"}} {{period.periodIndex + 1}}</div>
                      <div class="subject"
                           *ngFor="let subject of (excludedPeriods | getSubjectsForPeriodIndex:period.periodIndex + 1)">
                        <div class="row"
                             [style]="{backgroundColor: '#' + subject.color}"
                             [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                          {{subject.subjectCode}}</div>
                        <div *ngFor="let session of subject.sessions">
                          <div class="row border-top">{{session.sessionName}}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-template>
              </div>

              <div *cdkDragPreview>
                <div *ngFor="let period of excludedPeriods" class="period">
                  <div *ngIf="period.joinedPeriods.length === 1 else doublePeriod" class="single">
                    <div class="row period-name">{{"Period"}} {{period.periodIndex}}</div>
                    <div *ngFor="let subject of period.subjects" class="subject">
                      <div class="row"
                           [style]="{backgroundColor: '#' + subject.color}"
                           [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                        {{subject.subjectCode}}</div>
                      <div *ngFor="let session of subject.sessions">
                        <div class="row border-top">{{session.sessionName}}</div>
                      </div>
                    </div>
                  </div>

                  <ng-template #doublePeriod>
                    <div class="double" *ngIf="period.joinedPeriods[0] === period.periodIndex">
                      <div
                        [class.isDoubleLeft]="period.joinedPeriods.length === 2 && period.joinedPeriods[0] === period.periodIndex">
                        <div class="row period-name">{{"Period"}} {{period.periodIndex}}</div>
                        <div *ngFor="let subject of period.subjects" class="subject">
                          <div class="row"
                               [style]="{backgroundColor: '#' + subject.color}"
                               [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                            {{subject.subjectCode}}</div>
                          <div *ngFor="let session of subject.sessions">
                            <div class="row border-top">{{session.sessionName}}</div>
                          </div>
                        </div>
                      </div>

                      <div class="isDoubleRight">
                        <div class="row period-name">{{"Period"}} {{period.periodIndex + 1}}</div>
                        <div class="subject"
                             *ngFor="let subject of (excludedPeriods | getSubjectsForPeriodIndex:period.periodIndex + 1)">
                          <div class="row"
                               [style]="{backgroundColor: '#' + subject.color}"
                               [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}">
                            {{subject.subjectCode}}</div>
                          <div *ngFor="let session of subject.sessions">
                            <div class="row border-top">{{session.sessionName}}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <bcm-modal-footer>
      <bcm-button *ngIf="step === 1" data-dismiss kind="ghost">{{'Cancel' | translate}}</bcm-button>
      <bcm-button *ngIf="step === 2" kind="ghost" (click)="backToStepOne()">{{'Cancel' | translate}}</bcm-button>
      <bcm-button icon="far fa-link"
                  *ngIf="step === 1"
                  [disabled]="!selectedBlockId"
                  [color]="'blue'"
                  (click)="linkBlocks()">{{'Link Blocks' | translate}}</bcm-button>
      <bcm-button icon="far fa-link"
                  data-dismiss
                  *ngIf="step === 2"
                  [disabled]="disableLinkBlocks"
                  [color]="'blue'"
                  (click)="onCreateLinks()">{{'Create Link' | translate}}</bcm-button>
    </bcm-modal-footer>
  </bcm-modal>
</ng-container>

<bromcom-general-modal #linkBlockToWarningModal
                       icon="far fa-exclamation-triangle"
                       type="warningAmber"
                       [header]="'Do you want to continue?' | translate"
                       [cancelText]="'Cancel' | translate"
                       [nextText]="'Continue' | translate"
                       [description]="'This operation will unschedule the block.' | translate"
                       (nextEvent)="onOpenDragLinks()"
                       (cancelEvent)="backToStepOne()">
</bromcom-general-modal>

<bromcom-general-modal #conflictWillBeGeneratedWarningModal
                       icon="far fa-exclamation-triangle"
                       type="warningAmber"
                       [header]="'Do you want to continue?' | translate"
                       [cancelText]="'Cancel' | translate"
                       [nextText]="'Save' | translate"
                       [description]="'Creating this linking will cause assignment conflicts.' | translate"
                       (nextEvent)="createLinks()"
                       (cancelEvent)="backToStepTwo()">
</bromcom-general-modal>
