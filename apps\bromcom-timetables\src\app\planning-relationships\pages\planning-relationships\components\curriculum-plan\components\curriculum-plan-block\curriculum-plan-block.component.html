<div class="block"
     [class.active]="activeBlockId === this.block.id"
     [class.inactive]="activeBlockId !== this.block.id && activeBlockId"
     [id]="'block' + block.id"
     (click)="select()">
  <div class="header">
    <div class="circle">
      {{BLOCK_TYPE[block.blockTypeId]}}
    </div>

    <div class="actions">
      <bcm-icon (click)="openBlockActionsMenu()"
                class="icon"
                icon="far fa-ellipsis-h">
      </bcm-icon>
      <bromcom-curriculum-plan-block-actions [block]="block"
                                             [showBasicActions]="true"
                                             [isSidePanel]="true"
                                             (copyClicked)="copyBlock($event)"
                                             (deleteClicked)="deleteBlock(block.id)"
                                             (unScheduleClicked)="unScheduleSessions($event)"
                                             (spreadToBandsClicked)="spreadToBands($event)"
                                             (splitToBandsClicked)="splitToBands($event)"
                                             [openBlockActionsMenu$]="openBlockActionsMenu$"
                                             (openEditClassCodesModalEvent)="openEditClassCodesModal($event)"></bromcom-curriculum-plan-block-actions>
    </div>
  </div>

  <div class="block-code">
    {{block.blockCode}}
  </div>

  <div class="type">
    <div class="block-name">
      <bcm-tooltip *ngIf="block.blockName.length > 14; else simpleTextTemplate"
                   [message]="block.blockName"
                   trigger="hover">
        {{ block.blockName }}
      </bcm-tooltip>
      <ng-template #simpleTextTemplate>
        {{block.blockName}}
      </ng-template>
    </div>

    <div class="subjects">
      <bcm-tooltip [message]="tooltipContent"
                   trigger="hover">
        <ng-container *ngIf="(subjects$ | async); let list;">
          <div class="subject-circle"
               *ngFor="let subject of list | slice:0:5;"
               [ngStyle]="{'background-color': '#'+subject?.color}">
          </div>
          <div class="empty" *ngIf="list?.length === 0">
            --
          </div>
        </ng-container>
      </bcm-tooltip>
    </div>
  </div>

  <div class="band" [matTooltipDisabled]="bandNames.length < 14"
       [matTooltip]="bandNames"
       [matTooltipPosition]="'above'">
    {{bandNames}}
  </div>

  <div class="cycle">
    <span *ngIf="block.periodCount; else nullPeriod">
    {{block | getScheduledPeriodCountForSubject}}/{{block.periodCount}}
    </span>
    <ng-template #nullPeriod>
      --
    </ng-template>
  </div>
</div>
