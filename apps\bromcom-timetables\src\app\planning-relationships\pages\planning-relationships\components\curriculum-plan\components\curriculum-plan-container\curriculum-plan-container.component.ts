import {
  Compo<PERSON>,
  <PERSON>ement<PERSON>ef,
  EventE<PERSON>ter,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { RelationshipsService } from '../../../../../../services/relationships.service';
import { distinctUntilChanged, Subject, Subscription, takeUntil } from 'rxjs';
import { ISubject } from '../../../../../../../_shared/models/ISubject';
import { CurriculumPlanService } from '../../../../../../services/curriculum-plan.service';
import { BandSidePanelComponent } from '../band-side-panel/band-side-panel.component';
import { ICurriculumPlanBlock } from '../../../../../../../_shared/models/ICurriculumPlanBlock';
import { BLOCK_TYPE } from '../../../../../../../_shared/enums/BlockType';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';
import { SnackbarService } from '@bromcom/ui';
import { ICurriculumPlanAddSubjectToBlock } from '../../../../../../../_shared/models/ICurriculumPlanAddSubjectToBlock';
import { createEmptyBlock, formatBlock } from './curriculum-plan-container-formatter-helpers';
import { FormControl } from '@angular/forms';
import { ICurriculumPlanAddStaffToBlock } from '../../../../../../../_shared/models/ICurriculumPlanAddStaffToBlock';
import { ICurriculumPlanAddRoomToBlock } from '../../../../../../../_shared/models/ICurriculumPlanAddRoomToBlock';
import { BandService } from '../../../../../../services/band.service';
import { IBand } from '../../../../../../../_shared/models/IBand';
import { scrollToElement, updateLastVisibleIndexForBlocks } from '../../_shared/base-helper';
import { MatMenuPanel } from '@angular/material/menu';
import { PlanningRelationshipsService } from '../../../../../../services/planning-relationships.service';
import { IMenuItem, IMenuItemCommandEvent } from '../../../../../../../_shared/models/IMenuItem';
import { IFilterRequest } from 'apps/bromcom-timetables/src/app/_shared/models/IFilterRequest';

@Component({
  selector: 'bromcom-curriculum-plan-container',
  templateUrl: './curriculum-plan-container.component.html',
  styleUrls: ['./curriculum-plan-container.component.scss']
})
export class CurriculumPlanContainerComponent implements OnInit, OnDestroy {
  @ViewChild('bandSidePanel') bandSidePanel!: BandSidePanelComponent;
  @ViewChild('blocksContainer', { read: ElementRef }) blocksContainer!: ElementRef;

  @Input() classContextMenuRef!: MatMenuPanel;
  @Input() sessionMenuRef!: MatMenuPanel;
  @Input() subjectMenuRef!: MatMenuPanel;
  @Input() isSubjectDraggingActive = false;

  @Output() sessionMenuOpenedEvent = new EventEmitter<any>();
  @Output() subjectMenuOpenedEvent = new EventEmitter<any>();
  @Output() periodMenuOpenedEvent = new EventEmitter<any>();
  @Output() openEditClassCodesModalEvent: EventEmitter<any> = new EventEmitter();
  @Output() openLinkBlocksModalComponentEvent: EventEmitter<any> = new EventEmitter();

  timetableId!: number;
  isInitialized = false;
  displayBlocksFormControl = new FormControl();
  displaySubjectsFormControl = new FormControl();

  selectedYearGroup!: number;
  selectedBandId!: number;
  bandOptions: { id: number, text?: string, html?: string }[] = [];
  bandOptionsMenu: IMenuItem[] = [];
  blocksByYearGroupLength = 0;
  blocksByBandLength = 0;

  BLOCK_TYPE = BLOCK_TYPE;
  blockOptions = [
    { id: 1, text: 'Simple', disabled: false, checked: false },
    { id: 2, text: 'Linear', disabled: false, checked: false },
    { id: 3, text: 'Option', disabled: false, checked: false },
    { id: 4, text: 'Complex', disabled: false, checked: false }
  ];
  blockOptionsMenu: IMenuItem[] = [];

  isEmptySimpleBlock = false;
  isBlockHovered = false;
  isStaffDraggingActive = false;
  isRoomDraggingActive = false;
  freeSubjectId: number | null = null;
  selectedBlockId: number | null = null;
  blocksWithoutFilter: ICurriculumPlanBlock[] = [];
  iFilterObjects: IFilterRequest[] = [{
    blockTypeIds: [],
    subjectIds: [],
    departmentIds: [],
    staffTypeIds: [],
    filterSessions: false,
  }];
  blocksByBand: ICurriculumPlanBlock[] = [];

  subjectOptions: Partial<ISubject>[] = [];
  simpleBlockList: ICurriculumPlanBlock[] = [];
  linearBlockList: ICurriculumPlanBlock[] = [];
  optionBlockList: ICurriculumPlanBlock[] = [];
  complexBlockList: ICurriculumPlanBlock[] = [];
  isExpanded: boolean | undefined;

  blocks: ICurriculumPlanBlock[] = [];
  totalCount: number = 0;
  private bandChangedSubscription: Subscription | undefined;
  readonly unsubscribe$: Subject<void> = new Subject();

  @HostListener('click', ['$event'])
  divClickHandler(event: MouseEvent) {
    const targetId = (event.target as HTMLElement).id;
    if (['simpleBlockRow', 'linearBlockRow', 'optionBlockRow', 'complexBlockRow', 'emptyBlockRow'].includes(targetId)) {
      this.curriculumPlanBlocks.selectedBlockId$.next(null);
    }
  }

  constructor(
    private planningRelationShipsService: PlanningRelationshipsService,
    protected relationships: RelationshipsService,
    private curriculumPlan: CurriculumPlanService,
    private curriculumPlanBlocks: CurriculumPlanBlocksService,
    private band: BandService,
    private snackbar: SnackbarService
  ) {
    this.blockOptionsMenu = this.blockOptions.map(option => {
      return {
        label: option.text,
        value: option.id,
        command: (event: IMenuItemCommandEvent) => {
          this.onAddBlock(option.id);
        }
      } as IMenuItem;
    });
  }

  ngOnInit() {
    this.clearFilterOptions();
    this.planningRelationShipsService.timetableId$
      .pipe(distinctUntilChanged())
      .subscribe(timetableId => {
        if (timetableId) {
          this.timetableId = timetableId
        }
      });

      
    this.curriculumPlanBlocks.isStaffDraggingActive$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(isStaffDraggingActive => {
        this.isStaffDraggingActive = !!isStaffDraggingActive
        if (!isStaffDraggingActive) {
          this.snackbar.hide();
        }
      })

    this.curriculumPlanBlocks.isRoomDraggingActive$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(isRoomDraggingActive => {
        this.isRoomDraggingActive = !!isRoomDraggingActive
        if (!isRoomDraggingActive) {
          this.snackbar.hide();
        }
      })

    this.relationships.freeSubjectId$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(id => {
        this.freeSubjectId = id;
      })

    this.curriculumPlanBlocks.selectedBlockId$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(id => {
        this.selectedBlockId = id;
      })

    this.relationships.subjectsData$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(subjects => this.subjectOptions = subjects.map(subject => ({
        id: subject.id,
        text: subject.name,
        color: '#' + subject.color
      })))

    this.curriculumPlan.isExpanded$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(val => {
        this.isExpanded = val.isExpanded;

        if (val.sessionId) {
          scrollToElement(val.sessionId);
          this.curriculumPlan.isExpanded$.next({ isExpanded: val.isExpanded, sessionId: null });
        }
      });

    this.curriculumPlanBlocks.isAnyBlockHovered$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(hovered => this.isBlockHovered = hovered);

    this.curriculumPlanBlocks.blocksByBand$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(blocksByBand => {
        const emptyBlockIds = blocksByBand
          .filter(block => (!block.subjectToYearGroups[0].subjectId ||
            (block.subjectToYearGroups[0].subjectId && block.blockTypeId !== BLOCK_TYPE.Simple)) &&
            block.blockTypeId !== BLOCK_TYPE.Complex)
          .map(block => `emptySubjectPlaceholder${block.id}`);
        const emptyLinearBlockIds = blocksByBand
          .filter(block => block.blockTypeId === BLOCK_TYPE.Linear)
          .map(block => `expandedEmptyLinearSubjectPlaceholder${block.id}`);
        const emptyOptionBlockIds = blocksByBand
          .filter(block => block.blockTypeId === BLOCK_TYPE.Options)
          .map(block => `expandedEmptyOptionSubjectPlaceholder${block.id}`);
        const emptyComplexBlockIds = blocksByBand
          .filter(block => block.blockTypeId === BLOCK_TYPE.Complex)
          .map(block => {
            return block.sessions.map(session => `expandedEmptyComplexSubjectPlaceholder${session.id}`)
          }).flat();
        const emptyComplexHBlockIds = blocksByBand
          .filter(block => block.blockTypeId === BLOCK_TYPE.Complex)
          .map(block => {
            return block.sessions.map(session => `expandedEmptyComplexSubjectPlaceholder:h:${session.id}`)
          }).flat();
        const emptyComplexVBlockIds = blocksByBand
          .filter(block => block.blockTypeId === BLOCK_TYPE.Complex)
          .map(block => {
            return block.sessions.map(session => `expandedEmptyComplexSubjectPlaceholder:v:${session.id}`)
          }).flat();
        const expandedComplexNewFullClassIds = blocksByBand
          .filter(block => block.blockTypeId === BLOCK_TYPE.Complex)
          .map(block => `expandedComplexNewFullClass${block.id}`);
        const expandedComplexFullClassIds = blocksByBand
          .filter(block => block.blockTypeId === BLOCK_TYPE.Complex)
          .map(block => {
            return block.sessions.map(session => `expandedComplexFullClass-${block.id}-${session.classIndex}`)
          }).flat();
        const expandedComplexNewFullPeriodIds = blocksByBand
          .filter(block => block.blockTypeId === BLOCK_TYPE.Complex)
          .map(block => `expandedComplexNewFullPeriod${block.id}`);
        const expandedComplexFullPeriodIds = blocksByBand
          .filter(block => block.blockTypeId === BLOCK_TYPE.Complex)
          .map(block => {
            return block.sessions.map(session => `expandedComplexFullPeriod-${block.id}-${session.periodIndex}`)
          }).flat();

        this.curriculumPlanBlocks.droppableSubjectIds$.next([...new Set(
          [
            ...emptyBlockIds,
            ...emptyLinearBlockIds,
            ...emptyOptionBlockIds,
            ...emptyComplexBlockIds,
            ...emptyComplexHBlockIds,
            ...emptyComplexVBlockIds,
            ...expandedComplexNewFullClassIds,
            ...expandedComplexFullClassIds,
            ...expandedComplexNewFullPeriodIds,
            ...expandedComplexFullPeriodIds]
        )]);

        this.simpleBlockList = blocksByBand.filter(block => block.blockTypeId === BLOCK_TYPE.Simple);
        this.linearBlockList = blocksByBand.filter(block => block.blockTypeId === BLOCK_TYPE.Linear);
        this.optionBlockList = blocksByBand.filter(block => block.blockTypeId === BLOCK_TYPE.Options);
        this.complexBlockList = blocksByBand.filter(block => block.blockTypeId === BLOCK_TYPE.Complex);
        this.blocksByBandLength = blocksByBand.length;
        this.blocksByBand = blocksByBand;

        this.calculateIsEmptySimpleBlock();
        this.calculateTotal();
      });

    this.curriculumPlanBlocks.blocksByYearGroup$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(blocks => {
        const filteredBlocks = blocks.filter(block => block.bandIds.includes(this.selectedBandId));
        this.curriculumPlanBlocks.blocksByBand$.next(filteredBlocks);
      });

    this.curriculumPlan.selectedYearGroup$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(yearGroup => {
        if (yearGroup) {
          if (!this.curriculumPlan.isExpanded$.getValue().sessionId) {
            this.curriculumPlan.isExpanded$.next({
              isExpanded: this.curriculumPlan.isExpanded$.getValue().isExpanded,
              sessionId: null
            });
          }
          this.selectedYearGroup = yearGroup
          const filterData = this.curriculumPlanBlocks.filterOptionsForBlocks$.getValue();
          this.curriculumPlanBlocks
            .getBlocksByYearGroupFilter(this.timetableId, yearGroup, filterData)
            .subscribe(blocks => {
              const formatted = formatBlock.call(this, blocks);
              this.blocksByYearGroupLength = formatted.length;
              this.curriculumPlanBlocks.blocksByYearGroup$.next(formatted);

              blocks.forEach(block => {
                updateLastVisibleIndexForBlocks(block, this.curriculumPlanBlocks.lastVisibleIndexForBlocks);
              });
            });
        }
      });

    this.band.fetchedBands$.subscribe(bands => {
      if (bands) {
        this.bandOptions = this.getBandOptions(bands);
        this.bandOptionsMenu = this.bandOptions.map(option => {
          return {
            label: option.text,
            labelHtml: option.html,
            value: option.id,
            command: (event: IMenuItemCommandEvent) => {
              this.addBand(option.id);
            }
          } as IMenuItem;
        })
      }
    })

    this.curriculumPlanBlocks.stateChanged$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(() => this.getBlocksByFilter());


    this.curriculumPlan.selectedBand$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(band => {
        const blocksByYearGroup = this.curriculumPlanBlocks.blocksByYearGroup$.getValue();
        if (!band || !blocksByYearGroup) return;
        this.selectedBandId = band;
        const filteredBlocks = blocksByYearGroup.filter(block => block.bandIds.includes(band));
        this.curriculumPlanBlocks.blocksByBand$.next(filteredBlocks);
      });


    this.isInitialized = true;

    this.curriculumPlanBlocks.scrollToSelectedBlock$.subscribe(() => {
      const id = this.curriculumPlanBlocks.selectedBlockId$.getValue();
      if (id) {
        this.scrollToSelectedBlockElement(id);
      }
    })

    this.displayBlocksFormControl.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(() => {
      this.getBlocksByFilter();
      })

    this.displaySubjectsFormControl.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(() => {
      this.getBlocksByFilter();
      })

    this.curriculumPlanBlocks.filterOptionsForBlocks$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(filters => {
        if (!this.selectedYearGroup) {
          return
        }

        this.curriculumPlanBlocks.getBlocksByYearGroupFilter(this.timetableId, this.selectedYearGroup, filters)
          .subscribe(blocks => {
            this.formatAndSetBlocks(blocks);
          })
      })

    this.curriculumPlan.setChangeBandFunction(this.changedCurrentBand.bind(this));
  }

  ngAfterViewInit() {
    this.getBlocksByFilter();
    this.blocksByBand = this.curriculumPlanBlocks.blocksByBand$.getValue();
    this.getTotalBlocks();
  }

  ngOnDestroy(): void {
    this.curriculumPlanBlocks.blocksByYearGroup$.next([]);
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    if (this.bandChangedSubscription) {
      this.bandChangedSubscription.unsubscribe();
    }
  }

  getBlocksByFilter(): void {
    if (!this.selectedYearGroup) {
      return
    }

    this.curriculumPlanBlocks.filterOptionsForBlocks$.next({
      blockTypeIds: this.displayBlocksFormControl.getRawValue() ?? [],
      subjectIds: this.displaySubjectsFormControl.getRawValue() ?? [],
      departmentIds: [],
      staffTypeIds: [],
      filterSessions: this.curriculumPlanBlocks.filterOptionsForBlocks$.getValue().filterSessions
    });
  }

  clearFilterOptions(): void {
    this.curriculumPlanBlocks.filterOptionsForBlocks$.next({
      subjectIds: [],
      departmentIds: [],
      staffTypeIds: [],
      blockTypeIds: [],
      onlyUnscheduled: false,
      filterSessions: false
    });
  }

  formatAndSetBlocks(blocks: ICurriculumPlanBlock[]): void {
    const formatted = formatBlock.call(this, blocks);
    this.blocksByYearGroupLength = formatted.length;
    this.curriculumPlanBlocks.blocksByYearGroup$.next(formatted);
  }

  getBandOptions(bands: IBand[]): { id: number, text?: string, html?: string }[] {
    const options: { id: number, text?: string, html?: string }[] = [];
    const firstBand = bands[0];
    const lastBand = bands[bands.length - 1];

    options.push({ id: 1, html: `<i class="far fa-arrow-to-left"></i> ${firstBand?.bandName}` });

    if (bands.length >= 2) {
      bands.slice(0, -1).forEach((band, index) => {
        const nextBand = bands[index + 1];
        options.push({
          id: index + 2,
          html: `${band.bandName} <i class="far fa-arrow-to-left"></i><i class="far fa-arrow-to-right"></i> ${nextBand.bandName}`
        });
      });
    }

    options.push({ id: bands.length + 1, html: `${lastBand?.bandName} <i class="far fa-arrow-to-right"></i>` });

    return options;
  }

  addBand(id: number | string): void {
    if (typeof id !== 'number') {
      return;
    }
    this.bandSidePanel.addBand(id - 1);
  }

  ///////////////////////////////
  //// ADD NEW BLOCK RELATED ////
  ///////////////////////////////

  onAddBlock(blockTypeId: BLOCK_TYPE): void {
    if (!Object.values(BLOCK_TYPE).includes(blockTypeId)) {
      return;
    }
    this.addNewBlock(blockTypeId);
  }


  addSubjectToBlock(data: ICurriculumPlanAddSubjectToBlock, blockTypeId: number): void {
    const { listIndex, currentIndex, id, sessionIds } = data;
    const blockList = this.getBlockListByBlockType(blockTypeId);
    const block = blockList[listIndex]
    if (blockList[listIndex].subjectToYearGroups[currentIndex]?.subjectId) {
      return;
    }

    this.curriculumPlanBlocks.addSubjectToBlock({
      blockId: block.id,
      subjectId: id,
      sessionIds: sessionIds ?? []
    })
      .subscribe({
        next: () => {
          // This runs when subject dragged to planning area to create new simple block
          // to remove emptySubjectPlaceHolder to not get warning on console
          if (data && blockTypeId === BLOCK_TYPE.Simple) {
            const newDroppableBlockIds = this.curriculumPlanBlocks.droppableSubjectIds$.getValue()
              .filter(id => id !== 'emptySubjectPlaceholder' + block.id);
            this.curriculumPlanBlocks.droppableSubjectIds$.next(newDroppableBlockIds);
          }

          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.calculateIsEmptySimpleBlock();
          this.snackbar.saved();
        },
        error: () => {
          this.snackbar.error();
        }
      })
  }

  addStaffToBlock(data: ICurriculumPlanAddStaffToBlock): void {
    const { isAdditionalStaff } = data;

    const request$ = isAdditionalStaff
      ? this.curriculumPlanBlocks.assignAdditionalStaffToSession(data)
      : this.curriculumPlanBlocks.assignMainStaffToSession(data)

    request$.subscribe({
      next: () => {
        // Hide Information snackbar
        this.snackbar.hide();
        this.curriculumPlanBlocks.stateChanged$.next();
        this.snackbar.saved();
      },
      error: ({ error }) => {
        error.validationErrors?.[0]?.errorMessage
          ? this.snackbar.error(error.validationErrors[0]?.errorMessage)
          : this.snackbar.error();
      }
    });
  }

  addRoomToBlock(data: ICurriculumPlanAddRoomToBlock): void {
    this.curriculumPlanBlocks.assignRoomToSession(data).subscribe({
      next: () => {
        // Hide Information snackbar
        this.snackbar.hide();
        this.curriculumPlanBlocks.stateChanged$.next();
        this.snackbar.saved();
      },
      error: (error) => {
        if (error.status === 422 || error.statusText === 'Unprocessable Entity') {
          this.snackbar.error('This Room is already assigned to another session or Non-contact code activity during this period');
        } else {
          this.snackbar.error();
        }
      }
    });
  }

  private addNewBlock(blockTypeId: number, data?: ICurriculumPlanAddSubjectToBlock): void {
    const blockData = {
      blockTypeId,
      yearGroupId: this.selectedYearGroup,
      bandId: this.selectedBandId
    };

    this.curriculumPlanBlocks.addNewBlock(this.timetableId, blockData).subscribe({
      next: (response) => {
        const newDroppableBlockIds = [
          ...this.curriculumPlanBlocks.droppableSubjectIds$.getValue(),
          response.blockId.toString()
        ];
        this.curriculumPlanBlocks.droppableSubjectIds$.next(newDroppableBlockIds);
        this.curriculumPlanBlocks.lastVisibleIndexForBlocks[response.blockId] = 1;
        this.curriculumPlanBlocks.selectedBlockId$.next(response.blockId);
        this.curriculumPlanBlocks.stateChanged$.next();
        this.curriculumPlanBlocks.blockSideStateChanged$.next();

        setTimeout(() => {
          // This runs when subject dragged to planning area to create new simple block
          if (data && blockData.blockTypeId === BLOCK_TYPE.Simple) {
            const newBlock = createEmptyBlock.call(this, response, blockData.blockTypeId);
            this.simpleBlockList.push(newBlock);
            const newlyAddedBlockIndex = this.simpleBlockList.findIndex(simpleBlock => simpleBlock.id === response.blockId);
            this.addSubjectToBlock({ ...data, listIndex: newlyAddedBlockIndex }, BLOCK_TYPE.Simple);
          }
        }, 100)

        setTimeout(() => {
          this.calculateIsEmptySimpleBlock();
          this.curriculumPlanBlocks.scrollToSelectedBlockOnSidePanel$.next();
          this.curriculumPlanBlocks.scrollToSelectedBlock$.next();
          this.calculateTotal();
      }, 500)

        if ([BLOCK_TYPE.Linear, BLOCK_TYPE.Complex].includes(blockData.blockTypeId)) {
          this.curriculumPlanBlocks.lastVisibleIndexForBlocks[response.blockId] = 1;
        }
      },
      error: () => {
        this.snackbar.error();
      }
    });
  }

  private calculateIsEmptySimpleBlock(): void {
    this.isEmptySimpleBlock = false;
    this.simpleBlockList.forEach(simpleBlock => {

      if (!simpleBlock.subjectToYearGroups[0].subjectId) {
        this.isEmptySimpleBlock = true;
      }
    });
    this.calculateTotal();
  }

  private getBlockListByBlockType(blockTypeId: number): ICurriculumPlanBlock[] {
    if (blockTypeId === BLOCK_TYPE.Simple) {
      return this.simpleBlockList;
    } else if (blockTypeId === BLOCK_TYPE.Linear) {
      return this.linearBlockList;
    } else if (blockTypeId === BLOCK_TYPE.Options) {
      return this.optionBlockList;
    } else {
      return this.complexBlockList;
    }
  }

  scrollToSelectedBlockElement(elementId: number) {
    const blocks = this.curriculumPlanBlocks.blocksByBand$.getValue().map(block => block.id);
    if (blocks.includes(elementId)) {
      this.scrollToElement(elementId)
    } else {
      const blocksByYearGroup = this.curriculumPlanBlocks.blocksByYearGroup$.getValue();
      const selectedBlock = blocksByYearGroup.find(block => block.id === elementId);
      if (selectedBlock) {
        this.curriculumPlan.selectedBand$.next(selectedBlock.bandIds[0])
        this.scrollToElement(elementId)
      }
    }
  }

  scrollToElement(elementId: number): void {
    const element = document.getElementById(elementId.toString());
    setTimeout(() => {
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 1500);
  }

  sessionMenuOpened(data: any) {
    this.sessionMenuOpenedEvent.emit(data);
  }

  subjectMenuOpened(data: any) {
    this.subjectMenuOpenedEvent.emit(data);
  }

  periodMenuOpened(data: any) {
    this.periodMenuOpenedEvent.emit(data);
  }

  openEditClassCodesModal(event: any) {
    this.openEditClassCodesModalEvent.emit(event);
  }

  openLinkBlocksModal(event: any) {
    this.openLinkBlocksModalComponentEvent.emit(event);
  }

  calculateTotal(): void {
    this.totalCount = this.blocksByBand.reduce((total, block) => {
      const periodCount = block.periodCount;
      if (typeof periodCount === 'number' && !isNaN(periodCount)) {
        total += periodCount;
      }
      return total;
    }, 0);
  
    this.curriculumPlan.updateTotalCount(this.totalCount);
  }
  
  getTotalBlocks() {
    this.curriculumPlanBlocks.getBlocksByYearGroupFilter(this.timetableId, this.selectedYearGroup, this.iFilterObjects[0])
      .subscribe(blocks => {
        const formatted = formatBlock.call(this, blocks);
        if (!this.selectedBandId || !blocks) return;
        const filteredBlocks = formatted.filter(block => block.bandIds.includes(this.selectedBandId));
        this.blocksByBand = filteredBlocks;   
        this.calculateTotal();   
      })
  }

  changedCurrentBand() {
    this.calculateTotal();
  }
}
