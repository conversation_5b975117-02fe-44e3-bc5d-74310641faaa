<ng-container *ngIf="isOpen">
  <bcm-modal full-width="true" size="xxxlarge" class="preview-modal" (bcm-modal-before-close)="hide()" #previewModal>
        <bcm-modal-header>{{ 'Preview report' | translate }}</bcm-modal-header>
    <div class="toolbar">
      <bcm-button mat-button (click)="hide()">Back</bcm-button>
      <bcm-button mat-button>Export</bcm-button>
    </div>

    <ag-grid-angular
      class="ag-theme-alpine timetable-grid"
      style="width: 100%; height: 600px;"
      [columnDefs]="columnDefs"
      [rowData]="rowData"
      [defaultColDef]="{ resizable: false, sortable: false, filter: false }"
      [suppressRowTransform]="true"
      [domLayout]="'normal'"
      [suppressHorizontalScroll]="false"
      [suppressColumnVirtualisation]="true"
      [enableRangeSelection]="false"
      [rowSelection]="undefined"
      (gridReady)="onGridReady($event)"
    >
    </ag-grid-angular>
  </bcm-modal>
</ng-container>
