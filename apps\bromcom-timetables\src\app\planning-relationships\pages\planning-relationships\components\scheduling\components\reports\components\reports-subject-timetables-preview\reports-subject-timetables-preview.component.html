<ng-container *ngIf="isOpen">
  <bcm-modal full-width="true" size="fullscreen" class="preview-modal fullscreen-modal" (bcm-modal-before-close)="hide()" #previewModal>
    <bcm-modal-header>{{ 'Preview report' | translate }}</bcm-modal-header>
    <div class="toolbar">
      <bcm-button
        kind="secondary"
        icon="fa-solid fa-arrow-left"
        (bcm-click)="hide()">
        {{ 'Back' | translate }}
      </bcm-button>
      <div class="pagination-controls" *ngIf="totalPages > 1">
        <bcm-button
          kind="ghost"
          icon="fa-solid fa-angles-left"
          [disabled]="currentPage === 1"
          (bcm-click)="goToFirstPage()">
        </bcm-button>
        <bcm-button
          kind="ghost"
          icon="fa-solid fa-angle-left"
          [disabled]="currentPage === 1"
          (bcm-click)="goToPreviousPage()">
        </bcm-button>
        <span class="page-info">{{ currentPage }} of {{ totalPages }}</span>
        <bcm-button
          kind="ghost"
          icon="fa-solid fa-angle-right"
          [disabled]="currentPage === totalPages"
          (bcm-click)="goToNextPage()">
        </bcm-button>
        <bcm-button
          kind="ghost"
          icon="fa-solid fa-angles-right"
          [disabled]="currentPage === totalPages"
          (bcm-click)="goToLastPage()">
        </bcm-button>
      </div>
      <bcm-button
        kind="secondary"
        icon="fa-solid fa-download"
        (bcm-click)="exportReport()">
        {{ 'Export' | translate }}
      </bcm-button>
    </div>

    <ag-grid-angular
      class="ag-theme-alpine timetable-grid"
      style="width: 100%; height: calc(100vh - 200px);"
      [columnDefs]="columnDefs"
      [rowData]="currentPageData"
      [defaultColDef]="{ resizable: false, sortable: false, filter: false }"
      [suppressRowTransform]="true"
      [domLayout]="'normal'"
      [suppressHorizontalScroll]="false"
      [suppressColumnVirtualisation]="true"
      [enableRangeSelection]="false"
      [rowSelection]="undefined"
      [suppressPaginationPanel]="true"
      [suppressScrollOnNewData]="true"
      (gridReady)="onGridReady($event)"
    >
    </ag-grid-angular>
  </bcm-modal>
</ng-container>
