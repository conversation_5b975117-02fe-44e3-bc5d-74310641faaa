﻿import { Component } from '@angular/core';
import { ICellRendererParams } from 'ag-grid-community';

export interface IAutoScheduleIssuesActions extends ICellRendererParams {
  onAcceptRow: (params: ICellRendererParams) => {},
}

@Component({
  selector: 'bromcom-auto-schedule-issues-actions',
  templateUrl: './auto-schedule-issues-actions.component.html',
  styleUrls: ['./auto-schedule-issues-actions.component.scss'],
})
export class AutoScheduleIssuesActionsComponent {
  protected params!: IAutoScheduleIssuesActions;

  agInit(params: IAutoScheduleIssuesActions): void {
    this.params = params;
  }

  onAcceptRow(): void {
    this.params.onAcceptRow(this.params);
  }
}
