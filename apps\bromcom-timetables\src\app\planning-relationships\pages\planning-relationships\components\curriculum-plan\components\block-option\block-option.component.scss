@import '../../../../../../../../assets/styles/variables';
@import '../../../../../../styles/blocks';

@mixin flex() {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin text-style() {
  font-family: 'Inter', serif;
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
}

.block-container {
  margin: 8px;
  width: 168px;
  min-height: 168px;
  background: $color-blue-grey-100;
  border-radius: 4px;

  &:not(.active-block) {
    border: 2px solid $color-white-0;
  }

  .icon {
    font-size: 16px;
    cursor: pointer;
  }

  .header {
    @include header;
  }

  .block-type-full-name,
  .period-count {
    @include flex;
    @include text-style;
    @include full-name-style;
  }

  .subject-info-container {
    @include subject-info-container;
  }

  .session-info-container {
    @include session-info-container;
  }

  .bottom {
    width: 100%;
    height: 16px;
    border-radius: 0 0 4px 4px;
  }
}

.hide-add-new-block {
  height: 0;
  visibility: hidden;
}
