# <span style="color:coral">Scheduling</span>

## <span style="color:coral">GoJS integration</span>

### <span style="color:coral">Diagram Initialization</span>

```apps/bromcom-timetables/src/app/planning-relationships/pages/planning-relationships/components/scheduling/components/timetable-canvas/helper/visual-matrix/VisualMatrix.diagram.ts```

The `createDiagram()` function initializes a GoJS diagram with various configurations:

- Utilizes a license key obtained from the environment variable goJSLicense<PERSON>ey to activate GoJS.
- Defines a visual matrix layout with specified cell size, columns, rows, and additional layout 
  configurations.
- Sets up a grid panel with specified cell size and background color, along with horizontal and vertical
  grid lines.
- Configures various interaction tools such as selecting, context menu, dragging with grid snap,
  resizing with grid snap, and mouse wheel behavior.
- Enables an undo manager to support undo/redo functionality.
- Defines computations to adjust the viewport and canvas size based on diagram changes.
- Sets the diagram's model and templates for nodes and groups if provided in the configuration.

Additionally, the function initializes a custom resizing tool (LimitedDiagramResizingTool) and assigns it to the diagram's tool manager.

### <span style="color:coral">Visual Matrix Layout</span>

The `VisualMatrixLayout` class extends `go.GridLayout` to define a custom layout for a GoJS diagram. Here are the important aspects of this class:

### Properties and Constructor

```ts
export class VisualMatrixLayout extends go.GridLayout {
  private _columns: IVisualMatrixColumnHeader[];
  private _rows: IVisualMatrixRowHeader[];
  private stopRender = false;

  private originOffsetX = 0;
  private originOffsetY = 0;

  private readonly createColumnHeaderNodeTemplate: IMakerColumnHeaderNodeTemplate;
  private readonly createRowHeaderNodeTemplate: IMakerRowHeaderNodeTemplate;
  private readonly createTopLeftHeaderNodeTemplate: IMakerTopLeftHeaderNodeTemplate;

  viewType = SCHEDULING_VIEW_TYPES.Detailed;
  organisedBy = ORGANISED_BY_TYPES.YearGroup;

  // Other properties and constructor initialization omitted for brevity
}
```

This class provides methods for parsing and adding columns, rows, and headers to the diagram layout. It also allows for setting the layout origin.

### Methods

```ts
// Method to perform layout
override doLayout(coll: go.Diagram | go.Group | go.Iterable<go.Part>) {
  // Perform layout only if diagram exists and rendering is not stopped
  const diagram = this.diagram;
  if (diagram === null) return;
  if (this.stopRender) return;
  this.parse();
  super.doLayout(coll);
}

// Method to parse layout
public parse(): void {
  const diagram = this.diagram;
  // Rebuild header templates and add columns, rows, and top left header
  this.headerTableRowTemplate.rebuildItemElements();
  this.headerTableColumnTemplate.rebuildItemElements();
  this.headerTopLeftTableTemplate.rebuildItemElements();
  this.addColumns();
  this.addRows();
  this.addTopLeftHeader();
  diagram?.add(this.headerTopLeftTableTemplate);
  diagram?.add(this.headerTableRowTemplate);
  diagram?.add(this.headerTableColumnTemplate);
  this.stopRender = true;
}
```
   

## <span style="color:coral">Block interface and structure</span>

### Block interface

```ts
export interface IListOfBlocksResponse {
  id: number | null;
  blockTypeId: BLOCK_TYPE; // 1 - Simple, 2 - Linear, 3 - Options, 4 - Complex
  blockCode?: string;
  blockName?: string;
  bandIds?: number[];
  classCount?: number; // How many classes the whole block has. Option -> Sum of the subjects' classes
  periodCount?: number; // How many periods the whole block has. Linear -> Sum of the subjects' periods
  subjectToYearGroups: ISubjectToYearGroup[]; // Subjects added to the block
  sessions?: ISession[]; // All sessions of the block
  blockType?: string; //Property to hold the type string to arrange sessions
  isCrossBand?: boolean; //Boolean property to check if a block is cross band to arrange sessions
  bandDetails?: string; //String property to hold the band details including the band name and year group which is used to arrange the block layout
  classes?: string[]; //Array of strings to hold the class names
  isAdditionalGroup?: boolean; //Boolean property used to identify the additional empty group in subject information component
}
```

### Session interface

```ts
export interface ISession {
  id: number;
  subjectToYearGroupId: number;
  mainStaffId: number | null;
  additionalStaffIds: number[];
  roomId: number | null;
  periodId: number | null;
  sessionName: string | null;
  className: string;
  customClassName: string;
  joinedSessions: number[];
  isLocked?: boolean;
  periodIndex?: number;
  classIndex?: number;
  //The below properties are relevant for side panel block layout
  customClass?: string;
  colorCode?: string;
  staffDisplayName?: string;
  tooltipContent?: string;
  hasDifferentStaff?: boolean;
  columnId?: number;
  pairedColumnId?: number;
  isDraggable?: boolean;
  sessionsCount?: number;
  toBeScheduled?: boolean;
  toBeScheduledPeriodId?: number | null;
  sessionStaffName?: string | null;
  sessionRoomName?: string | null;
  sessionIndex?: number;
  sessionStaffId?: number | null;
  isGreyedOut?: boolean;
  isFiltered?: boolean;
  isSelected?: boolean;
}
```

### SubjectToYearGroup interface

```ts
export interface ISubjectToYearGroup {
  id: number;
  yearGroupId: number;
  subjectId: number;
  classCount: number;
  periodCount: number;
  singlePeriodCount?: number;
  doublePeriodCount?: number;
  colorCode?: string;
  assignedPeriodCount?: number;
}
```

## <span style="color:coral">Arrange Session Pipe</span>

For the UI of block layout in the side panel at the beginning we started to transform in the following steps in the ArrangeSessionsPipe:

The ArrangeSessionsPipe is an Angular pipe responsible for arranging sessions based on various parameters such as class name, staff, rooms, etc. This pipe is designed to facilitate the visualization and organization of sessions within a block.

- It takes an array of sessions (ISession[]), along with additional parameters such as 
  subjectsInYearGroup, className, staffList, blockTypeId, and optionally rooms.
- This method (transform()) is the main transformation logic of the pipe. It arranges sessions based on 
  the provided parameters.
- It filters sessions based on the provided class name and block type.
- It arranges single and double periods for each subject and class.
- It updates column IDs and other session properties for proper visualization.
- It handles complex block types differently from simple block types.
- arrangeSessions(): Arranges single and double sessions based on block type, staff, and room details.
- sortByjoinedSessionsLength(): Sorts sessions based on the length of joined sessions.
- appendStaffAndRoomDetails(): Appends staff and room details to sessions based on block type and 
  session characteristics.
- updateLayout(): Updates session layout based on scheduling status and block type.
- This pipe assumes a certain data structure and behavior of session objects and their relationships 
  within the system

```apps/bromcom-timetables/src/app/planning-relationships/pages/planning-relationships/components/scheduling/pipes/arrange-sessions.pipe.ts```

### <span style="color:coral">Scheduling of Simple block</span>

Can have

- Only one subject
- Multiple classes
- Multiple periods

Scheduling

- Blocks in side panel are shown in simplified view
- During scheduling all sessions of a period are scheduled together
- Can be scheduled to any period but to the band for which the block was created

### <span style="color:coral">Scheduling of Linear block</span>

Can have

- Multiple subjects
- Multiple classes
- Multiple periods

Scheduling

- Blocks in side panel are shown in simplified view
- Subjects are "next" to each other
- Each session can be scheduled individually within the period count of the block based on the class 
  order
- Can be scheduled to any period but to the band for which the block was created

### <span style="color:coral">Scheduling of Option block</span>

Can have

- Multiple subjects
- Multiple classes
- Multiple periods

Scheduling

- Blocks in side panel are shown in simplified view
- Subjects are "under" each other
- During scheduling all sessions of a period are scheduled together
- Can be scheduled to any period but to the band for which the block was created

### <span style="color:coral">Complex block</span>

Can have

- Multiple subjects
- Multiple classes
- Multiple periods

Good to know

- Blocks in side panel are shown in simplified view
- Subjects are "mixed", each session is a different subject entity
- During scheduling all sessions of a period are scheduled together
- No hidden periods
- Can be scheduled to any period but to the band for which the block was created

## <span style="color:coral">TimetableSource</span>

- The TimetableSource class is defined to manage the timetable data and operations in the scheduling area
- Methods like 'setRows', 'setYearGroupsRows', 'setStaffRows', and 'setRoomRows' are declared to set up 
  rows based on different organizing criteria.
- 'setColumns' method is defined to convert period structures to columns.
- Functions like 'convertPeriodStructureToColumns', 'convertRoomsWithDepartmentsToRows', etc., are 
  imported and used within the class to transform data structures.
- Filtering data based on certain conditions related to staff types and session IDs.
- Mapping and filtering data to obtain sessions based on specific criteria like period IDs, staff IDs, 
  or room IDs.
- Handling cases where sessions are joined or double sessions, and updating assembly data accordingly.
- Updating row heights based on the organization type (YearGroup, Teacher, or Room).
- Handling conflicts and setting accepted flags based on certain conflict types (staff assignment or  
  room assignment).
- Setting missing filter flags for sessions based on missing filters (room or staff).
- Converting data to GoJS nodes for visualization purposes, considering different view types (Simple or 
  Detailed) and organization types (YearGroup, Teacher, or Room).
- Finding the block associated with a session.  
- findBlocksForSessionIds: Given an array of session IDs, it returns an array of blocks corresponding to 
  those sessions.
- getPeriodIdFromCoordinates: Calculates and returns the period ID based on the provided coordinates.
- getCoordinatesForPeriodId: Calculates and returns the coordinates for a given period ID.
- getFollowingXPeriodId: Returns the period ID that follows a given period ID by a specified number of 
  periods.
- getFollowingXTeachingPeriodId: Similar to the previous function but specifically for teaching periods, 
  skipping breaks. 
- convertAssemblyToGroup: Converts an assembly (group of sessions) into a format suitable for display   
  as a group in a visual representation. 
- convertSessionsToNodeData: Converts individual sessions within an assembly to a format suitable for 
  display as nodes in a visual representation.
- convertSessionsToSimpleNodeData: Similar to the previous function but for a simpler representation 
  of sessions. 
- convertAssemblyToSimpleNode: Converts an assembly to a simpler node format.
- getAssemblyLocation: Calculates the location of an assembly based on its key.
- getLocation: Calculates the location of a session based on its key, assembly, and session ID.
- appendStaffAndRoomDetails: Appends staff and room details to a session.
- getColorForSubject: Determines the color for a session based on its subject.
- flatPeriods: Getter function that flattens and maps periods into a flat array.
- generateBreakNodes, generateDaybreakNodes, generateAMPMbreakNodes: Generate placeholder nodes for 
  breaks, day breaks, and AM/PM breaks.
- createNonContactCodesNodes: Creates nodes for non-contact codes.
- checkForStaff, checkForRoom: Helper functions to check if a staff or room exists.
- getStaffRowHeaderWidth: Calculates the width for the staff row header.
- SessionAssembly class: Represents a session assembly with various properties and a constructor
  for initialization.

```ts
export class SessionAssembly {
  assemblyId!: string;
  key!: string;
  startPeriodId!: number;
  sessions: ISessionWithBlocks[] = [];
  bandId!: number;
  reservedBandIds!: number[];
  staffId!: number | null;
  // additionalStaffIds: number[] = [];
  roomId!: number | null;

  reservedPeriodIds!: number[];
  blockType!: BLOCK_TYPE;
  isAccepted!: boolean;

  constructor(assembly?: SessionAssembly) {
    if (assembly) {
      Object.keys(assembly).forEach(key => {
        (this as any)[key] = (assembly as any)[key] ;
      })
    }
    this.assemblyId = uuidv4();
  }
}

```