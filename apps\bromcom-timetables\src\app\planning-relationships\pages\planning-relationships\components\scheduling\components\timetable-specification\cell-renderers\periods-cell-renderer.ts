import { ICellRendererComp, ICellRendererParams } from 'ag-grid-community';

export class PeriodsCellRenderer implements ICellRendererComp {
  eGui: HTMLSpanElement;
  value: any;
  periods!: string[];
  color = '';

  constructor() {
    this.eGui = document.createElement('div');
  }

  init(params: any) {
    this.value = params.value;
    this.periods = params.periods;
    this.color = params.color;
    this.updatePeriods();
  }

  updatePeriods(): void {
    this.eGui.style.width = '100%';
    let innerHTML = ''
    if (this.periods) {
      this.periods.forEach((period) => {
        const element = `<bcm-chip color="${this.color}" style="margin-right: 8px">${period}</bcm-chip>`
        innerHTML += element;
      });
    }

    this.eGui.innerHTML = `<div style="width: 100%; display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">${innerHTML}</div>`;
  }

  getGui() {
    return this.eGui;
  }

  refresh(params: ICellRendererParams) {
    this.value = params.value;

    this.eGui.innerHTML = '';
    this.updatePeriods();

    return true;
  }
}
