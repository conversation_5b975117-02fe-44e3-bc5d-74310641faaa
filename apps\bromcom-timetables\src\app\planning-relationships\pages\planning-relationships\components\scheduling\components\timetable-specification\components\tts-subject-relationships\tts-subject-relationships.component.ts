import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { forkJoin, Subject, switchMap, takeUntil } from 'rxjs';
import { GridOptions, GridReadyEvent, ICellRendererParams } from 'ag-grid-community';
import { GridApi } from 'ag-grid-community/dist/lib/gridApi';
import { TranslateService } from '@ngx-translate/core';
import { gridOptions } from './tts-subject-relationships-gridoptions';
import { PlanningRelationshipsService } from '../../../../../../../../services/planning-relationships.service';
import { RelationshipsService } from '../../../../../../../../services/relationships.service';
import { ISubject } from '../../../../../../../../../_shared/models/ISubject';
import { IYearGroup } from '../../../../../../../../../_shared/models/IYearGroup';
import { INCLUDED_TYPES } from '../../../../../../../../../_shared/enums/IncludedTypes';
import { TimetableSpecificationsService } from '../../../../../../../../services/timetable-specifications.service';
import { ITTSSubjectRelationship } from '../../../../../../../../../_shared/models/ITTSSubjectRelationship';
import {
  GeneralModalComponent
} from '../../../../../../../../../_shared/components/general-modal/general-modal.component';
import { SnackbarService } from '@bromcom/ui';

@Component({
  selector: 'bromcom-tts-subject-relationships',
  templateUrl: './tts-subject-relationships.component.html',
  styleUrls: ['./tts-subject-relationships.component.scss']
})
export class TtsSubjectRelationshipsComponent implements OnInit, OnDestroy {
  @ViewChild('ttsSubjectRelationshipsModal', { static: false }) ttsSubjectRelationshipsModal!: ElementRef;
  @ViewChild('deleteSubjectRelationshipWarningModal') deleteSubjectRelationshipWarningModal!: GeneralModalComponent;
  @ViewChild('excludeSubjectRelationshipWarningModal') excludeSubjectRelationshipWarningModal!: GeneralModalComponent;

  @Input() timetableId!: number;
  params!: GridReadyEvent;
  gridApi!: GridApi;
  gridOptions!: GridOptions;
  searchControl = new FormControl(null);
  yearGroups: Partial<IYearGroup>[] = [];
  subjectOptions: Partial<ISubject>[] = [];
  ruleOptions: { id: number, name: string }[] = [
    { id: 1, name: 'Must follow within a structured week' },
    { id: 2, name: 'Must precede within a structured week' },
    { id: 3, name: 'Not continuous within a day' },
    { id: 4, name: 'Must be on the same day as' },
    { id: 5, name: 'Must not be on the same day as' },
    { id: 6, name: 'Scheduled together within a day' }
  ]
  gridData: ITTSSubjectRelationship[] = [];
  INCLUDED_TYPES = INCLUDED_TYPES;
  viewType: INCLUDED_TYPES = INCLUDED_TYPES.ACTIVE;
  deleteRowId!: number;
  isRemoveBulkDisabled = true;
  excludeRowIds: number[] = [];

  readonly unsubscribe$: Subject<void> = new Subject();

  constructor(
    private timetableSpecificationsService: TimetableSpecificationsService,
    protected relationshipsService: RelationshipsService,
    protected planningRelationshipsService: PlanningRelationshipsService,
    protected translate: TranslateService,
    private snackbar: SnackbarService
  ) {
  }

  ngOnInit(): void {
    this.relationshipsService.subjectsData$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(subjects => this.subjectOptions = subjects.map(subject => ({
        id: subject.id,
        name: subject.name,
        text: subject.name,
        code: subject.code,
        color: '#' + subject.color
      })))

    this.planningRelationshipsService.yearGroupsData$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(yearGroups => {
        this.yearGroups = yearGroups
          .filter((yearGroup) => !yearGroup.isExcluded)
          .sort((a, b) => Number(b.name) - Number(a.name));
      })

    this.gridOptions = gridOptions.call(this, {
      onAddNewRow: this.onAddNewRow.bind(this),
      onAcceptNewRow: this.onAcceptNewRow.bind(this),
      onCancelAddingNewRow: this.onCancelAddingNewRow.bind(this),
      onEditRow: this.onEditRow.bind(this),
      onDeleteRow: this.onDeleteRowClicked.bind(this),
      onAcceptRow: this.onAcceptRow.bind(this),
      onCancelEditRow: this.onCancelEditRow.bind(this),
      onExcludeRow: this.onExcludeRow.bind(this),
      onIncludeRow: this.onIncludeRow.bind(this)
    });

    this.searchControl.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(searchValue => {
        this.gridApi.setQuickFilter(searchValue ?? '');
      })

    forkJoin([
      this.timetableSpecificationsService.getSubjectRelationshipsEnumList(),
      this.timetableSpecificationsService.getSubjectRelationshipsList(this.timetableId)
    ]).subscribe(([enums, data]) => {
      this.ruleOptions = enums;
      this.gridData = data;
      this.setRowData();
    })

    setTimeout(() => {
      this.setRowData();
    }, 50)
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  onGridReady(params: GridReadyEvent): void {
    this.params = params;
    this.gridApi = params.api;
    this.gridApi.setDomLayout('normal');
  }

  onAddNewRow(params: ICellRendererParams): void {
    this.gridOptions.api?.setPinnedBottomRowData([
      {
        id: 'newRowId',
        yearGroupIds: [],
        firstSubjectId: null,
        typeId: null,
        secondSubjectId: null,
        isExcluded: false
      }
    ]);
    const rowColumns = this.params.columnApi.getColumns() ?? [];
    this.gridApi.startEditingCell({ rowIndex: 0, rowPinned: 'bottom', colKey: rowColumns[1]?.getColId() });
  }

  onAcceptNewRow(params: ICellRendererParams) {
    this.timetableSpecificationsService.addSubjectRelationship(this.timetableId, {
      ...params.data,
      id: 0
    }).pipe(switchMap(() => this.timetableSpecificationsService.getSubjectRelationshipsList(this.timetableId)))
      .subscribe({
        next: data => {
          this.gridData = data;
          this.setRowData();
          this.gridOptions.api?.setPinnedBottomRowData([{}]);
          this.snackbar.saved();
        },
        error: ({ error }) => {
          if (error?.validationErrors && error?.validationErrors[0]) {
            this.snackbar.error(error.validationErrors[0].errorMessage);
          }
          const rowColumns = this.params.columnApi.getColumns() ?? [];
          this.gridApi.startEditingCell({ rowIndex: 0, rowPinned: 'bottom', colKey: rowColumns[1]?.getColId() });
        }
      })
  }

  onCancelAddingNewRow(): void {
    this.gridOptions.api?.setPinnedBottomRowData([{}]);
  }

  onEditRow(params: ICellRendererParams): void {
    const rowIndex = params.node.rowIndex;
    const rowColumns = this.params.columnApi.getColumns() ?? []
    if (rowIndex || rowIndex === 0) {
      this.gridApi.startEditingCell({ rowIndex, colKey: rowColumns[0].getColId() })
    }
  }

  onAcceptRow(params: ICellRendererParams): void {
    this.timetableSpecificationsService.editSubjectRelationship(params.data.id, params.data)
      .pipe(switchMap(() => this.timetableSpecificationsService.getSubjectRelationshipsList(this.timetableId)))
      .subscribe(
        {
          next: data => {
            this.gridData = data;
            this.setRowData();
            this.snackbar.saved();
          },
          error: ({ error }) => {
            if (error?.validationErrors && error?.validationErrors[0]) {
              this.snackbar.error(error.validationErrors[0].errorMessage);
            }
            const rowIndex = params.node.rowIndex;
            const rowColumns = this.params.columnApi.getColumns() ?? [];
            if (rowIndex || rowIndex === 0) {
              this.gridApi.startEditingCell({ rowIndex, colKey: rowColumns[1]?.getColId() });
            }
          }
        })
  }

  onCancelEditRow(params: ICellRendererParams): void {
    this.timetableSpecificationsService.getSubjectRelationshipsList(this.timetableId)
      .subscribe(data => {
        this.gridData = data;
        this.setRowData();
      })
  }

  onDeleteRowClicked(params: ICellRendererParams): void {
    this.deleteRowId = params.data.id;
    this.deleteSubjectRelationshipWarningModal.show();
  }

  onDeleteRow(): void {
    this.deleteSubjectRelationshipWarningModal.hide();
    this.timetableSpecificationsService.deleteSubjectRelationship(this.deleteRowId)
      .pipe(switchMap(() => this.timetableSpecificationsService.getSubjectRelationshipsList(this.timetableId)))
      .subscribe(data => {
        this.gridData = data;
        this.setRowData();
        this.snackbar.success(this.translate.instant('Successful operation'));
      })
  }

  onExcludeRow(params: ICellRendererParams): void {
    this.excludeRowIds = [params.data.id];
    this.excludeSubjectRelationshipWarningModal.show();
  }

  onIncludeRow(params: ICellRendererParams): void {
    this.excludeIncludeRequest({ isExcluded: false, ids: [params.data.id] })
  }

  excludeIncludeBulk(isExcluded: boolean): void {
    this.gridApi.stopEditing();
    this.gridOptions.api?.setPinnedBottomRowData([{}]);
    const ids = this.gridApi.getSelectedRows().map(row => row.id);
    if (!isExcluded) {
      this.excludeIncludeRequest({ isExcluded, ids })
    } else {
      this.excludeRowIds = ids;
      this.excludeSubjectRelationshipWarningModal.show();
    }
  }

  excludeIncludeRequest(data: { isExcluded: boolean, ids: number[] }) {
    this.excludeSubjectRelationshipWarningModal?.modalComponentRef?.nativeElement.hide();
    this.timetableSpecificationsService.excludeSubjectRelationship(data)
      .pipe(switchMap(() => this.timetableSpecificationsService.getSubjectRelationshipsList(this.timetableId)))
      .subscribe(data => {
        this.gridData = data;
        this.setRowData();
        this.snackbar.success(this.translate.instant('Successful operation'));
      })
  }

  viewTypeChange(event: Event): void {
    this.gridApi.stopEditing();
    if ((event as CustomEvent).detail.innerText.toLowerCase() === INCLUDED_TYPES.ACTIVE) {
      this.viewType = INCLUDED_TYPES.ACTIVE
      this.gridOptions.api?.setPinnedBottomRowData([{}]);
    } else {
      this.viewType = INCLUDED_TYPES.EXCLUDED
      this.gridOptions.api?.setPinnedBottomRowData([]);
    }
    this.setRowData();
  }

  setRowData(): void {
    this.viewType === INCLUDED_TYPES.ACTIVE
      ? setTimeout(() => {
        this.gridApi?.setRowData(this.gridData.filter(data => !data.isExcluded));
        this.gridApi.redrawRows();
      }, 0)
      : setTimeout(() => {
        this.gridApi?.setRowData(this.gridData.filter(data => data.isExcluded));
        this.gridApi.redrawRows();
      }, 0)
  }
}
