@import '../../../../../../assets/styles/variables';

::ng-deep .staffing {
  height: 100%;

  .action-row {
    display: flex;
    justify-content: space-between;

    .left {
      display: flex;

      .search {
        width: 256px;
        margin-right: 8px;
      }
    }
  }

  .flex-row {
    display: flex;
    flex-wrap: wrap;

    .chip-container {
      margin: 0 4px 8px 0
    }
  }

  .ag-grid-container {
    height: calc(100% - 40px);

    &.is-filter-applied {
      // 30 - flex-row - chips height appears
      max-height: calc(100% - 40px - 30px);
    }
  }

  .bcm-input .bcm-input__container {
    max-height: 31px !important;
  }

  .ag-cell-inline-editing {
    height: 33px !important;

    .ag-input-field-input.ag-text-field-input {
      text-align: center;
    }

    input {
      padding-left: 0 !important;
    }
  }

  .ag-cell.center-cell {
    display: flex;
    justify-content: space-around;
  }

  .actions {
    width: 100%;
  }

  .ag-floating-bottom-container .ag-cell.center-cell,
  .ag-pinned-right-floating-bottom .ag-cell.center-cell {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ag-header-cell.center-cell {
    text-align: center !important;
  }

  .ag-cell.center-cell-pinned {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ag-cell.right-cell {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .ag-row.gray {
    background-color: $color-blue-grey-100 !important;
  }

  .center-cell .ag-header-cell-label {
    flex: auto;
    justify-content: center;
  }

  .red {
    color: $color-red-tertiary-500;
    font-weight: bold;
  }

  .green {
    color: $color-emerald-500;
    font-weight: bold;
  }

  .ag-horizontal-left-spacer, .ag-horizontal-right-spacer {
    overflow: hidden;
  }
}
