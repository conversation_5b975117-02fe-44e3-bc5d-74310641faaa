<div class="tts-schedule-order-container">
  <div class="action-bar">
    <bromcom-input-field class="search-field"
                         [formControl]="searchControl"
                         [icon]="'fal fa-search'"
                         [placeholder]="'Search' | translate "
    ></bromcom-input-field>

    <bcm-button class="row-groups-button" kind="ghost"
                [matMenuTriggerFor]="filters">
      <bcm-icon class="icon" icon="far fa-plus"></bcm-icon>

      {{'Create Row Groups' | translate}}
      <bcm-icon class="icon" icon="fas fa-angle-down"></bcm-icon>

    </bcm-button>
  </div>

  <div class="table-container">
    <div class="flex-row">
      <div class="chip-container" *ngFor="let filterValue of filterValues, let index = index">
        <bcm-icon *ngIf="index !== 0" class="icon" icon="far fa-angle-right" slot="suffix"></bcm-icon>
        <bcm-chip color="green" size="large">{{filterValue.text}}</bcm-chip>
      </div>
    </div>

    <ag-grid-angular *ngIf="gridActivated"
                     style="width: 100%; height:calc(100% - 30px);"
                     id="tts-ag-grid"
                     class="ag-theme-alpine ag-grid"
                     domLayout="normal"
                     [gridOptions]="gridOptions"
                     (gridReady)="onGridReady($event)"
                     (cellValueChanged)="onCellValueChanged($event)">
    </ag-grid-angular>
  </div>
</div>

<mat-menu #filters="matMenu">
  <div class="create-row-groups" (click)="$event.stopPropagation()">
    <bromcom-filter
      [dataItems]="{items: rowGroupFilterOptions, startState: false}"
      [selection]="selectedGroupItemIds"
      [showHeader]="true"
      [showSearchBar]="false"
      [showActions]="false"
      [appearance]="'switch'"
      [selectAllOnClearedApply]="false"
      (filterData)="rowGroupFilterChanged($event)"
      (click)="$event.stopPropagation()"
    ></bromcom-filter>
  </div>
</mat-menu>
