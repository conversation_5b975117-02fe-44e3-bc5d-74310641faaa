@import "../../../../../../../../assets/styles/variables";

.block {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: $color-blue-grey-200;
  border: 1px solid $color-blue-grey-200;
  border-radius: 4px;
  width: 121px !important;
  font-style: normal;
  font-weight: 500;
  font-size: 13px;
  line-height: 22px;
  color: $color-blue-grey-600;
  cursor: pointer;

  .icon {
    margin-top: 4px;
    font-size: 16px;
  }

  &.active {
    border: 2px dashed $color-blue-800;
  }

  &.inactive {
    background: $color-blue-grey-200;
    border: 1px solid $color-blue-grey-200;
    font-style: normal;
    font-weight: 500;
    font-size: 13px;
    line-height: 22px;
    color: $color-blue-grey-400;

    img {
      opacity: 0.5;
    }

    .circle {
      opacity: 0.5;
    }
  }

  .header {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 24px;
    align-items: center;
    padding: 0 8px 0 16px;
    background: $color-blue-grey-200;
    border-bottom: 1px solid $color-blue-grey-200;
    border-radius: 4px 0 0 0;
    justify-content: space-between;

    .actions {
      display: flex;
      align-items: center;
      text-align: center;
    }

    .circle {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 18px;
      height: 18px;
      background: $color-blue-grey-50;
      border: 1px solid $color-white-0;
      border-radius: 24px;
      font-style: normal;
      font-weight: 500;
      font-size: 10px;
      line-height: 18px;
      color: $color-blue-grey-600;
    }
  }

  .block-code, .band, .cycle, .type {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2px 0;
    width: 100%;
    background: $color-blue-grey-100;
    border-top: 1px solid $color-blue-grey-200;
    border-bottom: 1px solid $color-blue-grey-200;
    border-radius: 0;
  }

  .band {
    text-align: center;
    width: 119px;
    padding: 0 6px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .cycle {
    border-bottom: none;
  }

  .type {
    display: inline-block;
    padding-left: 5px;
    padding-right: 5px;

    .block-name {
      align-items: center;
      text-align: center;
      justify-content: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-height: 22px;

      ::ng-deep bcm-tooltip {
        display: inline-block;
        width: 100%;
        align-items: center;
        text-align: center;
        justify-content: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .subjects {
      display: flex;
      flex-direction: row;
      text-align: center;
      align-items: center;
      justify-content: center;

      .empty {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 34px;
      }

      .subject-circle {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 0;
        width: 12px;
        height: 12px;
        margin: 11px 2px;
        border-radius: 24px;
        border: 1px solid $color-blue-grey-400;
      }
    }
  }
}

::ng-deep .block-tooltip-wrapper {
  display: flex;
  margin-top: 6px;
  border-top: 2px solid $color-blue-grey-600;
  flex-direction: column;
  align-items: center;

  ::ng-deep .block-tooltip-line {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;

    ::ng-deep .block-tooltip-circle {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 0;
      width: 12px;
      height: 12px;
      margin: 11px 4px;
      border-radius: 24px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}

::ng-deep .block-modal {

  .block-vertical {
    display: flex;
    flex-direction: column;
    padding: 12px;

    .bcm-button {
      width: 100%;
    }

    .bcm-button__container {
      width: 100%;
      justify-content: flex-start;

      &:hover {
        background-color: $color-blue-grey-50;
      }
    }

    .bcm-button__container-text {
      color: $color-blue-700;
    }
  }

  &.tw-z-tooltip {
    z-index: 10
  }
}
