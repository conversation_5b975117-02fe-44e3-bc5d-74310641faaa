import { ChangeDetectorRef, Component, ElementRef, Input, OnChanges, ViewChild } from '@angular/core';
import { BaseModalComponent } from '../../../../../../../../../_shared/components/BaseModalComponent';
import { IReportPreview } from '../../../../../../../../../_shared/models/IStaffReportEntity';
import { IStaffToSubjectRelationData } from '../../../../../../../../../_shared/models/IStaffToSubjectRelationData';
import { ColDef, GridApi, GridReadyEvent } from 'ag-grid-community';

@Component({
  selector: 'bromcom-reports-subject-timetables-preview',
  templateUrl: './reports-subject-timetables-preview.component.html',
  styleUrls: ['./reports-subject-timetables-preview.component.scss'],
})
export class ReportsSubjectTimetablesPreviewComponent extends BaseModalComponent {

  constructor(private cdr: ChangeDetectorRef) {
    super();
  }

  @Input() previewData!: IReportPreview;
  @Input() staffList!: IStaffToSubjectRelationData[];

  @ViewChild('previewModal') previewModalRef!: ElementRef;
  @ViewChild('container') containerref!: ElementRef;
  

  columnDefs: ColDef[] = [];
  rowData: any[] = [];
  gridApi!: GridApi;

  // Timetable cell renderer component
  timetableCellRenderer = (params: any) => {
    if (!params.value) return '';

    const { classCode, room, type } = params.value;

    if (type === 'PLN') {
      return `<div class="timetable-cell planning-cell">PLN</div>`;
    }

    const colorClass = this.getColorClass(classCode);
    return `
      <div class="timetable-cell ${colorClass}">
        <div class="class-code">${classCode}</div>
        <div class="room-info">${room || ''}</div>
      </div>
    `;
  };

  getColorClass(classCode: string): string {
    if (!classCode) return '';

    // Extract year group from class code (e.g., "9-A-EN-01" -> "9")
    const yearGroup = classCode.split('-')[0];

    switch (yearGroup) {
      case '7': return 'year-7';
      case '8': return 'year-8';
      case '9': return 'year-9';
      case '10': return 'year-10';
      case '11': return 'year-11';
      default: return 'default-color';
    }
  }

  onGridReady(params: GridReadyEvent): void {
    this.gridApi = params.api;
  }

  buildGridData(): void {
    const { staffReportEntities, periodNames } = this.previewData;

    const periodKeys = Object.keys(periodNames).sort((a, b) => +a - +b);
    const periodHeaders = periodKeys.map(k => periodNames[+k]);

    // Build column definitions
    this.columnDefs = [
      {
        headerName: '',
        field: 'groupType',
        width: 100,
        cellClass: 'group-cell',
        rowSpan: (params: any) => params.data?.rowSpan || 1,
        cellClassRules: {
          'department-cell': (params: any) => params.data?.groupType && params.data.groupType !== '',
          'subject-cell': (params: any) => params.data?.isSubject
        }
      },
      {
        headerName: '',
        field: 'staffName',
        width: 120,
        cellClass: 'staff-cell'
      },
      ...periodHeaders.map(period => ({
        headerName: period,
        field: period.toLowerCase().replace(/\s+/g, '_'),
        width: 100,
        cellRenderer: this.timetableCellRenderer,
        cellClass: 'period-cell'
      }))
    ];

    // Build row data with hierarchical structure
    this.rowData = this.buildHierarchicalData(staffReportEntities, periodHeaders);
  }

  buildHierarchicalData(staffReportEntities: any[], periodHeaders: string[]): any[] {
    const rows: any[] = [];

    // Group staff by department and subject
    const departmentGroups = this.groupStaffByDepartment(staffReportEntities);

    Object.keys(departmentGroups).forEach(departmentName => {
      const departmentStaff = departmentGroups[departmentName];
      const subjectGroups = this.groupStaffBySubject(departmentStaff);

      let departmentRowSpan = 0;
      const subjectRows: any[] = [];

      Object.keys(subjectGroups).forEach(subjectName => {
        const subjectStaff = subjectGroups[subjectName];

        // Add subject header row
        subjectRows.push({
          groupType: subjectName,
          staffName: '',
          isSubject: true,
          ...this.generateEmptyPeriods(periodHeaders)
        });

        // Add staff rows for this subject
        subjectStaff.forEach(staff => {
          subjectRows.push({
            groupType: '',
            staffName: staff.staffName,
            isStaff: true,
            ...this.mockPeriodAssignments(periodHeaders)
          });
        });

        departmentRowSpan += subjectStaff.length + 1; // +1 for subject header
      });

      // Add department header row with rowSpan
      rows.push({
        groupType: departmentName,
        staffName: '',
        isDepartment: true,
        rowSpan: departmentRowSpan,
        ...this.generateEmptyPeriods(periodHeaders)
      });

      // Add all subject and staff rows
      rows.push(...subjectRows);
    });

    return rows;
  }

  groupStaffByDepartment(staffReportEntities: any[]): { [key: string]: any[] } {
    return staffReportEntities.reduce((acc, staff) => {
      const department = this.findDepartmentForStaff(staff.staffId);
      if (!acc[department]) {
        acc[department] = [];
      }
      acc[department].push(staff);
      return acc;
    }, {} as { [key: string]: any[] });
  }

  groupStaffBySubject(staffList: any[]): { [key: string]: any[] } {
    // For now, we'll create mock subjects. In real implementation,
    // this would group by actual subject data
    return staffList.reduce((acc, staff, index) => {
      const subjectKey = `Subject ${Math.floor(index / 2) + 1}`;
      if (!acc[subjectKey]) {
        acc[subjectKey] = [];
      }
      acc[subjectKey].push(staff);
      return acc;
    }, {} as { [key: string]: any[] });
  }

  generateEmptyPeriods(periodHeaders: string[]): { [key: string]: any } {
    return periodHeaders.reduce((acc, period) => {
      const fieldName = period.toLowerCase().replace(/\s+/g, '_');
      acc[fieldName] = null;
      return acc;
    }, {} as { [key: string]: any });
  }

  findDepartmentForStaff(staffId?: number): string {
    const staff = this.staffList.find(s => s.id === staffId);
    return staff?.departments?.[0]?.departmentName ?? 'Department';
  }

  mockPeriodAssignments(periodHeaders: string[]): { [key: string]: any } {
    const classOptions = [
      { classCode: '7-A-EN-01', room: '', type: 'class' },
      { classCode: '8-B-EN-01', room: '', type: 'class' },
      { classCode: '9-A-FR-01', room: 'E4', type: 'class' },
      { classCode: '9-B-FR-01', room: 'E4', type: 'class' },
      { classCode: '8-B-FR-01', room: 'E4', type: 'class' },
      { classCode: '8-C-FR-01', room: 'K02', type: 'class' },
      { classCode: '11-C-EN-01', room: '', type: 'class' },
      { classCode: '11-C-EN-01', room: '', type: 'class' },
      { classCode: '9-B-EN-01', room: '', type: 'class' },
      { classCode: '11-C-FR-01', room: 'K02', type: 'class' },
      { classCode: '8-C-FR-01', room: 'K02', type: 'class' },
      { type: 'PLN' },
      null
    ];

    return periodHeaders.reduce((acc, period) => {
      const fieldName = period.toLowerCase().replace(/\s+/g, '_');
      const randomOption = classOptions[Math.floor(Math.random() * classOptions.length)];
      acc[fieldName] = randomOption;
      return acc;
    }, {} as { [key: string]: any });
  }

  show(): void{
    this.isOpen = true;
    this.cdr.detectChanges();
    setTimeout(() => {
      if (this.previewData && this.staffList) {
        this.buildGridData();
      }
      if (this.previewModalRef) {
      this.previewModalRef.nativeElement.show();
    }
    }, 100)
  }

  hide(): void{
    this.isOpen = false;
  }
}