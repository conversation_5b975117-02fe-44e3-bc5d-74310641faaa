import { Component, ElementRef, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { skip } from 'rxjs';

@Component({
  selector: 'bromcom-default-rules-list',
  templateUrl: './default-rules-list.component.html',
  styleUrls: ['./default-rules-list.component.scss'],
})
export class DefaultRulesListComponent implements OnChanges {

  listGroup = new FormGroup({
    items: new FormArray<FormControl<boolean | null>>([])
  });
  _dataItems: any[] = [];
  filteredDataItems: any[] = [];
  @ViewChild('defaultFilterPopup', { static: false }) defaultFilterPopup!: ElementRef;
  @Input() selectedItemIds: number[] = [];
  @Input() isOpen = false;
  @Input() set selection (data: number[] | null) {
    this.selectedItemIds = data || [];
  }

  @Input() set dataItems(data: {items: any[]}) {
      this._dataItems = data.items;
      this.initializeFormControls();
  }

  @Output() filterData = new EventEmitter();
  @Output() closeFilter = new EventEmitter<boolean>();

  ngOnChanges(changes: SimpleChanges): void {
    const change = changes['isOpen'];
    const selectedItemChange = changes['selectedItemIds'];

    if (change) {
      this.isOpen = change.currentValue;
    }
    if (selectedItemChange) {
      this._dataItems?.forEach((item) => {
        const index = this._dataItems.findIndex((data) => data.id === item.id);
        this.listGroup.controls.items.controls[index]?.setValue(this.selectedItemIds.includes(item.id));
      });
    }
  }

  initializeFormControls() {
    const itemsArray = this.listGroup.controls.items as FormArray;
    const tmp: Set<number> = new Set();
    if (itemsArray && this._dataItems?.length) {
      itemsArray.clear();
      this._dataItems?.forEach((data) => {
          tmp.add(data.id);
        const control = new FormControl((this.selectedItemIds.includes(data.id)));
        itemsArray.push(control);

        control.valueChanges.pipe(skip(2)).subscribe((value) => {
          this.filterData.emit({ id:data.id,value: !value});
        });
      });
      this.selectedItemIds = [...tmp];
    }
  }
}
