import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { ISubject } from "../../../../../../../_shared/models/ISubject";
import { IListOfBlocksResponse } from "../../../../../../../_shared/models/IListOfBlocksResponse";
import { IAddToGroupEvent } from "../../../../../../../_shared/models/IAddToGroupEvent";
import { BLOCK_TYPE } from "../../../../../../../_shared/enums/BlockType";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SubjectInformationService } from "../../../../../../services/subject-information.service";
import { Subject, debounceTime, takeUntil } from "rxjs";
import { SnackbarService } from '@bromcom/ui';
import { TranslateService } from '@ngx-translate/core';
import { ISubjectToYearGroup } from '../../../../../../../_shared/models/ISubjectToYearGroup';

@Component({
  selector: 'bromcom-box-drag',
  templateUrl: './drag-and-drop-simple-dragbox.component.html',
  styleUrls: ['./drag-and-drop-simple-dragbox.component.scss']
})
export class DragAndDropSimpleDragboxComponent implements OnInit, OnDestroy {
  @Input() set item(value: IListOfBlocksResponse) {
    this._item = value;
    this.formGroup.controls.classCount.setValue(value.subjectToYearGroups[0].classCount.toString());
    this.formGroup.controls.periodCount.setValue(value.subjectToYearGroups[0].periodCount.toString());
  }

  @Input() index!: number;
  @Input() linearGroups!: IListOfBlocksResponse[];
  @Input() optionsGroups!: IListOfBlocksResponse[];
  @Input() subjects!: ISubject[];
  @Output() removeEvent: EventEmitter<IListOfBlocksResponse> = new EventEmitter();
  @Output() addSubjectToYearGroupToLinearGroupEvent: EventEmitter<IAddToGroupEvent> = new EventEmitter();
  @Output() addSubjectToYearGroupToOptionsGroupEvent: EventEmitter<IAddToGroupEvent> = new EventEmitter();
  @Output() updateSubjectInfoEvent: EventEmitter<void> = new EventEmitter();
  @Output() updateSubjectToYearGroupEvent: EventEmitter<IListOfBlocksResponse> = new EventEmitter();
  @Output() copyBlockEvent: EventEmitter<IListOfBlocksResponse> = new EventEmitter();

  _item!: IListOfBlocksResponse;
  active = false;
  formGroup = new FormGroup({
    classCount: new FormControl('', Validators.required),
    periodCount: new FormControl('', Validators.required)
  });
  latestPayload!: ISubjectToYearGroup;
  private readonly unsubscribe$: Subject<void> = new Subject();

  constructor(private subjectInformationService: SubjectInformationService,
              private snackbar: SnackbarService,
              private translate: TranslateService) {
  }

  ngOnInit(): void {
    this.latestPayload = this._item.subjectToYearGroups[0];
    this.formGroup.valueChanges
      .pipe(
        debounceTime(300),
        takeUntil(this.unsubscribe$))
      .subscribe(val => {
        if (this.formGroup.controls.periodCount.value && this.formGroup.controls.classCount.value) {
        const payload = {
          ...this._item.subjectToYearGroups[0],
          periodCount: val.periodCount && +val.periodCount > 0 ? +val.periodCount : 1,
          classCount: val.classCount && +val.classCount > 0 ? +val.classCount : 1
        };
        if (val.classCount && +val.classCount == 0) {
          this.formGroup.controls.classCount.setValue('1', { onlySelf: true });
        }
        if (val.periodCount && +val.periodCount == 0) {
          this.formGroup.controls.periodCount.setValue('1', { onlySelf: true });
        }
        this.formGroup.updateValueAndValidity({ emitEvent: false, onlySelf: true });
        this.subjectInformationService.updateSubjectToYearGroup(payload).subscribe(() => {
          this.updateSubjectInfoEvent.emit();
        });
        const tmp = { ...this._item };
        tmp.subjectToYearGroups[0] = payload;
        this.updateSubjectToYearGroupEvent.emit(tmp);
        if ((this.latestPayload.classCount !== payload.classCount) || (this.latestPayload.periodCount !== payload.periodCount)) {
          this.snackbar.success(this.translate.instant('Updated successfully'));
        }
        this.latestPayload = payload;
        }
      })
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  remove(): void {
    this.removeEvent.emit(this._item);
  }

  addSubjectToYearGroupToLinearGroup(targetIndex: number): void {
    this.addSubjectToYearGroupToLinearGroupEvent.emit({
      subject: this._item.subjectToYearGroups[0],
      targetBlockId: this.linearGroups[targetIndex].id,
      type: BLOCK_TYPE.Linear,
      previousContainerData: { blockTypeId: BLOCK_TYPE.Simple },
    } as IAddToGroupEvent);
  }

  addSubjectToYearGroupToOptionsGroup(targetIndex: number): void {
    this.addSubjectToYearGroupToOptionsGroupEvent.emit({
      subject: this._item.subjectToYearGroups[0],
      targetBlockId: this.optionsGroups[targetIndex].id,
      previousContainerData: { blockTypeId: BLOCK_TYPE.Simple },
      type: BLOCK_TYPE.Options
    } as IAddToGroupEvent);
  }

  getColor(color: string): string {
    return `#${color}`;
  }

  getSubject(subjectId: number): ISubject | undefined {
    return this.subjects.find(subject => subject.id === subjectId);
  }

  getBoxText(): string {
    const subjectId = this._item.subjectToYearGroups[0].subjectId;
    const subject = this.getSubject(subjectId);
    return `${subject?.code.toUpperCase()} - ${subject?.name}`;
  }

  onClassCountInputBlur() {
    setTimeout(() => {
      if (!this.formGroup.controls.classCount.value) {
        this.formGroup.controls.classCount.setValue('1', { emitEvent: false, onlySelf: true });
        if (this.formGroup.controls.classCount.value !== this.latestPayload.classCount.toString()) {
          this.snackbar.success(this.translate.instant('Updated successfully'));
        }
        this.latestPayload.classCount = 1;
      } else if (this.formGroup.controls.classCount.value && this.formGroup.controls.classCount.value !== this.latestPayload.classCount.toString()) {
        this.snackbar.success(this.translate.instant('Updated successfully'));
      }
    }, 500);
  }

  onPeriodCountInputBlur() {
    setTimeout(() => {
      if (!this.formGroup.controls.periodCount.value) {
        this.formGroup.controls.periodCount.setValue('1', { emitEvent: false, onlySelf: true });
        if (this.formGroup.controls.periodCount.value !== this.latestPayload.periodCount.toString()) {
          this.snackbar.success(this.translate.instant('Updated successfully'));
        }
        this.latestPayload.periodCount = 1;
      } else if (this.formGroup.controls.periodCount.value && this.formGroup.controls.periodCount.value !== this.latestPayload.periodCount.toString()) {
        this.snackbar.success(this.translate.instant('Updated successfully'));
      }
    }, 500);
  }

  copyBlock(): void {
    this.copyBlockEvent.emit(this._item);
  }
}
