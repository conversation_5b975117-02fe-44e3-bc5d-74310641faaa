<ng-container *ngIf="isOpen">
  <bcm-modal size="xxxlarge" class="check-for-missing-modal"
    (bcm-modal-before-close)="onClose()"
            #checkForMissingModal>
    <bcm-modal-header>{{ 'Check for Missing Staff/Rooms' | translate }}</bcm-modal-header>

    <div class="modal-body">
      <div class="action-bar">
        <bromcom-input-field class="search-field"
                            [formControl]="searchControl"
                            [icon]="'fal fa-search'"
                            [placeholder]="'Search' | translate "
        ></bromcom-input-field>

        <div class="action-buttons">
          <bromcom-switch class="switch"
                          [formControl]="showAllYearGroupsControl"
                          [label]="'All Year Groups' | translate">
          </bromcom-switch>

          <bcm-button class="button" icon="far fa-file-export" kind="ghost" (click)="exportMissingStaffRoom()"
          >{{'Export' | translate }}</bcm-button>

          <bcm-button-group class="button-group" type="radio" (bcm-click)="viewTypeChange($event)">
            <bcm-button kind="ghost" icon="far fa-user"
                        value="{{viewTypes.Staff}}"
                        checked="{{viewType === viewTypes.Staff}}"
            >{{'Staff' | translate }}</bcm-button>
            <bcm-button kind="ghost" icon="far fa-school"
                        value="{{viewTypes.Room}}"
                        checked="{{viewType === viewTypes.Room}}"
            >{{'Room' | translate }}</bcm-button>
          </bcm-button-group>
        </div>
      </div>

      <div class="table-container">
        <ag-grid-angular style="width: 100%; height:100%;"
                        class="ag-theme-alpine"
                        domLayout="normal"
                        [gridOptions]="gridOptions"
                        (gridReady)="onGridReady($event)">
        </ag-grid-angular>
      </div>
    </div>
  </bcm-modal>
</ng-container>