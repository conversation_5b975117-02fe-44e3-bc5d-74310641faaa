@import "apps/bromcom-timetables/src/assets/styles/variables";

@mixin displayFlexRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.display-timetable-modal {
  z-index: 10;

  ::ng-deep .bcm-modal__container.large {
    max-width: fit-content;
  }

  .modal-body {
    padding: 24px;

    .action-bar {
      @include displayFlexRow;
      flex-wrap: wrap;
      margin-bottom: 8px;

      .left-side {
        @include displayFlexRow;
        width: 450px;

        .data-selector {
          max-width: 256px;
          width: 256px;
        }
      }
    }

    .display-timetable {
      position: relative;
      height: calc(100% - 84px);

      .timetable-holder {
        overflow-x: auto;
      }

      table {
        border-collapse: collapse;
        margin: 8px 0 8px 0;
      }

      .week-label {
        color: $color-blue-grey-700;
        font-size: 14px;
      }

      .settings-cog {
        width: 93px;
        height: 24px;
      }

      .period-cell {

        &:not(.isBreak) {
          width: 142.4px !important;
          min-width: 142.4px !important;
          max-width: 142.4px !important;
        }

        position: relative;
        overflow: hidden;

        &.isBreak {
          width: 96px;
        }

        &.isGreyedOut {
          background: $color-blue-grey-100 !important;
        }
      }

      .period-header {
        height: 24px;
        position: relative;
        &:not(.isBreak) {
          width: 142.4px;
        }

        &.isBreak {
          width: 96px !important;
        }
      }

      .day-header {
        width: 93px;
        height: 52px;
        background-color: $color-blue-grey-100;
      }

      th, td {
        border: 1px solid $color-blue-grey-200;
        text-align: center;
        color: $color-blue-grey-600;
        font-size: 13px;

        &.isBreak:not(.isGreyedOut) {
          background-color: $color-blue-grey-900 !important;

          .period-holder {
            width: 96px !important;
          }
        }

        .period-holder {
          max-width: 142.4px;
          gap: 1px;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          align-items: center;
          justify-content: start;

          &.flex-column {
            flex: 1;
          }

          &.isNonContactCodeHolder {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 100%;
          }

          &.isConflict {
            border: 2px dashed $color-red-tertiary-500;
          }

          .empty-cell {
            height: 100%;
            width: 100%;
            display: flex;
            position: absolute;

            &.isSelected {
              border: 2px dashed $color-blue-800;
            }
          }

          .sessionClass {
            height: 52px;
            display: flex;
            max-width: 142.4px;
            overflow: hidden;
            gap: 1px;

            .session {
              display: inline-block;
              height: 100%;
              width: 142.4px;

              .row {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 24px;
                padding: 4px 10px;

                .session-name {
                  text-align: center;
                  font-size: 14px;
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }

                .session-location, .session-staff {
                  width: 50%;
                  text-align: start;
                  font-size: 12px;
                  align-items: center;
                  gap: 16px;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  display: inline-block;
                  white-space: nowrap;

                  i {
                    margin-right: 6px;
                  }
                }

                .session-subject {
                  width: 50%;
                  text-align: center;
                  display: inline-block;
                  font-size: 12px;
                  align-items: center;
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                }
              }
            }
          }

          .non-contact-class {
            height: 100%;
            width: 100%;
            display: flex;
            flex-direction: column;
            padding: 4px 10px 2px 10px;
            justify-content: center;
            align-items: center;
            background: $color-grey-600;
            position: relative;

            .non-contact-holder {
              height: 100%;
              width: 100%;
              display: inline-flex;
              color: $color-white-0;
              font-size: 14px;
              align-items: center;
              justify-content: center;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;

              &.showRoomDetails {
                display: inline-block !important;
              }
            }

            .room-detail-holder {
              position: absolute;
              bottom: 0px;
              color: $color-white-0;
              width: 100%;
              text-align: start;
              font-size: 12px;
              align-items: center;
              text-overflow: ellipsis;
              overflow: hidden;
              display: inline-flex;
              padding: 4px 10px;

              .nc-location {
                display: inline-flex;
                align-items: center;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;

                i {
                  margin-right: 6px;
                  margin-left: 1px;
                }
              }
            }
          }
        }
      }

      th {
        background-color: $color-blue-grey-100;
      }

      tr {
        border-bottom: 1px solid $color-blue-grey-200;
      }

      td:first-child {
        border-right: 1px solid $color-blue-grey-200;
      }
    }
  }

  .tooltip-container{
    position: relative;
    top: 0px;
    right: 0px;
  }

  .tooltip-container span {
    width: 15px;
    height: 15px;
    line-height: 15px;
    border-radius: 50%;
    font-size: 10px;
    color: #fff;
    text-align: center;
    background: #374151;
  }

  .cus-tooltip {
    position: relative;
    display: inline-block;
    position: absolute;
    top: 0;
    right: 3px;
  }
  
  .cus-tooltip .cus-tooltiptext {
    visibility: hidden;
    background-color: #374151;
    color: #fff;
    text-align: center;
    border-radius: 4px;
    padding: 2px 7px 3px 7px;
    position: absolute;
    z-index: 1;
    bottom: 120%;
    left: 50%;
    opacity: 0;
    transition: opacity 0.3s;
    transform: translateX(-50%);
    min-width: 110px;
  }
  .cus-tooltip .cus-tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #374151 transparent transparent transparent;
  }
  
  .cus-tooltip:hover .cus-tooltiptext {
    visibility: visible;
    opacity: 1;
  }

  .cus-tooltip .cus-tooltip-icon {
    width: 15px;
    height: 15px;
    line-height: 15px;
    border-radius: 50%;
    font-size: 10px;
    color: #fff;
    text-align: center;
    background: #374151;
    display: inline-block;
  }
}

::ng-deep .display-timetable-modal {
  .bcm-modal__container {
    width: fit-content !important;
    max-width: fit-content !important;
  }
}
