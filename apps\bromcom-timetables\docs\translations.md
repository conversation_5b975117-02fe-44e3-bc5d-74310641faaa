# Translation

## How to prepare the translation

1. Get text from the application
2. Add new language
3. Create `json` files from `po`


## Add new language

Run `npm run timetables:i18next-create`


* `locale id`: xx_XX ([Check LCID here](https://www.science.co.il/language/Locale-codes.php))
* `output file`: xx.po

Example:
```
  Specify the local id (xx_XX): en_HU
  Specify the output file (xx.po): en.po
  Created ../apps/bromcom-timetables/src/translations/en.po.
```

## Get text from project

- Run `npm run timetables:i18next-gettext`

This step will create/upgrade `template.pot` file from the project. Always `template.pot` is the base language file.

Example:
```
  Did write .pot contents to ./apps/bromcom-timetables/src/translations/en.po
  Done in 1.02s.
```

HINTS:
 - Options for the grunt method:
https://www.npmjs.com/package/angular-gettext-tools


## Update all `po` files

Preparation:
* Copy all updated `po` files into the `./apps/bromcom-timetables/src/translations_upgrade` folder

Run `npm run timetables:i18next-update`

The process reads all file from the source (`./apps/bromcom-timetables/src/translations`) folder and the upgrade (`./apps/bromcom-timetables/src/translations_upgrade`)
folder, so it updates (and merges) the language po files from `en.po` file. After this process, the upgrade files
has been removed from upgrade folder.

Example:
```
  UPDATING en.po
  ................................................ done.
  UPGRADING en.po
  REMOVED UPGRADE FILE

  UPDATING fr.po
  .................................................. done.

  UPDATING de.po
  ................................................... done.

  UPDATING it.po
  ................................................... done.
```

## Update a `po` file

Preparation:
* Copy the updated `po` file into the `./apps/bromcom-timetables/src/translations_upgrade` folder

Run `npm run timetables:i18next-update-file`

The process reads a file from the source (`src/translations`) folder and the upgrade (`src/translations_upgrade`)
folder, so it updates (and merges) the language po file from `en.po` file. After this process, the upgrade file
has been removed from upgrade folder.

* `output file`: xx.po

Example:
```
  Specify the file (en.po): en.po
  UPDATING en.po
  .............................................. done.
  UPGRADING en.po
  REMOVED UPGRADE FILE
```

## Create `json` files from `po`

Run `npm run timetables:i18next-conv`

This step read all `po` files and convert to the `json` file

Example:
```
Processing file: apps/bromcom-timetables/src/translations/en.po -> apps/bromcom-timetables/src/translations/en.json
Processing file: apps/bromcom-timetables/src/translations/fr.po -> apps/bromcom-timetables/src/translations/fr.json
Processing file: apps/bromcom-timetables/src/translations/de.po -> apps/bromcom-timetables/src/translations/de.json

File written: apps/bromcom-timetables/src/translations/en.json

File written: apps/bromcom-timetables/src/translations/fr.json

File written: apps/bromcom-timetables/src/translations/de.json

```

## How to use it in the code

#### Pipe
```html
<p>{{'This is a duck on the picture' | translate}}</p>
```

#### Pipe with inline data
```html
<p>{{'This is a fish on the [[dog]] picture [[fish]]' | translate: {fish: 12, dog: 12} }}</p>
```

#### Pipe with predefined data
```typescript
get fish_params(): Record<string, number> {
  return {
    fish: this.fish_number
  }
}
```

```html
<p>{{'This is a tree on the picture [[fish]]' | translate: fish_params }}</p>
```

#### Inner html
```html
<p translate="There are [[value]] apples on the picture." [translateParams]="{value: '12'}"></p>

<p translate [translateParams]="{value: '55'}">There are [[value]] apples on the picture</p>
```

#### Use in the conroller
```typescript

constructor(
  private translate: TranslateService
) {
}

fish_number = 1;


get instant_text(): string {
  return this.translate.instant('There are [[value]] fish on the picture.', {
    value: this.fish_number
  })
}
```

```html
<input type="number" [(ngModel)]="fish_number">
    <p>{{instant_text}}</p>
</div>

```
