import { Component } from '@angular/core';
import { BaseDetailedBlock } from "../../_shared/base-detailed-block";
import { BLOCK_TYPE } from "../../../../../../../_shared/enums/BlockType";

@Component({
  selector: 'bromcom-detailed-block',
  templateUrl: './expanded-detailed-block.component.html',
  styleUrls: ['./expanded-detailed-block.component.scss']
})
export class ExpandedDetailedBlockComponent extends BaseDetailedBlock {
  readonly BLOCK_TYPE = BLOCK_TYPE;
}
