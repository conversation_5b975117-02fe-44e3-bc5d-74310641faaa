@import "apps/bromcom-timetables/src/assets/styles/variables";
@import "apps/bromcom-timetables/src/assets/styles/style-collector";

.staff-container {
  height: 100%;
  padding: 16px 0;

  ::ng-deep .staff-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;

    .bcm-switch {
      margin-bottom: 0 !important;
    }
  }

  .staff-header {
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    background: $color-blue-grey-100;
    border-radius: 8px;

    margin: 8px 0;
    padding: 4px;
    height: 40px;

    .drag-icon {
      width: 40px;
      text-align: center;
      position: relative;

      .dot {
        @include dot;
      }
    }

    .initials, .name, .subject, .assigned, .total {
      color: $color-blue-grey-700;
      font-weight: 500;
      padding: 0 8px;
      cursor: pointer;
    }

    .initials {
      width: 67px;
    }

    .name {
      width: 20%;
    }

    .subject {
      width: 20%;
      text-align: center;
      padding: 0 2px !important;

      i {
        position: absolute;
        margin-top: 3px;
        margin-left: 3px;
      }
    }

    .assigned {
      width: 23%;
      text-align: center;
      padding: 0 2px !important;
    }

    .total {
      width: 17%;
      text-align: center;

      i {
        position: absolute;
        margin-top: 3px;
        margin-left: 3px;
      }
    }
  }

  .staff-cards-container {
    // 32px - paddings 16+16
    // 32px - search input
    // 40px - edit staff button
    // 32px - staff type dropdown
    // 46px - correction
    height: calc(100% - 32px - 32px - 40px - 32px - 46px);
    overflow: auto;

    .no-data {
      min-height: 378px;
    }
  }

  .staff-cards-container::-webkit-scrollbar {
    display: none;
  }
}
