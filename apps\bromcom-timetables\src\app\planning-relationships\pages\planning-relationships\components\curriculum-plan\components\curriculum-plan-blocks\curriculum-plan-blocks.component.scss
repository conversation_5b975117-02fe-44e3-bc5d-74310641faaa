@import "apps/bromcom-timetables/src/assets/styles/variables";

.blocks-container {
  height: 100%;
  padding: 16px 0;

  .blocks-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;

    div > .action-button {
      margin-right: 8px;
      position: relative;

      ::ng-deep &.filter .bcm-button__container {
        .bcm-button__container-text {
          margin-right: -10px;
        }
      }

      .dot {
        width: 8px;
        height: 8px;
        padding: 0px 4px;
        border-radius: 24px;
        background: $color-primary-blue-600;
        position: absolute;
        top: 6px;
        right: 8px;
      }
    }
  }

  .blocks-body {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-content: flex-start;
    height: calc(100% - 32px - 32px);
    overflow: auto;

    .block {
      margin-right: 12px;
      margin-bottom: 16px;
    }
  }
}

::ng-deep .blocks-filter-popup {
  .filter-buttons {
    margin-top: 0 !important;
  }
}
