import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { ICurriculumPlanRoom } from '../../../../../../../_shared/models/ICurriculumPlanRoom';
import { v4 as uuidv4 } from 'uuid';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';
import { MatMenuTrigger } from '@angular/material/menu';

@Component({
  selector: 'bromcom-room-card',
  templateUrl: './room-card.component.html',
  styleUrls: ['./room-card.component.scss']
})
export class RoomCardComponent {
  @ViewChild(MatMenuTrigger) trigger!: MatMenuTrigger;
  @Input() room!: ICurriculumPlanRoom
  @Input() showActions = false;
  @Output() displayTimetable: EventEmitter<ICurriculumPlanRoom> = new EventEmitter();
  @Output() swapRoom: EventEmitter<ICurriculumPlanRoom> = new EventEmitter();
  index = uuidv4();

  constructor(private curriculumPlanBlocks: CurriculumPlanBlocksService) {
  }

  openRoomActions(): void {
    this.trigger.openMenu();
  }

  roomDragStarted(): void {
    this.curriculumPlanBlocks.isRoomDraggingActive$.next(this.room);
  }

  roomDragEnded(): void {
    this.curriculumPlanBlocks.isRoomDraggingActive$.next(null);
  }
  
  onDisplayTimetableClick(room: ICurriculumPlanRoom) {
    this.displayTimetable.emit(room);
  }
  
  onSwapRoomClick(room: ICurriculumPlanRoom) {
    this.swapRoom.emit(room);
  }
}
