import {Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { ISubject } from '../../../../../../../_shared/models/ISubject';
import { NestedTreeControl } from '@angular/cdk/tree';
import { MatTreeNestedDataSource } from '@angular/material/tree';
import { Subject, debounceTime, takeUntil, distinctUntilChanged, combineLatest } from 'rxjs';
import { CurriculumPlanService } from '../../../../../../services/curriculum-plan.service';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@bromcom/ui';
import { customComparator, IListOption } from '@bromcom/core';
import { AutoScheduleService } from '../../../../../../services/auto-schedule.service';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';
import { NOT_AVAILABLE } from '../../../../../../../_shared/enums/NotAvailable';
import { TitleHelperService } from "../../../../../../../_shared/services/title-helper.service";
import { GeneralModalComponent } from '../../../../../../../_shared/components/general-modal/general-modal.component';

interface Node {
  name: string;
  children?: Node[];
  code?: string;
  color?: string;
  index?: number;
}

export interface BlocksByYG {
  yearGroupId?: number
  blocks: BlockNode[]
}

export interface BlockTreeNode {
  name: string;
  children?: BlockTreeNode[];
  blockTypeId?: number;
  id?: number;
}

export interface BlockNode {
  blockId: number;
  blockName: string;
  blockTypeId: number;
}

@Component({
  selector: 'bromcom-auto-schedule-selector',
  templateUrl: './auto-schedule-selector.component.html',
  styleUrls: ['./auto-schedule-selector.component.scss']
})
export class AutoScheduleSelectorComponent implements OnInit, OnDestroy {
  @Input() timetableId!: number;
  @Input() projectId!: number;
  @Input() selectYearGroups: IListOption[] = [];
  @Output() changeToNextStep = new EventEmitter<number>();
  @Output() changeToPreviousStep = new EventEmitter<boolean>();
  @ViewChild('conflictsProcessTimetableModalComponent') conflictsProcessTimetableModalComponent! : GeneralModalComponent;
  @ViewChild('keepExistingScheduleRadioGroup') keepExistingScheduleRadioGroup!: ElementRef;
  @ViewChild('includeDraftNccRadioGroup') includeDraftNccRadioGroup!: ElementRef;
  keepExistingSchedule: boolean | null = null;
  includeDraftNcc: FormControl<boolean | null> = new FormControl(null);

  selectAllSubjects = new FormControl(false);
  selectAllBlocks = new FormControl({ value: false, disabled: true });
  searchControl = new FormControl('');
  blocksSearchControl = new FormControl({ value: '', disabled: true });
  selectYearGroupsForm = new FormControl<number[] | null>(null, Validators.required);
  listByForm = new FormControl(1);
  subjectsForm = new FormGroup({
    subjectList: new FormArray<FormControl<boolean>>([])
  });
  blocksForm = new FormGroup({
    blockList: new FormArray<FormControl<boolean>>([])
  });
  subjectList: ISubject[] = [];
  blocksList: BlockNode[] = [];

  listBy = [
    {
      id: 1,
      text: 'Subjects'
    },
    {
      id: 2,
      text: 'Departments'
    }
  ];

  treeControl = new NestedTreeControl<Node>((node) => node.children);
  blockTreeControl = new NestedTreeControl<BlockTreeNode>((node) => node.children);
  dataSource = new MatTreeNestedDataSource<Node>();
  blockDataSource = new MatTreeNestedDataSource<BlockTreeNode>();
  previouslySelectedBlockIds: number[] = [];
  alreadyRequestedBlockIds: number[] = [];
  TREE_DATA: Node[] = [];
  BLOCKS_TREE_DATA: Node[] = [];
  freeSessionSubject!: ISubject;
  readonly unsubscribe$: Subject<void> = new Subject();
  projectName = '';
  timetableName = '';
  isFirstGetBlocks = true;

  constructor(
    private curriculumPlanService: CurriculumPlanService,
    protected translate: TranslateService,
    private snackbar: SnackbarService,
    private autoScheduleService: AutoScheduleService,
    private curriculumPlanBlocksService: CurriculumPlanBlocksService,
    private titleHelperService: TitleHelperService
  ) {
  }

  ngOnInit(): void {
    this.selectAllSubjects.setValue(false);
    this.selectAllBlocks.setValue(false);
    this.searchControl.setValue('');
    this.blocksSearchControl.setValue('');
    this.listByForm.setValue(1);
    this.getSubjectList();

    this.listByForm.valueChanges.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {
      this.treeControl.collapseAll();
      this.clearAll();
      this.selectYearGroupsForm.setValue([]);
      this.selectAllSubjects.setValue(false);
      this.blocksForm.controls.blockList.clear({ emitEvent: false });
    });

    this.selectAllSubjects.valueChanges.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
      this.subjectsForm.controls.subjectList.value.forEach((_, index) => {
        const isSearched = this.subjectList[index].name.toLowerCase().includes(this.searchControl.value?.toLowerCase() ?? '');
        if (((this.searchControl.value || this.searchControl.value === '') && isSearched) || this.searchControl.value == null) {
          this.subjectsForm.controls.subjectList.controls[index].setValue(val || false);
        }
      });
    });

    this.selectAllBlocks.valueChanges.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
      const searchValueRaw = this.blocksSearchControl.value;
      const searchValue = (searchValueRaw ?? '').toLowerCase();

      this.blocksForm.controls.blockList.controls.forEach((control, index) => {
        const blockNameRaw = this.blocksList[index]?.blockName ?? '';
        const blockName = blockNameRaw.toLowerCase();

        const isSearched = blockName.includes(searchValue);

        if ((searchValue !== '' && isSearched) || searchValue === '') {
          control.setValue(val || false);
        }
      });
    });

    this.searchControl.valueChanges.pipe(debounceTime(300), takeUntil(this.unsubscribe$)).subscribe((searchTerm) => {
      const subjectListControls = this.subjectsForm.get('subjectList') as FormArray;
      const isAllSelected = subjectListControls.controls.every(control => !!control.value);
      if (searchTerm == null && !isAllSelected) {
        this.selectAllSubjects.setValue(false, { emitEvent: false, onlySelf: true });
      }
    });

    this.blocksSearchControl.valueChanges.pipe(distinctUntilChanged(), debounceTime(300), takeUntil(this.unsubscribe$)).subscribe((searchTerm) => {
      if (!this.blocksList.length) return;

      const blockListControls = this.blocksForm.get('blockList') as FormArray;
      const matchingIndexes = this.blocksList
        .map((block, index) => ({
          index,
          matches: block.blockName?.toLowerCase().includes(searchTerm?.toLowerCase() ?? '')
        }))
        .filter(b => b.matches)
        .map(b => b.index);

      const allMatchingAlreadySelected = !!matchingIndexes.length && matchingIndexes.every(i => blockListControls.at(i).value === true);
      this.selectAllBlocks.setValue(allMatchingAlreadySelected, { emitEvent: false, onlySelf: true });
    });

    this.blocksForm.valueChanges.pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$)).subscribe((val) => {
      const blockListControls = this.blocksForm.get('blockList') as FormArray;
      const isAllSelected = blockListControls.controls.every(control => !!control.value);
        this.selectAllBlocks.setValue(isAllSelected, { emitEvent: false, onlySelf: true });
        this.selectAllBlocks.updateValueAndValidity({ emitEvent: false });
    })

    this.titleHelperService.getProjectById(this.projectId)?.subscribe(response => {
      this.projectName = response?.projectName;
    });
    this.titleHelperService.getTimetableById(this.timetableId)?.subscribe(response => {
      this.timetableName = response?.timeTableName;
    });

    combineLatest([
      this.subjectsForm.valueChanges,
      this.selectYearGroupsForm.valueChanges
    ])
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$)
      )
      .subscribe(() => {
        const subjectIds = this.subjectsForm.controls.subjectList.value
          .map((subj, index) => subj ? this.subjectList[index].id : null)
          .filter(value => value) as number[];

        const blockFilterRequest = {
          SubjectIds: subjectIds,
          YearGroupIds: this.selectYearGroupsForm.value as number[]
        };

        if (!blockFilterRequest.SubjectIds?.length || !blockFilterRequest.YearGroupIds?.length) {
          this.blocksList = [];
          this.blockDataSource.data = [];
          this.blockTreeControl.dataNodes = [];
          this.blocksForm.controls.blockList.clear();
          this.selectAllBlocks.setValue(false, { emitEvent: false });
          this.selectAllBlocks.disable();
          this.blocksSearchControl.disable({ emitEvent: false });
          return;
        }

        this.alreadyRequestedBlockIds = this.blocksList.map(block => block.blockId);
        this.previouslySelectedBlockIds = this.getSelectedBlockIds();

        this.autoScheduleService.getBlocksByYearGroup(this.timetableId, blockFilterRequest).subscribe(response => {
          if (response.length) {
            this.selectAllBlocks.setValue(true, { emitEvent: false });
            this.selectAllBlocks.enable();
            this.blocksSearchControl.enable({ emitEvent: false });
          } else {
            this.selectAllBlocks.setValue(false, { emitEvent: false });
            this.selectAllBlocks.disable();
            this.blocksSearchControl.disable({ emitEvent: false });
          }

          this.blocksList = response.flatMap(group => group.blocks);
          this.BLOCKS_TREE_DATA = this.transformToYearGroups(response);

          this.blocksForm.controls.blockList.clear({ emitEvent: false });
          response.forEach(yg => yg.blocks.forEach(block => {
            const isSelected = this.previouslySelectedBlockIds.includes(block.blockId) || !this.alreadyRequestedBlockIds.includes(block.blockId) || this.isFirstGetBlocks;
            this.blocksForm.controls.blockList.push(new FormControl(isSelected as any), { emitEvent: false });
          }));

          this.blocksForm.controls.blockList.updateValueAndValidity();
          this.blockDataSource.data = this.BLOCKS_TREE_DATA;
          this.blockTreeControl.dataNodes = this.BLOCKS_TREE_DATA;
          this.blockTreeControl.expandAll();

          if (this.isFirstGetBlocks) {
            this.isFirstGetBlocks = false;
          }
        });
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  onChange() {
    let indices;
    if (this.listByForm.value === 2) {
      indices = this.dataSource.data.map(d => d.children).flat().map(c => c?.index);
    }
      const subjectListControls = this.subjectsForm.get('subjectList') as FormArray;
      const isAllSelected = subjectListControls.controls.every(control => !!control.value);
    setTimeout(() => {
      // This part is to check if all the elements were selected and if yes, the 'select all' should be selected too
      const all = this.listByForm.value === 2 ? this.subjectsForm.controls.subjectList.value.every(item => item) : isAllSelected;
      this.selectAllSubjects.setValue(all, { emitEvent: false, onlySelf: true });
    }, 200)
  }

  getSubjectList() {
    this.curriculumPlanService.getSubjectsList(this.projectId).subscribe((res) => {
      const sortedByDepAndSubjectCode = res
        .filter((subject) => subject.departmentName)
        .sort((a: ISubject, b: ISubject) => customComparator(a.departmentName || '', b.departmentName || ''))
        .sort((a: ISubject, b: ISubject) => {
          if (a.departmentName === b.departmentName) {
            if (a.code < b.code) return -1;
            if (a.code > b.code) return 1;
          }
          return 0;
        });

      this.freeSessionSubject = res.find(subject => subject.shortName.toLowerCase() === NOT_AVAILABLE.FREE_SESSION)!;
      this.subjectList = res
        .filter((subject) => subject.shortName.toLowerCase() !== NOT_AVAILABLE.FREE_SESSION)
        .sort((a: ISubject, b: ISubject) => customComparator(a.code, b.code));
      this.TREE_DATA = this.transformArray(sortedByDepAndSubjectCode);

      this.subjectsForm.controls.subjectList.patchValue([]);
      this.subjectList.map(() => {
        this.subjectsForm.controls.subjectList.push(new FormControl(false as any), { emitEvent: false });
      });
      this.subjectsForm.controls.subjectList.updateValueAndValidity();
      this.dataSource.data = this.TREE_DATA;
      this.treeControl.dataNodes = this.TREE_DATA;
    });
  }

  hasChild = (_: number, node: Node | BlockTreeNode) => !!node.children && node.children.length > 0;

  transformArray(inputArray: ISubject[]): Node[] {
    const outputArray: Node[] = [];
    inputArray.forEach((inputObject: ISubject): void => {
      const { departmentName } = inputObject;
      if (departmentName) {
        const departmentIndex: number = outputArray.findIndex((item) => item.name === departmentName);
        if (departmentIndex !== -1) {
          outputArray[departmentIndex]?.children?.push({
            name: inputObject.name,
            code: inputObject.code,
            color: inputObject.color,
            index: this.subjectList.findIndex(s => s.id === inputObject.id)
          });
        } else {
          outputArray.push({
            name: departmentName,
            children: [{
              name: inputObject.name,
              code: inputObject.code,
              color: inputObject.color,
              index: this.subjectList.findIndex(s => s.id === inputObject.id)
            }]
          });
        }
      }
    });
    return outputArray;
  }

  transformToYearGroups(input: BlocksByYG[]): BlockTreeNode[] {
    return input.map(group => ({
      name: this.selectYearGroups.find(yg => yg.id === group.yearGroupId)?.text ?? '',
      children: group.blocks.length
        ? group.blocks.map(block => ({
          name: block.blockName ?? '',
          blockTypeId: block.blockTypeId,
          id: block.blockId ?? 1
        }))
        : []
    }));
  }

  clearAll(): void {
    this.subjectsForm.controls.subjectList.value.forEach((_, index) => {
      const isSearched = this.subjectList[index].name.toLowerCase().includes(this.searchControl.value?.toLowerCase() ?? '');
      if (((this.searchControl.value || this.searchControl.value === '') && isSearched) || this.searchControl.value == null) {
        this.subjectsForm.controls.subjectList.controls[index].setValue(false);
      }
      this.selectAllSubjects.setValue(false, { emitEvent: false, onlySelf: true });
    });
  }

  clearAllBlocks(): void {
    const searchValue = this.blocksSearchControl.value;
    this.blocksForm.controls.blockList.value.forEach((_, index) => {
      const isSearched = this.blocksList[index].blockName?.toLowerCase().includes(searchValue ?? '');
      if (((searchValue || searchValue === '') && isSearched) || searchValue == null) {
        this.blocksForm.controls.blockList.controls[index].setValue(false, {emitEvent: false});
      }
      this.selectAllBlocks.setValue(false, { emitEvent: true, onlySelf: true });
    })
  }

  isSubjectSearched(node: Node): boolean {
    return this.isSearched(node, this.searchControl.value, (this.subjectsForm.get('subjectList') as FormArray));
  }

  isBlockSearched(node: Node) {
    return this.isSearched(node, this.blocksSearchControl.value, (this.blocksForm.get('blockList') as FormArray));
  }

  isSearched(node: Node, searchTerm: string | null, subjectListArray: FormArray): boolean {
    // Find the indexes where the control value is true
    const selectedIndexes: number[] = [];
    subjectListArray.controls.forEach((control, index) => {
      if (control.value === true) {
        selectedIndexes.push(index);
      }
    });
    const isSearched = searchTerm ? node?.children?.some(n => n.name?.toLowerCase().includes(searchTerm) || n.code?.toLowerCase().includes(searchTerm)) : false;
    const isSelected = node?.children?.some(n => n.index != null ? selectedIndexes.includes(n.index) : false);
    if (isSearched || isSelected) {
      return true
    } else {
      return false;
    }
  }

  async keepExistingScheduleRadioChange() {
    const radioValue = await this.keepExistingScheduleRadioGroup.nativeElement.getValue();

    if (radioValue === null){
      this.keepExistingSchedule = null;
    } else {
      this.keepExistingSchedule = radioValue === 'yes';
    }
  }

  async includeDraftNccRadioChange() {
    const radioValue = await this.includeDraftNccRadioGroup.nativeElement.getValue();

    if (radioValue === null){
      this.includeDraftNcc.setValue(null);
    } else {
      this.includeDraftNcc.setValue(radioValue === 'yes');
    }
  }

  preSchedule(rerunAutoSchedule = false, nccGroupIds: number[] = []): void {
    if (this.selectYearGroupsForm.invalid) {
      return;
    }

    this.autoScheduleService
      .preSchedule(this.timetableId, {
        blockIds: this.getSelectedBlockIds(),
        nccGroupIds,
        keepExistingSchedule: this.keepExistingSchedule ?? false,
      })
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: () => {
          this.curriculumPlanBlocksService.stateChanged$.next();
          this.snackbar.success(this.translate.instant('Pre-schedule has been successfully completed.'));
          if (rerunAutoSchedule) {
            this.changeToPreviousStep.emit(true);
          }
          else {
            this.changeToNextStep.emit(this.includeDraftNcc.value ? 1 : 2);
          }
        },
        error: ({ error }) => {
          if (error?.validationErrors?.[0]) {
            this.changeToNextStep.emit(0);
          } else {
            this.snackbar.error();
          }
        }
      });
  }

  private getSelectedBlockIds(): number[] {
    const blockIds: number[] = [];
    this.blocksForm.controls.blockList.value.forEach((subj, index) => {
      if (subj) {
        blockIds.push(this.blocksList[index]?.blockId);
      }
    });

    return blockIds;
  }
}
