import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { Subject, distinctUntilChanged, skip, takeUntil } from 'rxjs';
import { BaseModalComponent } from '../../../../../../../_shared/components/BaseModalComponent';
import { RelationshipsService } from '../../../../../../services/relationships.service';
import { ORGANISED_BY_TYPES } from '../../../../../../../_shared/enums/OrganisedByTypes';
import { CurriculumPlanService } from '../../../../../../services/curriculum-plan.service';
import { ITimeTableOverviewResponse } from '../../../../../../../_shared/models/ITimeTableOverviewResponse';
import { isColorDark } from '@bromcom/ui';
import * as saveAs from 'file-saver';
import { PlanningRelationshipsService } from '../../../../../../services/planning-relationships.service';
import {
  diagonalLines
} from '../../../scheduling/components/timetable-canvas/helper/visual-matrix/helpers/BackgroundTemplates';
import { TitleHelperService } from '../../../../../../../_shared/services/title-helper.service';
import { DepartmentService } from '../../../../../../../projects/services/department.service';
import { NewTimetableService } from '../../../../../../../timetables/services/new-timetable.service';


@Component({
  selector: 'bromcom-curriculum-plan-overview-modal',
  templateUrl: './curriculum-plan-overview.component.html',
  styleUrls: ['./curriculum-plan-overview.component.scss']
})
export class CurriculumPlanOverviewModalComponent extends BaseModalComponent implements OnDestroy {
  readonly unsubscribe$: Subject<void> = new Subject();
  @ViewChild('overviewModal') overviewModal!: ElementRef;

  chipTemplate = `
    <span class="list-circle" style="background-color: #{{color}};"></span>
    <span style="padding-right: 8px;">{{code}}</span>
  `;
  yearGroups: any;
  departmentList: any;
  subjectList: any;
  projectId!: number;
  timetableId!: number;
  projectName = '';
  timetableName = '';
  tableBackgroundURL = diagonalLines('#475568').toDataURL();
  selectedSubjectIds: number[] = [];
  selectedDepartmentIds: number[] = [];
  selectedYearGroupIds: number[] = [];
  totalBandCount:number = 0;

  selectedOrganisedByType = ORGANISED_BY_TYPES.YearGroup;
  organisedByTypes = ORGANISED_BY_TYPES;

  overviewData: ITimeTableOverviewResponse[] = [];

  constructor(
    public relationships: RelationshipsService,
    private curriculumPlanService: CurriculumPlanService,
    private planningRelationshipsService: PlanningRelationshipsService,
    private titleHelperService: TitleHelperService,
    private departmentService: DepartmentService,
    private timetableService: NewTimetableService) {
    super();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    this.planningRelationshipsService.overviewRequest$.next({
      yearGroupIds: [],
      subjectIds: [],
      departmentIds: [],
      isSubjectView: true,
      isStaffView: false,
      isRoomView: false
    })
  }

  private isOverviewRequestSubscribed = false;

  show(projectId: number, timetableId: number): void {
    this.organiseBy(this.selectedOrganisedByType);

    this.isOpen = true;
    this.projectId = projectId;
    this.timetableId = timetableId;
    this.getPeriodCount();

    this.planningRelationshipsService.yearGroupsData$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe((yearGroups) => {
        this.yearGroups = yearGroups
          .filter((yearGroup) => !yearGroup.isExcluded)
          .map((yearGroup) => {
            return { ...yearGroup, text: yearGroup.name };
          })
          .sort((a, b) => Number(b.name) - Number(a.name));
      })

    if (!this.isOverviewRequestSubscribed) {
      this.isOverviewRequestSubscribed = true;

      this.planningRelationshipsService.overviewRequest$
        .pipe(takeUntil(this.unsubscribe$))
        .subscribe(request => {
          this.curriculumPlanService.getOverview(this.timetableId, request).subscribe((data) => {
            this.overviewData = data;
            this.selectedSubjectIds = request.subjectIds ?? [];
            this.selectedDepartmentIds = request.departmentIds ?? [];
            this.selectedYearGroupIds = request.yearGroupIds ?? [];
          });
        });

      this.planningRelationshipsService.overviewRequest$.next(this.planningRelationshipsService.overviewRequest$.getValue())

      this.relationships.subjectsData$
        .pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$))
        .subscribe((subjects) => {
          this.subjectList = subjects.map((subject) => ({
            ...subject,
            text: subject.code ?? + ' - ' + subject.name ?? 'N/A'
          }));
        });

      this.departmentService.getDepartmentsOptions(this.projectId).subscribe(departments => {
        this.departmentList = departments
          .map((department) => ({
            id: department.id,
            text: department.name
          }))
      })
    }

    setTimeout(() => {
      this.overviewModal.nativeElement.show();
      this.selectedSubjectIds = this.planningRelationshipsService.overviewRequest$.getValue().subjectIds ?? [];
      this.selectedDepartmentIds = this.planningRelationshipsService.overviewRequest$.getValue().departmentIds ?? [];
      this.selectedYearGroupIds = this.planningRelationshipsService.overviewRequest$.getValue().yearGroupIds ?? [];
    }, 100);
  }

  organiseBy(type: ORGANISED_BY_TYPES) {
    this.selectedOrganisedByType = type;
    const rq = this.planningRelationshipsService.overviewRequest$.getValue();
    rq.isRoomView = type === ORGANISED_BY_TYPES.Room;
    rq.isStaffView = type === ORGANISED_BY_TYPES.Teacher;
    rq.isSubjectView = type === ORGANISED_BY_TYPES.YearGroup;
    this.planningRelationshipsService.overviewRequest$.next(rq);
  }

  export() {
    this.titleHelperService.getProjectById(this.projectId)?.subscribe(response => {
      this.projectName = response?.projectName;
    });
    this.titleHelperService.getTimetableById(this.timetableId)?.subscribe(response => {
      this.timetableName = response?.timeTableName;
    });

    this.curriculumPlanService.getOverviewExport(this.timetableId, this.planningRelationshipsService.overviewRequest$.getValue()).subscribe((data) => {
      const fileName = `${this.projectName} ${this.timetableName} Overview.xlsx`
      saveAs(data, fileName);
    })
  }

  onApplyYeargroupFilter(event: any) {
    const rq = this.planningRelationshipsService.overviewRequest$.getValue();
    rq.yearGroupIds = event.selectedItemIds;
    this.planningRelationshipsService.overviewRequest$.next(rq);
  }

  onApplyDepartmentFilter(event: any) {
    const rq = this.planningRelationshipsService.overviewRequest$.getValue();
    rq.departmentIds = event.selectedItemIds;
    this.planningRelationshipsService.overviewRequest$.next(rq);
  }

  onApplySubjectFilter(event: any) {
    const rq = this.planningRelationshipsService.overviewRequest$.getValue();
    rq.subjectIds = event.selectedItemIds;
    this.planningRelationshipsService.overviewRequest$.next(rq);
  }

  isColorDarkHelper(color: string | undefined) {
    return color ? isColorDark(color) : false;
  }

  showToolTip(col: any): string {
    let val = col.tooltip || '';
    if (col.cellType === 'PeriodCount') {
      if (+col.content > this.totalBandCount) {
        val = "Total band count exceeds the allowed maximum.";
      }
    }
    return val;
  }

getPeriodCount() {
  this.timetableService.getPeriodStructureByTimetableId(this.timetableId).subscribe((periodStructure) => {
    const totalPeriods = periodStructure.periodStructure.weeks
      .flatMap(week => week.days)
      .flatMap(day => day.periods)
      .filter(period => period.periodCode === "PERIOD")
      .length;

    this.totalBandCount = totalPeriods;
  });
}
}
