<ng-container *ngIf="isOpen">
  <bcm-modal size="small" class="band-confirmation-modal"
  (bcm-modal-before-close)="onClose()"
            #confirmationModal>
    <div class="modal-body">
      <bcm-icon icon="far fa-exclamation-triangle fa-5"

                [ngClass]="{emerald : type === 'success', warning: type === 'warning'}"></bcm-icon>

      <div class="title">{{ headerText }}</div>
      <div class="description">{{ mainText }}</div>
    </div>

    <bcm-modal-footer>
      <bcm-button [dataDismiss]="dismiss" kind="ghost"
                  (click)="onDeleteClicked(false)">{{'Cancel' | translate}}</bcm-button>
      <bcm-button [dataDismiss]="dismiss" [color]="buttonColor"
                  (click)="onDeleteClicked(true)">{{buttonTitle}}</bcm-button>
    </bcm-modal-footer>
  </bcm-modal>
</ng-container>
