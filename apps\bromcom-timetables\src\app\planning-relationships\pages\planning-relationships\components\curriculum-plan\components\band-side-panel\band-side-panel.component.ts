import { Component, Input, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { DeleteConfirmationComponent } from '../../../common/delete-confirmation/delete-confirmation.component';
import { IBand } from '../../../../../../../_shared/models/IBand';
import { FormControl, Validators } from '@angular/forms';
import { BandService } from '../../../../../../services/band.service';
import { SnackbarService } from '@bromcom/ui';
import { TranslateService } from '@ngx-translate/core';
import {BehaviorSubject, debounceTime, distinctUntilChanged, filter, Observable, Subject, takeUntil} from 'rxjs';
import { CurriculumPlanService } from '../../../../../../services/curriculum-plan.service';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';
import { PlanningRelationshipsService } from '../../../../../../services/planning-relationships.service';
import { MatMenuTrigger } from '@angular/material/menu';
import {NewTimetableService} from "../../../../../../../timetables/services/new-timetable.service";

@Component({
  selector: 'bromcom-band-side-panel',
  templateUrl: './band-side-panel.component.html',
  styleUrls: ['./band-side-panel.component.scss']
})
export class BandSidePanelComponent implements OnInit, OnDestroy {
  @ViewChild(MatMenuTrigger) trigger!: MatMenuTrigger;
  @Input() timetableId!: number;
  @Input() disableRemoveByYear = false;
  @Input() disableRemoveByBand = false;
  @ViewChild('bandConfirmationComponent') bandConfirmationComponent!: DeleteConfirmationComponent;
  @ViewChild('deleteFromYearGroupConfirmationComponent') deleteFromYearGroupConfirmationComponent!: DeleteConfirmationComponent;
  @ViewChild('deleteFromBandConfirmationComponent') deleteFromBandConfirmationComponent!: DeleteConfirmationComponent;

  selectedYearGroup!: number;
  bandsByYearGroup: IBand[] = [];
  selectedYearName = '';
  selectedBandIndex = 0;
  editBandName = false;
  bandNameControl = new FormControl('', [Validators.required]);
  bandsOptions: IBand[] = [];
  reviewGeneratedYearGroups: number[] = [];
  totalCount$: Observable<number> = new Observable();
  totalPeriodCount$: BehaviorSubject<number> = new BehaviorSubject(100);

  readonly unsubscribe$: Subject<void> = new Subject();

  constructor(
    private curriculumPlan: CurriculumPlanService,
    private curriculumPlanBlocks: CurriculumPlanBlocksService,
    private planningRelationships: PlanningRelationshipsService,
    private band: BandService,
    private snackbar: SnackbarService,
    private translate: TranslateService,
    private timetableService: NewTimetableService
  ) {
  }

  ngOnInit(): void {
    this.curriculumPlan
      .getGeneratedBlockYeargroupIds(this.timetableId)
      .subscribe(res => {
        this.reviewGeneratedYearGroups = res.yearGroupIds;
      });

    this.getPeriodCount();

    this.curriculumPlan.selectedYearGroup$
      .pipe(
        takeUntil(this.unsubscribe$))
      .subscribe(selectedYearGroup => {
        if (!selectedYearGroup) {
          return
        }
        this.selectedYearGroup = selectedYearGroup;
        this.band.getListOfBandsByYearGroup(this.timetableId, this.selectedYearGroup)
          .subscribe(bands => {
            this.bandsByYearGroup = bands;
            this.curriculumPlan.selectedBand$.next(this.bandsByYearGroup[0]?.id);
            this.selectedYearName = this.planningRelationships.yearGroupsData$.getValue()
              .find(yearGroup => yearGroup.id === selectedYearGroup)?.description ?? ''
            this.updateBandOptions();
          })
      })

    this.curriculumPlan.selectedBand$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(selectedBand => {
        const band = this.bandsByYearGroup.find(band => band.id === selectedBand)
        if (band) {
          this.bandNameControl.setValue(band?.bandName);
          this.selectedBandIndex = this.bandsByYearGroup.findIndex(band => band.id === selectedBand);
        }
      });

    this.bandNameControl.valueChanges
      .pipe(
        debounceTime(500),
        filter(() => this.editBandName),
        takeUntil(this.unsubscribe$)
      )
      .subscribe(bandName => {
        this.bandNameValueChange(bandName)
      });

      this.totalCount$ = this.curriculumPlan.value$ ?? 0;
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  showBandBulkActions(): void {
    this.trigger.openMenu();
  }

  addBand(order: number): void {
    this.band.addNewBandByYearGroup(this.timetableId, this.selectedYearGroup, order)
      .subscribe({
        next: (newBand) => {
          this.band.getListOfBandsByYearGroup(this.timetableId, this.selectedYearGroup).subscribe();

          this.bandsByYearGroup.splice(order, 0, newBand)
          this.selectedBandIndex = order;
          this.curriculumPlan.selectedBand$.next(newBand.id);
          this.setBandControlValue();
          this.updateBandOptions();
          this.snackbar.success(this.translate.instant(`Band ${newBand.bandName} is added successfully.`));
          this.curriculumPlanBlocks.blockSideFilterStateChanged$.next();
          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentBandChanged$.next(true);
        },
        error: () => {
          this.snackbar.error()
        }
      })
  }

  onEditBandName(): void {
    this.editBandName = true;
  }

  onSaveBandName(): void {
    const bandName = this.bandNameControl.value;

    if (!bandName || bandName === this.bandsByYearGroup[this.selectedBandIndex].bandName) {
      this.editBandName = false;
      this.snackbar.info(this.translate.instant('Band name is the same as the original value. No changes were made.'));
      return;
    }

    const currentBand = this.bandsByYearGroup[this.selectedBandIndex];

    if (bandName) {
      this.band.updateBandName(currentBand.id, bandName)
        .subscribe({
          next: () => {
            currentBand.bandName = bandName;
            this.bandNameControl.setValue(bandName);
            this.editBandName = false;
            this.updateBandOptions();
            this.snackbar.success(this.translate.instant('Band name updated successfully'));
            this.curriculumPlanBlocks.stateChanged$.next();
            this.curriculumPlanBlocks.blockSideStateChanged$.next();
          },
          error: () => {
            this.snackbar.error();
          }
        })
    } else {
      this.snackbar.error(this.translate.instant('Band name cannot be empty'))
    }
  }

  onCancelBandName(): void {
    this.editBandName = false;
    this.setBandControlValue();
  }

  showDeleteBand(): void {
    this.bandConfirmationComponent.show();
  }

  deleteBand(isDelete: boolean): void {
    if (isDelete) {
      const band = this.bandsByYearGroup[this.selectedBandIndex];
      this.band.deleteBand(band.id).subscribe({
        next: () => {
          this.band.getListOfBandsByYearGroup(this.timetableId, this.selectedYearGroup).subscribe();

          this.bandsByYearGroup = this.bandsByYearGroup.filter(bandByYear => bandByYear.id !== band.id);
          if (this.selectedBandIndex >= this.bandsByYearGroup.length - 1) {
            this.selectedBandIndex = this.bandsByYearGroup.length - 1;
          }
          this.curriculumPlan.selectedBand$.next(this.bandsByYearGroup[this.selectedBandIndex].id);
          this.setBandControlValue();
          this.updateBandOptions();
          this.snackbar.success(this.translate.instant(`Band ${band.bandName} is deleted successfully.`));
          this.curriculumPlanBlocks.blockSideFilterStateChanged$.next();
          this.curriculumPlanBlocks.stateChanged$.next();
        },
        error: () => {
          this.snackbar.error()
        }
      })
    }
  }

  updateBandOptions(): void {
    this.bandsOptions = this.bandsByYearGroup.map(band => ({
      ...band,
      bandName: this.selectedYearName + band.bandName
    })) ?? []
  }

  updateSelectedBandIndex(value: number): void {
    const newIndex = this.selectedBandIndex + value;
    if (newIndex >= 0 && newIndex < this.bandsByYearGroup.length) {
      this.selectedBandIndex = newIndex;
      this.setBandControlValue();
      this.curriculumPlan.selectedBand$.next(this.bandsByYearGroup[newIndex].id);
      this.curriculumPlan.currentBandChanged$.next(true);
    }
  }

  onCopyClicked(bandId: number): void {
    const blockIds = this.curriculumPlanBlocks.blocksByBand$.getValue().map(block => block.id)
    this.band.copyBandStructure(blockIds, bandId).subscribe({
      next: () => {
        this.curriculumPlanBlocks.stateChanged$.next();
        this.curriculumPlanBlocks.blockSideStateChanged$.next();
        this.snackbar.success(this.translate.instant('Blocks copied successfully.'));
      },
      error: () => {
        this.snackbar.error();
      }
    })
  }

  onRemoveBlocksClicked(removeFromYearGroup: boolean): void {
    removeFromYearGroup
      ? this.deleteFromYearGroupConfirmationComponent.show()
      : this.deleteFromBandConfirmationComponent.show()
  }

  onRemoveBlocks(isRemove: boolean, removeFromYearGroup: boolean): void {
    if (!isRemove) return;

    const blockIds = removeFromYearGroup
      ? this.curriculumPlanBlocks.blocksByYearGroup$.getValue().map(block => block.id)
      : this.curriculumPlanBlocks.blocksByBand$.getValue().map(block => block.id)
    this.band.removeAllBlocks(blockIds).subscribe({
      next: () => {
        this.curriculumPlanBlocks.stateChanged$.next();
        this.snackbar.success(this.translate.instant('Blocks deleted successfully.'));
      },
      error: () => {
        this.snackbar.error();
      }
    })
  }

  onRestoreReviewVersionClicked() {
    this.curriculumPlan.restoreReviewVersion(this.timetableId, this.selectedYearGroup)
      .subscribe({
        next: () => {
          this.curriculumPlan.selectedYearGroup$.next(this.selectedYearGroup);
          this.curriculumPlanBlocks.stateChanged$.next();
          this.snackbar.success(this.translate.instant('Block Planning version restored successfully.'));
        },
        error: err => {
          this.snackbar.error(err.error.validationErrors[0]?.errorMessage)
        }
      });
  }

  private setBandControlValue(): void {
    this.bandNameControl.setValue(this.bandsByYearGroup[this.selectedBandIndex].bandName);
  }

  private bandNameValueChange(value: string | null): void {
    if (!value || value === this.bandsByYearGroup[this.selectedBandIndex].bandName) return;
    const currentBand = this.bandsByYearGroup[this.selectedBandIndex];

    this.band.checkIfBandNameUnique(currentBand.timeTableId, currentBand.yearGroupId, value, currentBand.id)
      .subscribe({
        next: () => {
          this.snackbar.error(this.translate.instant('Band name is not unique'));
          this.bandNameControl.setErrors({ custom: this.translate.instant('Project name is not unique') })
        }
      });

      this.trgiggerCurriculumPlanFunction()
  }

  trgiggerCurriculumPlanFunction() {
    this.curriculumPlan.callChangeBandFunction();
  }

  getPeriodCount() {
    this.timetableService.getPeriodStructureByTimetableId(this.timetableId).subscribe((periodStructure) => {
      const totalPeriods = periodStructure.periodStructure.weeks
        .flatMap(week => week.days)
        .flatMap(day => day.periods)
        .filter(period => period.periodCode === "PERIOD")
        .length;

      this.totalPeriodCount$.next(totalPeriods);
    });
  }
}
