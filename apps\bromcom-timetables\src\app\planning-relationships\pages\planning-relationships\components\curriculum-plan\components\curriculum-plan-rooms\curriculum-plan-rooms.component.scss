@import "apps/bromcom-timetables/src/assets/styles/variables";
@import "apps/bromcom-timetables/src/assets/styles/style-collector";

.room-container {
  height: 100%;
  padding: 16px 0;

  .room-header {
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    background: $color-blue-grey-100;
    border-radius: 8px;

    margin: 8px 0;
    padding: 4px;
    height: 40px;

    .drag-icon {
      width: 10%;
      text-align: center;
      position: relative;

      .dot {
        @include dot;
      }
    }

    .code, .teacher, .type, .site, .contactTime {
      color: $color-blue-grey-700;
      font-weight: 500;
    }

    .code {
      width: 15%;
      max-width: 55px;
      min-width: 55px;
      cursor: pointer;
    }

    .teacher {
      width: 22%;
      padding: 0 8px;
      cursor: pointer;
    }

    .type {
      width: 15%;
      min-width: 15px;
      padding: 0 0 0 8px;
      cursor: pointer;
    }

    .site {
      width: 20%;
      min-width: 20px;
      padding: 0 8px;
      cursor: pointer;
    }

    .contactTime {
      width: 18%;
      cursor: pointer;
    }
  }

  .room-cards-container {
    // 32px - paddings 16+16
    // 32px - search input
    // 32px - edit staff button
    // 50px - correction
    height: calc(100% - 32px - 32px - 32px - 50px);
    overflow: auto;

    .icon {
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;

      i.fa-info-circle {
        font-size: 14px;
        margin-top: 2px
      }
    }

    .no-data {
      min-height: 378px;
    }
  }

  .room-cards-container::-webkit-scrollbar {
    display: none;
  }
}
