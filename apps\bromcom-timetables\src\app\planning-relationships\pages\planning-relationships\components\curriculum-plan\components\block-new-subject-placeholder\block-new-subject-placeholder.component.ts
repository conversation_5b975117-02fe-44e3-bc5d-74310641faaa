import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { ICurriculumPlanBlock } from '../../../../../../../_shared/models/ICurriculumPlanBlock';

@Component({
  selector: 'bromcom-block-new-subject-placeholder',
  templateUrl: './block-new-subject-placeholder.component.html',
  styleUrls: ['./block-new-subject-placeholder.component.scss']
})
export class BlockNewSubjectPlaceholderComponent {
  @Output() addSubjectToBlock = new EventEmitter();
  @Input() block!: ICurriculumPlanBlock;
  @Input() listIndex!: number;
  @Input() showSides = true;
  @Input() classNames = [''];
  @Input() isComplexBlock = false;
  @Input() emptySubjectPlaceholderId!: string;

  drop(event: CdkDragDrop<any>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      this.addSubjectToBlock.emit(event);
    }
  }
}
