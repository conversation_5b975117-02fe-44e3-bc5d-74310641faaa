import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs';

@Component({
  selector: 'bromcom-filter',
  templateUrl: './filter.component.html',
  styleUrls: ['./filter.component.scss']
})
export class FilterComponent implements OnInit, OnChanges {
  searchControl = new FormControl('');
  filterGroup = new FormGroup({
    items: new FormArray<FormControl<boolean | null>>([])
  });
  _dataItems: any[] = [];
  filteredDataItems: any[] = [];
  @Input() selectedItemIds: number[] = [];
  clearFilterOptions = false;
  @ViewChild('filterPopup', { static: false }) filterPopup!: ElementRef;
  @Input() isOpen = false;
  @Input() resetFilter = false;
  @Input() selectAllOnClearedApply = true;
  @Input() showHeader = true;
  @Input() showSearchBar = true;
  @Input() showActions = true;
  @Input() appearance = 'checkbox';
  @Input() enableApplyFilterWithDefaultSelection = false;

  @Input() set selection(data: number[] | null) {
    this.selectedItemIds = data || [];
    this.filteredDataItems?.forEach((item) => {
      const index = this._dataItems.findIndex((data) => data.id === item.id);
      this.filterGroup.controls.items.controls[index]?.setValue(this.selectedItemIds.includes(item.id));
    });
  }

  @Input() set dataItems(data: { items: any[], startState: boolean, removeFreeSessions?: boolean, freeSessionId?: number | null }) {
    if (data.removeFreeSessions && data.freeSessionId) {
      const freeSessionIndex = this._dataItems.findIndex(d => d.id === data.freeSessionId);
      this._dataItems = data.items;
      this.searchFilter();
      this.filterGroup.controls.items.removeAt(freeSessionIndex);
    } else if (!data.removeFreeSessions && data.freeSessionId) {
      this._dataItems = data.items;
      this.searchFilter();
      const freeSessionIndex = this._dataItems.findIndex(d => d.id === data.freeSessionId);
      this.filterGroup.controls.items.insert(freeSessionIndex, new FormControl(false));
    } else if (!data.removeFreeSessions && data.freeSessionId == null && this.selectedItemIds.length === 0) {
      this._dataItems = data.items;
      this.searchFilter();
      this.initializeFormControls(data.startState);
    }
  }

  @Output() filterData = new EventEmitter();
  @Output() closeFilter = new EventEmitter<boolean>();

  ngOnChanges(changes: SimpleChanges): void {
    const change = changes['isOpen'];
    const reset = changes['resetFilter'];
    if (change) {
      this.isOpen = change.currentValue;
      this.searchControl.setValue('');
      this.searchFilter();
    }
    if (!this.isOpen) {
      this.filteredDataItems?.forEach((item) => {
        const index = this._dataItems.findIndex((data) => data.id === item.id);
        this.filterGroup.controls.items.controls[index]?.setValue(this.selectedItemIds.includes(item.id));
      });
    }
    if (reset?.currentValue) {
      this.selectedItemIds = [];
      this.filteredDataItems?.forEach((item, index) => {
        this.filterGroup.controls.items.controls[index]?.setValue(false);
      });
    }
  }

  ngOnInit() {
    this.searchControl.valueChanges
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe(() => {
        this.searchFilter();
      });

    if (!this.showActions) {
      this.filterGroup.valueChanges
        .pipe(debounceTime(50), distinctUntilChanged())
        .subscribe(value => {
          const selectedItemIdsSet: Set<number> = new Set();
          this.filterGroup.controls.items.value?.forEach((isSelected, index) => {
            if (isSelected) {
              const selectedItemId = this._dataItems[index].id;
              selectedItemIdsSet.add(selectedItemId);
            }
          });
          this.filterData.emit({ selectedItemIds: [...selectedItemIdsSet] });
        })
    }
  }

  initializeFormControls(setAllToTrue?: boolean) {
    const itemsArray = this.filterGroup.controls.items as FormArray;
    const tmp: Set<number> = new Set();
    if (itemsArray && this._dataItems?.length) {
      itemsArray.clear();
      this._dataItems?.forEach((data) => {
        if (setAllToTrue !== false) {
          tmp.add(data.id);
        }
        itemsArray.push(new FormControl((setAllToTrue === true ? true : (setAllToTrue === false ? false : this.selectedItemIds.includes(data.id)))));
      });
      this.selectedItemIds = [...tmp];
    }
  }

  selectAll(): void {
    this.filteredDataItems.forEach((item) => {
      const index = this._dataItems.findIndex((data) => data.id === item.id);
      this.filterGroup.controls.items.controls[index]?.setValue(true);
    });
  }

  clearFilter() {
    this.filteredDataItems?.forEach((item) => {
      const index = this._dataItems.findIndex((data) => data.id === item.id);
      this.filterGroup.controls.items.controls[index]?.setValue(false);
    });
    this.clearFilterOptions = true;
  }

  searchFilter() {
    const searchTerm = this.searchControl?.value?.toLowerCase();
    this.filteredDataItems = searchTerm ? this._dataItems.filter(
      (item) => item.text.toLowerCase().includes(searchTerm)
    ) : this._dataItems;
  }

  applyFilter(event: any) {
    const selectedItemIdsSet: Set<number> = new Set();
    this.filterGroup.controls.items.value?.forEach((isSelected, index) => {
      if (isSelected) {
        const selectedItemId = this._dataItems[index].id;
        selectedItemIdsSet.add(selectedItemId);
      }
    });
    this.selectedItemIds = [...selectedItemIdsSet];
    this.clearFilterOptions = ((this.clearFilterOptions && this.selectedItemIds.length === 0) || (!this.enableApplyFilterWithDefaultSelection && this._dataItems?.length === this.selectedItemIds?.length));
    this.filterData.emit({ selectedItemIds: [...selectedItemIdsSet], clearFilter: this.clearFilterOptions });
    if (this.clearFilterOptions && this.selectAllOnClearedApply) {
      this.filteredDataItems.forEach((item, index) => {
        this.selectedItemIds.push(item.id);
        this.filterGroup.controls.items.controls[index]?.setValue(true);
      });
    }
    this.searchControl.setValue('');
  }

  onCancel(event: any) {
    this.searchControl.setValue('');
    this.closeFilter.emit(true);
    this.isOpen = false;
    this.filteredDataItems = this._dataItems;
    setTimeout(() => {
      this.filteredDataItems?.forEach((item) => {
        const index = this._dataItems.findIndex((data) => data.id === item.id);
        this.filterGroup.controls.items.controls[index]?.setValue(this.selectedItemIds.includes(item.id));
      });
    }, 100);
  }
}
