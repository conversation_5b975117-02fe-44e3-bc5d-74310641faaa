@import '../../../../../../../../../bromcom-timetables/src/assets/styles/variables.scss';

.bcm-list__input__ {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  border: 1px solid var(--bcm-new-ds-color-slate-300);
  border-radius: 4px;
  color: var(--bcm-new-ds-color-slate-600);
  background-color: var(--bcm-new-ds-color-white);
  user-select: none;
  cursor: pointer;
  padding: 0 8px;
  overflow: hidden;
  position: relative;
  transition: background-color 100ms, border-color 100ms;

  height: 32px;
  font-size: 14px;
  line-height: calc(14px + 8px);
}

.bcm-list__label__ {
  line-height: 1;
  padding: 5px 0;
}

.bcm-list__input-container__ {
  overflow: hidden;
}

::ng-deep .mdc-evolution-chip-set__chips {
  flex-flow: unset !important;
}

::ng-deep .mat-mdc-chip {
  height: 24px !important;
  padding: 0 4px;
  background-color: $color-blue-grey-200;

  .list-circle {
    display: block;
    width: 16px;
    height: 16px;
    margin-left: 4px;
    margin-right: 8px;
    border-radius: 100px;
  }
}

.placeholder {
  color: $color-blue-grey-400;
  font-size: 14px;
}


::ng-deep .filter-list .filter-popup {
  position: absolute;
  background: white;
  border: 1px solid $color-blue-grey-200;;
  border-radius: 4px;
  z-index: 100;
}
