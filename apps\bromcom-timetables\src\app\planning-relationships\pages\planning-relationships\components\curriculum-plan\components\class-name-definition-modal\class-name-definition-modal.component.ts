import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ClassNameDefinitionService } from '../../../../../../services/class-name-definition.service';
import { IClassNameDefinition } from '../../../../../../../_shared/models/IClassNameDefinition';
import { FormControl } from '@angular/forms';
import { IClassNameItems } from '../../../../../../../_shared/models/IClassNameItems';
import { CLASS_NAME_TYPE } from '../../../../../../../_shared/enums/ClassNameType';
import { CLASS_NAME_ITEMS_COLOR } from '../../../../../../../_shared/enums/ClassNameItemColors';
import { CLASS_NAME_ITEMS_BORDER_COLOR } from '../../../../../../../_shared/enums/ClassNameItemBorderColors';
import { IClassNameDefinitionByYearGroup } from '../../../../../../../_shared/models/IClassNameDefinitionByYearGroup';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { SnackbarService } from '@bromcom/ui';
import { TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';
import { CurriculumPlanService } from '../../../../../../services/curriculum-plan.service';
import { SchedulingService } from '../../../../../../services/scheduling.service';
import { PlanningRelationshipsService } from '../../../../../../services/planning-relationships.service';
import { IMenuItem, IMenuItemCommandEvent } from '../../../../../../../../../../../libs/ui/src/lib/components/menu/IMenuItem';
import { BaseModalComponent } from '../../../../../../../_shared/components/BaseModalComponent';

@Component({
  selector: 'bromcom-class-name-definition-modal',
  templateUrl: './class-name-definition-modal.component.html',
  styleUrls: ['./class-name-definition-modal.component.scss']
})
export class ClassNameDefinitionModalComponent extends BaseModalComponent implements OnInit, OnDestroy {
  @ViewChild('classNameDefinitionRef') classNameDefinitionRef!: ElementRef;
  @ViewChild('subjectRadioButtonGroup') subjectRadioButtonGroup!: ElementRef;
  @ViewChild('classNumberRadioButtonGroup') classNumberRadioButtonGroup!: ElementRef;

  timetableId!: number;
  selectedYearGroupControl = new FormControl<number | null>(null);
  classNameExampleControl = new FormControl<string>('');
  currentSelectedYearGroupId!: number;
  yearGroupList: IClassNameDefinition[] = [];
  yearOptions: { id: number | string, text: string }[] = [];

  yearOptionsMenu: IMenuItem[] = [];

  classNameItems: IClassNameItems[] = [];
  classNameItemsSeparator: IClassNameItems[] = [];
  classNameItemsResult: IClassNameItems[] = [];
  CLASS_NAME_TYPE = CLASS_NAME_TYPE;
  classNameDefinitionByYearGroup!: IClassNameDefinitionByYearGroup;
  isDuplicatesChecked = false;
  isDuplicatesFound = false;

  readonly unsubscribe$: Subject<void> = new Subject();

  constructor(
    private planningRelationShipsService: PlanningRelationshipsService,
    private curriculumPlanBlocksService: CurriculumPlanBlocksService,
    private schedulingService: SchedulingService,
    private curriculumPlanService: CurriculumPlanService,
    private classNameDefinitionService: ClassNameDefinitionService,
    private snackbar: SnackbarService,
    private translate: TranslateService
  ) {
    super();
  }

  ngOnInit() {
    this.planningRelationShipsService.timetableId$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(timetableId => {
        if (timetableId) {
          this.timetableId = timetableId
        }
      })

    this.getYearGroupList();

    this.classNameDefinitionService.getPossibleClassNameItems().subscribe(classNameItems => {
      this.classNameItems = classNameItems.map(item => {
        if (item.id === 5) {
          return {
            ...item,
            text: item.text.replace('(locked)', ' '),
            backgroundColor: CLASS_NAME_ITEMS_COLOR['FreeTextLocked'],
            borderColor: CLASS_NAME_ITEMS_BORDER_COLOR['FreeTextLocked']
          }
        }

        const classNameEnum = item.text.replace(' ', '');
        return {
          ...item,
          backgroundColor: CLASS_NAME_ITEMS_COLOR[classNameEnum as keyof typeof CLASS_NAME_ITEMS_COLOR],
          borderColor: CLASS_NAME_ITEMS_BORDER_COLOR[classNameEnum as keyof typeof CLASS_NAME_ITEMS_BORDER_COLOR]
        }
      });
      this.classNameItemsSeparator = classNameItems.filter(item => item.type === CLASS_NAME_TYPE.OPTIONAL_SEPARATOR)
    })

    this.selectedYearGroupControl.valueChanges
      .pipe()
      .subscribe(selectedYearGroupValue => {
        if (selectedYearGroupValue) {
          this.currentSelectedYearGroupId = selectedYearGroupValue;
          this.getClassNameDefinitionByYear();
        }
      })
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  drop(event: CdkDragDrop<IClassNameItems[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
      this.classNameDefinitionByYearGroup.classNameIds = this.classNameItemsResult.map(item => item.id);
    } else {
      if (event.item.data.id === 5) {
        this.snackbar.info(this.translate.instant('This Free Text must be included!'));
        return
      }

      if (event.previousContainer.id === '4' && event.container.id !== event.item.data.type.toString()) {
        this.snackbar.info(this.translate.instant('This item cannot be dropped here.'));
        return
      }

      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
      this.classNameItemsSeparator = this.classNameItems.filter(item => item.type === CLASS_NAME_TYPE.OPTIONAL_SEPARATOR)
      this.classNameDefinitionByYearGroup.classNameIds = this.classNameItemsResult.map(item => item.id);
      this.isDuplicatesChecked = false;
    }

    this.setExampleValue();
  }

  onCopySettingsTo(yeargroupId: number | string): void {
    if (typeof yeargroupId !== 'number' && yeargroupId.toString() !== 'all') {
      return;
    }

    const data = {
      sourceYearGroupId: this.currentSelectedYearGroupId,
      targetYearGroupId: yeargroupId.toString() === 'all' ? null : +yeargroupId,
      isCopyToAll: yeargroupId.toString() === 'all'
    }

    this.classNameDefinitionService.copyClassNameDefinitionByYearGroup(this.timetableId, data)
      .subscribe(() => {
        this.schedulingService.classNameDefinition$.next(true);
        this.curriculumPlanBlocksService.stateChanged$.next();
        this.schedulingService.stateChanged$.next();
        this.getYearGroupList();
        this.snackbar.success(this.translate.instant('Class Name Definition copied successfully'));

        const currentSelectedBlock = this.curriculumPlanService.currentSelectedBlock$.getValue();
        if (currentSelectedBlock) {
          this.curriculumPlanService.getBlock(currentSelectedBlock.id)
            .subscribe(block => this.curriculumPlanService.currentSelectedBlock$.next(block))
        }
      })
  }

  onCheck(): void {
    this.classNameDefinitionService.checkClassNameDefinitionByYearGroup(this.timetableId, this.currentSelectedYearGroupId, this.classNameDefinitionByYearGroup)
      .subscribe(res => {
        this.isDuplicatesChecked = true;
        this.isDuplicatesFound = res.hasAnyDuplicates;
      })
  }

  onSaveClassNameDefinition(): void {
    this.classNameDefinitionService.setClassNameDefinitionByYearGroup(this.timetableId, this.currentSelectedYearGroupId, this.classNameDefinitionByYearGroup)
      .subscribe(() => {
        this.schedulingService.classNameDefinition$.next(true);
        this.getYearGroupList();
        this.snackbar.saved(this.translate.instant('Class Name Definition saved successfully'));

        const currentSelectedBlock = this.curriculumPlanService.currentSelectedBlock$.getValue();
        if (currentSelectedBlock) {
          this.curriculumPlanService.getBlock(currentSelectedBlock.id)
            .subscribe(block => this.curriculumPlanService.currentSelectedBlock$.next(block))
        }

        this.classNameDefinitionRef.nativeElement.hide();
      })
  }

  onCloseClassNameDefinition(): void {
    this.getClassNameDefinitionByYear();
    this.isDuplicatesChecked = false;
    this.isDuplicatesFound = false;
  }

  async setExampleValue() {
    this.classNameDefinitionByYearGroup.subjectSetting = await this.subjectRadioButtonGroup.nativeElement.getValue()
    this.classNameDefinitionByYearGroup.classNumberSetting = await this.classNumberRadioButtonGroup.nativeElement.getValue()

    const exampleValue = this.classNameDefinitionByYearGroup.classNameIds
      .map(id => this.classNameItems
        .find(item => item.id === (id === 7
          ? this.classNameDefinitionByYearGroup.subjectSetting
          : id)
        )?.exampleValue
      ).join('');

    this.classNameExampleControl.setValue(exampleValue);
  }

  private getYearGroupList(): void {
    this.classNameDefinitionService.getYearGroupList(this.timetableId).subscribe(yearGroupList => {
      this.yearGroupList = yearGroupList.reverse().map(yearGroupElement => {
        return {
          ...yearGroupElement,
          id: yearGroupElement.yearGroupId,
          text: `${yearGroupElement.yearGroupName} - ${yearGroupElement.classNameDefinitionExample}`
        }
      }).sort((a, b) => Number(b.yearGroupName) - Number(a.yearGroupName));
      this.yearOptions = [];
      this.yearOptions.push({ id: 'all', text: this.translate.instant('All Year Groups') });
      this.yearOptions.push(...yearGroupList.map(yearGroupElement => {
        return {
          ...yearGroupElement,
          id: yearGroupElement.yearGroupId,
          text: yearGroupElement.yearGroupName
        }
      }));

      this.yearOptionsMenu = this.yearOptions.map(option => {
        return {
          label: option.text,
          value: option.id,
          command: (event: IMenuItemCommandEvent) => {
            this.onCopySettingsTo(option.id);
          }
        } as IMenuItem;
      })

      setTimeout(() => this.selectedYearGroupControl.setValue(this.currentSelectedYearGroupId ?? this.yearGroupList[0].id), 0);
    })
  }

  private getClassNameDefinitionByYear(): void {
    this.classNameDefinitionService.getClassNameDefinitionByYearGroup(this.timetableId, this.currentSelectedYearGroupId)
      .subscribe(res => {
        this.classNameDefinitionByYearGroup = res
        this.classNameItemsResult = res.classNameIds.map(id => this.classNameItems.find(item => item.id === id)!);

        if (this.isOpen) {
          this.subjectRadioButtonGroup.nativeElement.check(res.subjectSetting);
          this.classNumberRadioButtonGroup.nativeElement.check(res.classNumberSetting);
          this.setExampleValue();
        }
        
      })
  }

  show(): void {
    this.isOpen = true;
    setTimeout(() => {
      this.classNameDefinitionRef.nativeElement.show();
    }, 100)
    
  }
}
