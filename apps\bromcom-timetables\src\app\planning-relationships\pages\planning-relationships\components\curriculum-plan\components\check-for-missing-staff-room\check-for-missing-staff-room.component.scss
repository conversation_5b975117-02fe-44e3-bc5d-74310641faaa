@mixin displayFlexSpaceBetween {
  display: flex;
  justify-content: space-between;
}

.check-for-missing-modal {

  .modal-body {
    padding: 24px;

    .action-bar {
      @include displayFlexSpaceBetween;
      align-items: center;

      .switch {
        max-height: 32px;
        margin-right: 14px;
      }

      .search-field {
        min-width: 256px;
      }

      .action-buttons {
        @include displayFlexSpaceBetween;
        margin-bottom: 8px;

        .button {
          margin: 0 4px;
        }

        .button-group {
          margin-left: 16px;

          ::ng-deep &.bcm-button-group {
            border: none;
          }
        }
      }
    }
  }

  .table-container {
    height: 71vh;
  }
}
