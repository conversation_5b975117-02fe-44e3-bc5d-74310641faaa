import { customComparator, NOOP, transformToAGGridConfig} from '@bromcom/core';
import { AgGridNoDataComponent } from '@bromcom/ui';
import { AutoScheduleIssuesComponent } from './auto-schedule-issues.component';
import {
  AutoScheduleIssuesActionsComponent,
  IAutoScheduleIssuesActions
} from "./components/auto-schedule-issues-actions/auto-schedule-issues-actions.component";
import {BaseColDefOptionalDataParams} from "ag-grid-community/dist/lib/entities/colDef";
import {
  AutoScheduleReasonsComponent, IAutoScheduleReasons
} from "./components/auto-schedule-reasons/auto-schedule-reasons.component";


export function gridOptions(this: AutoScheduleIssuesComponent, config: IAutoScheduleIssuesActions & IAutoScheduleReasons) {
  const {
    onAcceptRow = NOOP,
    onOpenReasons = NOOP
  } = config

  const isAcceptedGetter = (params: BaseColDefOptionalDataParams) => {
    return params.data.isAccepted ?
      `<div class="accepted">${this.translate.instant('Accepted')}</div>` : '';
  }

  return transformToAGGridConfig({
    animateRows: false,
    suppressRowClickSelection: true,
    suppressClickEdit: true,
    suppressCellFocus: true,
    tooltipShowDelay: 500,
    noRowsOverlayComponent: AgGridNoDataComponent,
    noRowsOverlayComponentParams: {
      style: 'display: flex; justify-content: center; align-items: center; height: 100%; width: 100%; min-height: 162px; padding: 48px 0 0 0',
      noRowsMessageFunc: () => this.translate.instant(`No data available!`)
    },
    enableCellChangeFlash: false,
    getRowId: (params) => params.data.sessionIds[0],
    defaultColDef: {
      wrapText: true,
      autoHeight: true,
      flex: 1,
      comparator: customComparator
    },
    columnDefs: [
      {
        field: 'yearGroup',
        headerName: this.translate.instant('Year Group'),
        wrapHeaderText: true,
        minWidth: 120,
        comparator: customComparator,
        menuTabs: [],
      },
      {
        field: 'band',
        headerName: this.translate.instant('Band'),
        minWidth: 120,
        flex: 2.5,
        editable: true,
        wrapText: true,
        comparator: customComparator,
        menuTabs: [],
      },
      {
        field: 'block',
        headerName: this.translate.instant('Block'),
        minWidth: 180,
        menuTabs: [],
      },
      {
        field: 'blockPeriod',
        headerName: this.translate.instant('Block Period'),
        wrapHeaderText: true,
        minWidth: 120,
        menuTabs: [],
      },
      {
        field: 'period',
        headerName: this.translate.instant('Period'),
        minWidth: 120,
        flex: 1.5,
        valueGetter: params => {
          return params.data.issueDetails.length === 1
            ? params.data.issueDetails[0].period
            : "";
        },
        menuTabs: [],
      },
      {
        field: 'session',
        headerName: this.translate.instant('Class'),
        minWidth: 150,
        menuTabs: [],
      },
      {
        field: 'reason',
        headerName: this.translate.instant('Reason'),
        minWidth: 300,
        flex: 3,
        menuTabs: [],
        tooltipValueGetter: params => {
          const issueDetails = params.data.issueDetails;         
             
          if (issueDetails[0].issueEntityType == 8) {
            return params.value;
          }
        },      
        valueGetter: params => {
          const issueDetails = params.data.issueDetails;

          if (issueDetails.length === 1) {
            return issueDetails[0].reason;
          }

          const periodCountMap: Record<string, number> = {};

          issueDetails.forEach((item: { period: string }) => {
            if (periodCountMap[item.period]) {
              periodCountMap[item.period]++;
            } else {
              periodCountMap[item.period] = 1;
            }
          });

          return Object.entries(periodCountMap)
            .map(([period, count]) => `${period} - ${count} issue${count > 1 ? 's' : ''}`)
            .join(', ');
        },
        cellRenderer: AutoScheduleReasonsComponent,
        cellRendererParams: {
          onOpenReasons
        },
      },
      {
        field: 'suggestedAction',
        headerName: this.translate.instant('Suggested Action'),
        minWidth: 300,
        flex: 2.5,
        menuTabs: [],
        valueGetter: params => {
          return params.data.issueDetails.length === 1
            ? params.data.issueDetails[0].suggestedAction
            : "";
        },
        cellStyle: {
          wordBreak: 'break-word',
        }
      },
      {
        field: 'status',
        headerName: this.translate.instant('Status'),
        minWidth: 120,
        menuTabs: [],
        valueGetter: params => {
          return params.data.isAccepted;
        },
        cellStyle: { display: 'flex', justifyContent: 'center' },
        cellRenderer: isAcceptedGetter,
      },
      {
        field: 'action',
        headerName: this.translate.instant('Action'),
        minWidth: 125,
        sortable: false,
        menuTabs: [],
        cellStyle: { display: 'flex', justifyContent: 'center' },
        cellRenderer: AutoScheduleIssuesActionsComponent,
        cellRendererParams: {
          onAcceptRow
        }
      },
    ]
  })
}
