{"periodStructure": {"id": 191, "originalId": 9, "periodStructureName": "2023 - 1 Week - 6 Periods", "academicDaysModel": "0111110", "noOfDaysInCycle": 5, "startDate": "2022-06-17T00:00:00", "endDate": null, "weeks": [{"weekNumber": 1, "weekDisplayName": "1", "days": [{"dayOfWeek": 1, "dayDisplayName": "Monday", "periods": [{"id": 12835, "originalId": 696, "periodCode": "SESSION", "periodDisplayName": "AM", "startTime": "2023-03-28T08:00:00", "endTime": "2023-03-28T08:04:00"}, {"id": 12836, "originalId": 697, "periodCode": "PRESCHL", "periodDisplayName": "PS", "startTime": "2023-03-28T08:05:00", "endTime": "2023-03-28T08:29:00"}, {"id": 12837, "originalId": 698, "periodCode": "PERIOD", "periodDisplayName": "1", "startTime": "2023-03-28T08:30:00", "endTime": "2023-03-28T09:24:00"}, {"id": 12838, "originalId": 699, "periodCode": "PERIOD", "periodDisplayName": "2", "startTime": "2023-03-28T09:25:00", "endTime": "2023-03-28T10:14:00"}, {"id": 12839, "originalId": 700, "periodCode": "PERIOD", "periodDisplayName": "TG1", "startTime": "2023-03-28T10:15:00", "endTime": "2023-03-28T10:34:00"}, {"id": 392, "originalId": 0, "periodCode": "BREAK", "periodDisplayName": "BR", "startTime": "2023-03-19T10:35:00", "endTime": "2023-03-19T10:54:00"}, {"id": 12840, "originalId": 701, "periodCode": "PERIOD", "periodDisplayName": "TG2", "startTime": "2023-03-28T10:35:00", "endTime": "2023-03-28T10:54:00"}, {"id": 12841, "originalId": 702, "periodCode": "PERIOD", "periodDisplayName": "3", "startTime": "2023-03-28T10:55:00", "endTime": "2023-03-28T11:49:00"}, {"id": 12842, "originalId": 703, "periodCode": "PERIOD", "periodDisplayName": "4a", "startTime": "2023-03-28T11:50:00", "endTime": "2023-03-28T12:29:00"}, {"id": 12843, "originalId": 704, "periodCode": "PERIOD", "periodDisplayName": "4b", "startTime": "2023-03-28T12:30:00", "endTime": "2023-03-28T13:09:00"}, {"id": 128451, "originalId": 705, "periodCode": "SESSION", "periodDisplayName": "PM", "startTime": "2023-03-28T13:10:00", "endTime": "2023-03-28T13:14:00"}, {"id": 12844, "originalId": 706, "periodCode": "PERIOD", "periodDisplayName": "5", "startTime": "2023-03-28T13:15:00", "endTime": "2023-03-28T14:09:00"}, {"id": 12845, "originalId": 707, "periodCode": "PERIOD", "periodDisplayName": "6", "startTime": "2023-03-28T14:10:00", "endTime": "2023-03-28T14:59:00"}, {"id": 12846, "originalId": 708, "periodCode": "PERIOD", "periodDisplayName": "7", "startTime": "2023-03-28T15:00:00", "endTime": "2023-03-28T15:59:00"}]}, {"dayOfWeek": 2, "dayDisplayName": "Tuesday", "periods": [{"id": 12849, "originalId": 709, "periodCode": "SESSION", "periodDisplayName": "AM", "startTime": "2023-03-28T08:00:00", "endTime": "2023-03-28T08:04:00"}, {"id": 12850, "originalId": 710, "periodCode": "PRESCHL", "periodDisplayName": "PS", "startTime": "2023-03-28T08:05:00", "endTime": "2023-03-28T08:29:00"}, {"id": 12851, "originalId": 711, "periodCode": "PERIOD", "periodDisplayName": "1", "startTime": "2023-03-28T08:30:00", "endTime": "2023-03-28T09:24:00"}, {"id": 12852, "originalId": 712, "periodCode": "PERIOD", "periodDisplayName": "2", "startTime": "2023-03-28T09:25:00", "endTime": "2023-03-28T10:14:00"}, {"id": 12853, "originalId": 713, "periodCode": "PERIOD", "periodDisplayName": "TG1", "startTime": "2023-03-28T10:15:00", "endTime": "2023-03-28T10:34:00"}, {"id": 392, "originalId": 0, "periodCode": "BREAK", "periodDisplayName": "BR", "startTime": "2023-03-19T10:35:00", "endTime": "2023-03-19T10:54:00"}, {"id": 12854, "originalId": 714, "periodCode": "PERIOD", "periodDisplayName": "TG2", "startTime": "2023-03-28T10:35:00", "endTime": "2023-03-28T10:54:00"}, {"id": 12855, "originalId": 715, "periodCode": "PERIOD", "periodDisplayName": "3", "startTime": "2023-03-28T10:55:00", "endTime": "2023-03-28T11:49:00"}, {"id": 12856, "originalId": 716, "periodCode": "PERIOD", "periodDisplayName": "4a", "startTime": "2023-03-28T11:50:00", "endTime": "2023-03-28T12:29:00"}, {"id": 12857, "originalId": 717, "periodCode": "PERIOD", "periodDisplayName": "4b", "startTime": "2023-03-28T12:30:00", "endTime": "2023-03-28T13:09:00"}, {"id": 12858, "originalId": 718, "periodCode": "SESSION", "periodDisplayName": "PM", "startTime": "2023-03-28T13:10:00", "endTime": "2023-03-28T13:14:00"}, {"id": 12859, "originalId": 719, "periodCode": "PERIOD", "periodDisplayName": "5", "startTime": "2023-03-28T13:15:00", "endTime": "2023-03-28T14:09:00"}, {"id": 12860, "originalId": 720, "periodCode": "PERIOD", "periodDisplayName": "6", "startTime": "2023-03-28T14:10:00", "endTime": "2023-03-28T14:59:00"}, {"id": 12861, "originalId": 721, "periodCode": "PERIOD", "periodDisplayName": "7", "startTime": "2023-03-28T15:00:00", "endTime": "2023-03-28T15:59:00"}]}, {"dayOfWeek": 3, "dayDisplayName": "Wednesday", "periods": [{"id": 12862, "originalId": 722, "periodCode": "SESSION", "periodDisplayName": "AM", "startTime": "2023-03-28T08:00:00", "endTime": "2023-03-28T08:04:00"}, {"id": 12863, "originalId": 723, "periodCode": "PRESCHL", "periodDisplayName": "PS", "startTime": "2023-03-28T08:05:00", "endTime": "2023-03-28T08:29:00"}, {"id": 12864, "originalId": 724, "periodCode": "PERIOD", "periodDisplayName": "1", "startTime": "2023-03-28T08:30:00", "endTime": "2023-03-28T09:24:00"}, {"id": 12865, "originalId": 725, "periodCode": "PERIOD", "periodDisplayName": "2", "startTime": "2023-03-28T09:25:00", "endTime": "2023-03-28T10:14:00"}, {"id": 12866, "originalId": 726, "periodCode": "PERIOD", "periodDisplayName": "TG1", "startTime": "2023-03-28T10:15:00", "endTime": "2023-03-28T10:34:00"}, {"id": 392, "originalId": 0, "periodCode": "BREAK", "periodDisplayName": "BR", "startTime": "2023-03-19T10:35:00", "endTime": "2023-03-19T10:54:00"}, {"id": 12867, "originalId": 727, "periodCode": "PERIOD", "periodDisplayName": "TG2", "startTime": "2023-03-28T10:35:00", "endTime": "2023-03-28T10:54:00"}, {"id": 12868, "originalId": 728, "periodCode": "PERIOD", "periodDisplayName": "3", "startTime": "2023-03-28T10:55:00", "endTime": "2023-03-28T11:49:00"}, {"id": 12869, "originalId": 729, "periodCode": "PERIOD", "periodDisplayName": "4a", "startTime": "2023-03-28T11:50:00", "endTime": "2023-03-28T12:29:00"}, {"id": 12870, "originalId": 730, "periodCode": "PERIOD", "periodDisplayName": "4b", "startTime": "2023-03-28T12:30:00", "endTime": "2023-03-28T13:09:00"}, {"id": 12871, "originalId": 731, "periodCode": "SESSION", "periodDisplayName": "PM", "startTime": "2023-03-28T13:10:00", "endTime": "2023-03-28T13:14:00"}, {"id": 12872, "originalId": 732, "periodCode": "PERIOD", "periodDisplayName": "5", "startTime": "2023-03-28T13:15:00", "endTime": "2023-03-28T14:09:00"}, {"id": 12873, "originalId": 733, "periodCode": "PERIOD", "periodDisplayName": "6", "startTime": "2023-03-28T14:10:00", "endTime": "2023-03-28T14:59:00"}, {"id": 12874, "originalId": 734, "periodCode": "PERIOD", "periodDisplayName": "7", "startTime": "2023-03-28T15:00:00", "endTime": "2023-03-28T15:59:00"}]}, {"dayOfWeek": 4, "dayDisplayName": "Thursday", "periods": [{"id": 12875, "originalId": 735, "periodCode": "SESSION", "periodDisplayName": "AM", "startTime": "2023-03-28T08:00:00", "endTime": "2023-03-28T08:04:00"}, {"id": 12876, "originalId": 736, "periodCode": "PRESCHL", "periodDisplayName": "PS", "startTime": "2023-03-28T08:05:00", "endTime": "2023-03-28T08:29:00"}, {"id": 12877, "originalId": 737, "periodCode": "PERIOD", "periodDisplayName": "1", "startTime": "2023-03-28T08:30:00", "endTime": "2023-03-28T09:24:00"}, {"id": 12878, "originalId": 738, "periodCode": "PERIOD", "periodDisplayName": "2", "startTime": "2023-03-28T09:25:00", "endTime": "2023-03-28T10:14:00"}, {"id": 12879, "originalId": 739, "periodCode": "PERIOD", "periodDisplayName": "TG1", "startTime": "2023-03-28T10:15:00", "endTime": "2023-03-28T10:34:00"}, {"id": 392, "originalId": 0, "periodCode": "BREAK", "periodDisplayName": "BR", "startTime": "2023-03-19T10:35:00", "endTime": "2023-03-19T10:54:00"}, {"id": 12880, "originalId": 740, "periodCode": "PERIOD", "periodDisplayName": "TG2", "startTime": "2023-03-28T10:35:00", "endTime": "2023-03-28T10:54:00"}, {"id": 12881, "originalId": 741, "periodCode": "PERIOD", "periodDisplayName": "3", "startTime": "2023-03-28T10:55:00", "endTime": "2023-03-28T11:49:00"}, {"id": 12882, "originalId": 742, "periodCode": "PERIOD", "periodDisplayName": "4a", "startTime": "2023-03-28T11:50:00", "endTime": "2023-03-28T12:29:00"}, {"id": 12883, "originalId": 743, "periodCode": "PERIOD", "periodDisplayName": "4b", "startTime": "2023-03-28T12:30:00", "endTime": "2023-03-28T13:09:00"}, {"id": 12884, "originalId": 744, "periodCode": "SESSION", "periodDisplayName": "PM", "startTime": "2023-03-28T13:10:00", "endTime": "2023-03-28T13:14:00"}, {"id": 12885, "originalId": 745, "periodCode": "PERIOD", "periodDisplayName": "5", "startTime": "2023-03-28T13:15:00", "endTime": "2023-03-28T14:09:00"}, {"id": 12886, "originalId": 746, "periodCode": "PERIOD", "periodDisplayName": "6", "startTime": "2023-03-28T14:10:00", "endTime": "2023-03-28T14:59:00"}, {"id": 12887, "originalId": 747, "periodCode": "PERIOD", "periodDisplayName": "7", "startTime": "2023-03-28T15:00:00", "endTime": "2023-03-28T15:59:00"}]}, {"dayOfWeek": 5, "dayDisplayName": "Friday", "periods": [{"id": 12888, "originalId": 748, "periodCode": "SESSION", "periodDisplayName": "AM", "startTime": "2023-03-28T08:00:00", "endTime": "2023-03-28T08:04:00"}, {"id": 12889, "originalId": 749, "periodCode": "PRESCHL", "periodDisplayName": "PS", "startTime": "2023-03-28T08:05:00", "endTime": "2023-03-28T08:29:00"}, {"id": 12890, "originalId": 750, "periodCode": "PERIOD", "periodDisplayName": "1", "startTime": "2023-03-28T08:30:00", "endTime": "2023-03-28T09:24:00"}, {"id": 12891, "originalId": 751, "periodCode": "PERIOD", "periodDisplayName": "2", "startTime": "2023-03-28T09:25:00", "endTime": "2023-03-28T10:14:00"}, {"id": 12892, "originalId": 752, "periodCode": "PERIOD", "periodDisplayName": "TG1", "startTime": "2023-03-28T10:15:00", "endTime": "2023-03-28T10:34:00"}, {"id": 392, "originalId": 0, "periodCode": "BREAK", "periodDisplayName": "BR", "startTime": "2023-03-19T10:35:00", "endTime": "2023-03-19T10:54:00"}, {"id": 12893, "originalId": 753, "periodCode": "PERIOD", "periodDisplayName": "TG2", "startTime": "2023-03-28T10:35:00", "endTime": "2023-03-28T10:54:00"}, {"id": 12894, "originalId": 754, "periodCode": "PERIOD", "periodDisplayName": "3", "startTime": "2023-03-28T10:55:00", "endTime": "2023-03-28T11:49:00"}, {"id": 12895, "originalId": 755, "periodCode": "PERIOD", "periodDisplayName": "4a", "startTime": "2023-03-28T11:50:00", "endTime": "2023-03-28T12:29:00"}, {"id": 12896, "originalId": 756, "periodCode": "PERIOD", "periodDisplayName": "4b", "startTime": "2023-03-28T12:30:00", "endTime": "2023-03-28T13:09:00"}, {"id": 12897, "originalId": 757, "periodCode": "SESSION", "periodDisplayName": "PM", "startTime": "2023-03-28T13:10:00", "endTime": "2023-03-28T13:14:00"}, {"id": 12898, "originalId": 758, "periodCode": "PERIOD", "periodDisplayName": "5", "startTime": "2023-03-28T13:15:00", "endTime": "2023-03-28T14:09:00"}, {"id": 12899, "originalId": 759, "periodCode": "PERIOD", "periodDisplayName": "6", "startTime": "2023-03-28T14:10:00", "endTime": "2023-03-28T14:59:00"}, {"id": 12900, "originalId": 760, "periodCode": "PERIOD", "periodDisplayName": "7", "startTime": "2023-03-28T15:00:00", "endTime": "2023-03-28T15:59:00"}]}]}]}, "id": 191, "projectId": 34, "timeTableName": "test3", "createdDate": "0001-01-01T00:00:00", "createdBy": null, "modifiedDate": null, "modifiedBy": null, "isApplied": false, "startDate": null, "endDate": null, "versionOf": null}