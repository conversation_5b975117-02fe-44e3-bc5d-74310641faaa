<div class="staff-side">
  <div class="session-staff-icon">
    <bcm-icon *ngIf="session.additionalStaffIds.length"
              class="plus-icon"
              icon="far fa-plus">
    </bcm-icon>
    <bcm-icon class="icon" icon="far fa-users-class"
              [class.pointer]="!isFeedbackSession"
              [class.staff-conflict]="staffConflictedSessionIds.includes(session.id)"
              [class.staff-filter-active]="staffToSessionActiveId === session.id"
              (click)="staffToSession($event)"></bcm-icon>
  </div>

  <div *ngIf="session.additionalStaffIds?.length; else simpleMainStaffTemplate" class="text">
    <bcm-tooltip
      [message]="session.mainStaffId | staffTooltipPipe:session.additionalStaffIds:staffs"
      trigger="hover">
      <div class="session-main-staff">
        {{session.mainStaffId | staffPipe:staffs}}
      </div>
    </bcm-tooltip>
  </div>

  <ng-template #simpleMainStaffTemplate>
    <div class="text">
      <bcm-tooltip *ngIf="session.mainStaffId; else empyMainStaffIdTemplate"
                   [message]="'Main Staff: ' + (session.mainStaffId | staffPipe:staffs)"
                   trigger="hover">
        <div class="session-staff">{{session.mainStaffId | staffPipe:staffs}}</div>
      </bcm-tooltip>
      <ng-template #empyMainStaffIdTemplate>
        <bcm-tooltip 
                   [message]="'Main Staff: --'"
                   trigger="hover">
        <div class="session-staff">--</div>
      </bcm-tooltip>
      </ng-template>
    </div>
  </ng-template>
</div>
