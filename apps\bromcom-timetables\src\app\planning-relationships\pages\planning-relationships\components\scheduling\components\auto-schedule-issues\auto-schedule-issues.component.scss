@import 'apps/bromcom-timetables/src/assets/styles/variables';
@import 'apps/bromcom-timetables/src/app/planning-relationships/pages/planning-relationships/components/scheduling/components/timetable-specification/styles/tts-ag-grid';

.auto-schedule-issues-container {

  .action-bar {
    @include action-bar;
  }

  .table-container {
    @include table-container;

    ::ng-deep .ag-cell {
      height: inherit !important;
      line-height: unset;
    }

    ::ng-deep .ag-header-cell {
      border-right: none;
    }
  }

  .export-schedule-button {
    padding: 0px 2px 4px 12px;
  }

  ::ng-deep .accepted {
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 10px;
    background: $color-emerald-100;
    border-radius: 20px;
    color: $color-emerald-600;
    height: 26px;
  }

  .rerun-auto-schedule-box  {
    padding-left: 12px;
    padding-right: 12px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    background: $color-blue-50;
    border: $color-blue-300;
    border-radius: 8px;
    margin-top: 15px;

    .info {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }

    .info-icon {
      margin-top: 6px;
      color: var(--bcm-new-ds-color-blue-800);
    }

    .text {
      display: flex;
      align-items: center;
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      padding-left: 8px;
      padding-right: 8px;
      color: var(--bcm-new-ds-color-blue-800);
    }

    .update-button {
      display: flex;
      align-items: flex-end;
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      padding-left: 8px;
      padding-right: 8px;
    }

    .update-button:active {
      background-color: $color-blue-50 !important;
    }
  }
}

.auto-schedule-reasons-modal {
  .modal-body {
    max-height: 59vh;
    overflow: auto;
    margin: 24px;

    .expanded-container {
      height: 350px;
      width: 940px;
    }
  }

  .footer {
    display: flex;
    justify-content: flex-end;
  }
}

@media only screen and (max-height: 800px) {
  .auto-schedule-issues-container .table-container {
    height: 40vh !important;
  }
}
