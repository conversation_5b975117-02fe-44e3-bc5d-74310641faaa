<ng-container *ngIf="isOpen">
    <bcm-modal size="{{showConfirmationView ? 'small' : 'xlarge'}}" class="substitute-staff-modal" [ngClass]="{'confirmation-view': !showHeader}" (bcm-modal-before-close)="onClose()" #substitueStaffModal>
      <bcm-modal-header>
        <div *ngIf="showHeader">{{ 'Substitute Staff' | translate }}</div>
      </bcm-modal-header>
  
      <div class="modal-body">
        <ng-container *ngIf="!showConfirmationView; else confirmationView">
          <div class="action-bar">
            <div class="left-side">
              <bromcom-input-field class="search-field" [formControl]="searchControl" [icon]="'fal fa-search'"
                                   [placeholder]="'Search' | translate "></bromcom-input-field>
            </div>
          </div>
  
          <div class="table-container">
            <ag-grid-angular style="width: 100%; height:100%;"
                             class="ag-theme-alpine"
                             domLayout="normal"
                             [rowData]="rowData"
                             [tooltipInteraction]="true"
                             [gridOptions]="gridOptions"
                             (gridReady)="onGridReady($event)">
            </ag-grid-angular>
          </div>
        </ng-container>
        <ng-template #confirmationView>
          <div class="substitute-confirmation-holder">
            <div class="info-holder">
              <bcm-icon class="icon info-icon" icon="far fa-info-circle"></bcm-icon>
            </div>
            <div class="confirmation-label-holder">
              <label class="confirmation-label">{{confirmationMessage}}</label>
            </div>
          </div>
        </ng-template>
      </div>
  
      <bcm-modal-footer>
        <ng-container *ngIf="!showConfirmationView; else confirmationViewFooter">
          <bcm-button kind="ghost" data-dismiss (click)="onCancel()">{{'Cancel' | translate }}</bcm-button>
          <bcm-button [disabled]="!selectedStaffData"
                      (click)="onSubstituteStaff()">{{'Substitute Staff' | translate }}</bcm-button>
        </ng-container>
        <ng-template #confirmationViewFooter>
          <div class="substitute-confirmation-butons">
            <div>
              <bcm-button kind="ghost" icon="far fa-arrow-left"
                          (click)="onBackClick()">{{'Back' | translate }}</bcm-button>
            </div>
            <div class="confirmation-action-buttons">
              <bcm-button kind="ghost" data-dismiss (click)="onCancel()">{{'Cancel' | translate }}</bcm-button>
              <bcm-button icon="far fa-save"
                          (click)="substituteStaff()">{{'Save' | translate }}</bcm-button>
            </div>
          </div>
        </ng-template>
      </bcm-modal-footer>
    </bcm-modal>
  </ng-container>
  
  
