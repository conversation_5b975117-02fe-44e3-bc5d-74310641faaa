@import '../../../../../../../../../../../../bromcom-timetables/src/assets/styles/variables.scss';


.filter-popup {
  width: 370x;
  padding: 1px;

  .filter-header {
    display: flex;
    height: 25px;
    justify-content: center;
    align-items: center;
    border-bottom: 2px solid $color-primary-blue-600;
    margin-bottom: 8px;

    .filter-icon {
      margin-bottom: 6px;

      i {
        font-size: 14px;
        color: $color-blue-500;
      }
    }
  }

  .search {
    margin-bottom: 3px;
  }

  .filter-options {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    align-self: stretch;
    font-size: 14px;
    color: $color-primary-blue-600;
    border-bottom: 1px solid $color-blue-200;

    span {
      margin-bottom: 8px;
      cursor: pointer;
    }
  }

  .vertical {
    margin-top: 4px;
    max-height: 300px;
    overflow-y: auto;
    gap: 2px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }

  .filter-buttons {
    display: flex;
    height: 32px;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    margin-top: 16px;

    .button {
      width: auto !important;
    }
  }

  .data-item {
    display: flex;
  }
}

.block-vertical {
  padding: 0px !important;
}

::ng-deep .mat-mdc-menu-panel.mat-mdc-menu-panel {
  max-width: 370px !important;
}

.switch-container {
  margin-left: auto;
  margin-right: auto;

  display: flex;
  flex-direction: column;
  gap: 4px;

  .switch-container-inner {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 4px;
    padding-right: 12px; 
  }
   
  .switch-container-inner:has(bromcom-switch .bcm-switch.checked) {
    background: $color-blue-100;
    border-radius: 3px;
  }
  
  .fa-info-circle{
    font-size: small;
  }

  bromcom-switch {
    width: 100%; 
    padding-top: 4px;

    ::ng-deep .bcm-switch {
      width: 100%;
      
      &.checked {
        .bcm-switch__container {
          background: $color-blue-100;
          border-radius: 3px;
        }
      }

      .bcm-switch__container {
        display: flex;
        width: 100%;
        flex-direction: row-reverse;
        justify-content: flex-end;

        >.bcm-switch__label {          
          margin-left: 5px;
        }
      }
      
    }
  }
}
