<div class="staffing">
  <div class="action-row">
    <div class="left">
      <div class="search">
        <bromcom-input-field [formControl]="staffingSearchControl"
                             [icon]="'fal fa-search'"
                             [placeholder]="'Search' | translate"
        ></bromcom-input-field>
      </div>

      <bcm-button icon="far fa-redo-alt" (click)="updateStaffing()">
        {{'Update' | translate}}
      </bcm-button>
    </div>


    <bromcom-switch class="switch" [label]="'Expand All' | translate"
                    [formControl]="expandSwitchControl"></bromcom-switch>
  </div>

  <div class="flex-row">
    <div class="chip-container" *ngFor="let filterValue of filterModelValues">
      <bcm-chip color="green" size="large" type="dismissable"
                (bcm-chip-dismiss)="removeFilterValue(filterValue)">{{filterValue}}</bcm-chip>
    </div>
  </div>

  <div class="ag-grid-container" [ngClass]="{'is-filter-applied': isFilterApplied}">
    <ag-grid-angular *ngIf="isColumnsInitFinished"
                     id="staffing-grid"
                     [ngStyle]="{width: '100%', height: '100%'}"
                     [ngClass]="{'scroll-visible': dataLength > (filterModelValues ? 5 : 11)}"
                     style="width: 100%;"
                     class="ag-theme-alpine staffing"
                     domLayout='normal'
                     [gridOptions]="staffingGridOptions"
                     (gridReady)="onGridReady($event)">
    </ag-grid-angular>
  </div>
</div>
