@import '../../../../../../../../../bromcom-timetables/src/assets/styles/variables.scss';

::ng-deep .overview-filter .filter-popup {
  border: 1px solid $color-blue-grey-300;
}

.filter-popup {
  width: 256px;
  background-color: $color-white-0;

  .filter-header {
    display: flex;
    height: 25px;
    justify-content: center;
    align-items: center;
    border-bottom: 2px solid $color-primary-blue-600;
    margin-bottom: 8px;

    .filter-icon {
      margin-bottom: 6px;

      i {
        font-size: 14px;
        color: $color-blue-500;
      }
    }
  }

  .search {
    margin-bottom: 3px;
  }

  .filter-options {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    align-self: stretch;
    font-size: 14px;
    color: $color-primary-blue-600;
    border-bottom: 1px solid $color-blue-200;

    span {
      margin-bottom: 8px;
      cursor: pointer;
    }
  }

  .vertical {
    margin-top: 4px;
    max-height: 300px;
    overflow-y: auto;
    gap: 2px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }

  .filter-buttons {
    display: flex;
    height: 32px;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    margin-top: 16px;

    .button {
      width: auto !important;
    }
  }

  .data-item {
    display: flex;
  }
  
  bromcom-checkbox {
    ::ng-deep .bcm-checkbox {
      .bcm-checkbox__input {
        .bcm-checkbox__label .slot .text-holder span:not(.list-circle) {
          max-width: 157px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .switch-container {
    width: 90%;
    margin-left: auto;
    margin-right: auto;

    bromcom-switch {
      width: 100%;

      ::ng-deep .bcm-switch {
        width: 100%;

        .bcm-switch__container {
          display: block;
          width: 100%;

          > bcm-label {
            float: left;
            margin-top: 8px;
            max-width: 157px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          > .bcm-switch__label {
            float: right;
          }
        }
      }
    }
  }
}
