import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import {
  GeneralModalComponent
} from '../../../../../../../../../_shared/components/general-modal/general-modal.component';
import { GridOptions, GridReadyEvent, ICellRendererParams } from 'ag-grid-community';
import { GridApi } from 'ag-grid-community/dist/lib/gridApi';
import { FormControl } from '@angular/forms';
import { IYearGroup } from '../../../../../../../../../_shared/models/IYearGroup';
import { ISubject } from '../../../../../../../../../_shared/models/ISubject';
import { Subject, switchMap, takeUntil } from 'rxjs';
import { TimetableSpecificationsService } from '../../../../../../../../services/timetable-specifications.service';
import { RelationshipsService } from '../../../../../../../../services/relationships.service';
import { PlanningRelationshipsService } from '../../../../../../../../services/planning-relationships.service';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@bromcom/ui';
import { INCLUDED_TYPES } from '../../../../../../../../../_shared/enums/IncludedTypes';
import { gridOptions } from './tts-subject-on-days-gridoptions';
import { ITTSSubjectOnDays } from '../../../../../../../../../_shared/models/ITTSSubjectOnDays';
import { NewTimetableService } from '../../../../../../../../../timetables/services/new-timetable.service';
import { IWithPeriodStructureResponse } from '../../../../../../../../../_shared/models/IWithPeriodStructureResponse';

@Component({
  selector: 'bromcom-tts-subject-on-days',
  templateUrl: './tts-subject-on-days.component.html',
  styleUrls: ['./tts-subject-on-days.component.scss']
})
export class TtsSubjectOnDaysComponent implements OnInit, OnDestroy {
  @ViewChild('ttsSubjectRelationshipsModal', { static: false }) ttsSubjectRelationshipsModal!: ElementRef;
  @ViewChild('deleteSubjectOnDaysWarningModal') deleteSubjectOnDaysWarningModal!: GeneralModalComponent;
  @ViewChild('excludeSubjectRelationshipWarningModal') excludeSubjectRelationshipWarningModal!: GeneralModalComponent;

  @Input() timetableId!: number;
  params!: GridReadyEvent;
  gridApi!: GridApi;
  gridOptions!: GridOptions;
  searchControl = new FormControl(null);
  yearGroups: Partial<IYearGroup>[] = [];
  subjectOptions: Partial<ISubject>[] = [];
  gridData: ITTSSubjectOnDays[] = [];
  INCLUDED_TYPES = INCLUDED_TYPES;
  viewType: INCLUDED_TYPES = INCLUDED_TYPES.ACTIVE;
  deleteRowId!: number;
  dayList: any[] = [];
  isRemoveBulkDisabled = true;
  excludeRowIds: number[] = [];

  readonly unsubscribe$: Subject<void> = new Subject();

  constructor(
    private timetableSpecificationsService: TimetableSpecificationsService,
    protected relationshipsService: RelationshipsService,
    protected planningRelationshipsService: PlanningRelationshipsService,
    private timetableService: NewTimetableService,
    protected translate: TranslateService,
    private snackbar: SnackbarService
  ) {
  }

  ngOnInit(): void {
    this.relationshipsService.subjectsData$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(subjects => this.subjectOptions = subjects.map(subject => ({
        id: subject.id,
        name: subject.name,
        text: subject.name,
        code: subject.code,
        color: '#' + subject.color
      })))

    this.planningRelationshipsService.yearGroupsData$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(yearGroups => {
        this.yearGroups = yearGroups
          .filter((yearGroup) => !yearGroup.isExcluded)
          .sort((a, b) => Number(b.name) - Number(a.name));
      })

    this.timetableService.getPeriodStructureByTimetableId(this.timetableId).subscribe((periodStructure) => {
      const periods = this.markPeriodsAndRemoveAMPMPeriods(periodStructure);
      this.dayList = periods?.periodStructure.weeks
        .map((week) =>
          week.days.map((day) => day.periods.map(period => {
            return {
              ...period,
              text: `${day.dayDisplayName.slice(0, 3)} (${week.weekDisplayName}) P${period.periodDisplayName}`,
              id: period.id
            };
          })).flat()
        )
        .flat();

      setTimeout(() => {
        this.setRowData();
      }, 50)
    });

    this.gridOptions = gridOptions.call(this, {
      onAddNewRow: this.onAddNewRow.bind(this),
      onAcceptNewRow: this.onAcceptNewRow.bind(this),
      onCancelAddingNewRow: this.onCancelAddingNewRow.bind(this),
      onEditRow: this.onEditRow.bind(this),
      onDeleteRow: this.onDeleteRowClicked.bind(this),
      onAcceptRow: this.onAcceptRow.bind(this),
      onCancelEditRow: this.onCancelEditRow.bind(this),
      onExcludeRow: this.onExcludeRow.bind(this),
      onIncludeRow: this.onIncludeRow.bind(this)
    });

    this.searchControl.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(searchValue => {
        this.gridApi.setQuickFilter(searchValue ?? '');
      })

    this.timetableSpecificationsService.getSubjectOnDaysList(this.timetableId)
      .subscribe(data => {
        this.gridData = data;
        this.setRowData();
      })
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  onGridReady(params: GridReadyEvent): void {
    this.params = params;
    this.gridApi = params.api;
    this.gridApi.setDomLayout('normal');
  }

  onAddNewRow(params: ICellRendererParams): void {
    this.gridOptions.api?.setPinnedBottomRowData([
      {
        id: 'newRowId',
        yearGroupIds: [],
        subjectId: null,
        notAvailablePeriodIds: null,
        undesirablePeriodIds: null,
        preferredPeriodIds: null,
        isExcluded: false
      }
    ]);
    const rowColumns = this.params.columnApi.getColumns() ?? [];
    this.gridApi.startEditingCell({ rowIndex: 0, rowPinned: 'bottom', colKey: rowColumns[1]?.getColId() });
  }

  onAcceptNewRow(params: ICellRendererParams) {
    this.timetableSpecificationsService.addSubjectOnDays(this.timetableId, {
      ...params.data,
      id: 0
    }).pipe(switchMap(() => this.timetableSpecificationsService.getSubjectOnDaysList(this.timetableId)))
      .subscribe({
        next: data => {
          this.gridData = data;
          this.setRowData();
          this.gridOptions.api?.setPinnedBottomRowData([{}]);
          this.snackbar.saved();
        },
        error: ({ error }) => {
          if (error?.validationErrors && error?.validationErrors[0]) {
            this.snackbar.error(error.validationErrors[0].errorMessage);
          }
          const rowColumns = this.params.columnApi.getColumns() ?? [];
          this.gridApi.startEditingCell({ rowIndex: 0, rowPinned: 'bottom', colKey: rowColumns[1]?.getColId() });
        }
      })
  }

  onCancelAddingNewRow(): void {
    this.gridOptions.api?.setPinnedBottomRowData([{}]);
  }

  onEditRow(params: ICellRendererParams): void {
    const rowIndex = params.node.rowIndex;
    const rowColumns = this.params.columnApi.getColumns() ?? []
    if (rowIndex || rowIndex === 0) {
      this.gridApi.startEditingCell({ rowIndex, colKey: rowColumns[0].getColId() })
    }
  }

  onAcceptRow(params: ICellRendererParams): void {
    this.timetableSpecificationsService.editSubjectOnDays(params.data.id, params.data)
      .pipe(switchMap(() => this.timetableSpecificationsService.getSubjectOnDaysList(this.timetableId)))
      .subscribe(
        {
          next: data => {
            this.gridData = data;
            this.setRowData();
            this.snackbar.saved();
          },
          error: ({ error }) => {
            if (error?.validationErrors && error?.validationErrors[0]) {
              this.snackbar.error(error.validationErrors[0].errorMessage);
            }
            const rowIndex = params.node.rowIndex;
            const rowColumns = this.params.columnApi.getColumns() ?? [];
            if (rowIndex || rowIndex === 0) {
              this.gridApi.startEditingCell({ rowIndex, colKey: rowColumns[0]?.getColId() });
            }
          }
        })
  }

  onCancelEditRow(params: ICellRendererParams): void {
    this.timetableSpecificationsService.getSubjectOnDaysList(this.timetableId)
      .subscribe(data => {
        this.gridData = data;
        this.setRowData();
      })
  }

  onDeleteRowClicked(params: ICellRendererParams): void {
    this.deleteRowId = params.data.id;
    this.deleteSubjectOnDaysWarningModal.show();
  }

  onDeleteRow(): void {
    this.deleteSubjectOnDaysWarningModal.hide();
    this.timetableSpecificationsService.deleteSubjectOnDays(this.deleteRowId)
      .pipe(switchMap(() => this.timetableSpecificationsService.getSubjectOnDaysList(this.timetableId)))
      .subscribe(data => {
        this.gridData = data;
        this.setRowData();
        this.snackbar.success(this.translate.instant('Successful operation'));
      })
  }

  onExcludeRow(params: ICellRendererParams): void {
    this.excludeRowIds = [params.data.id];
    this.excludeSubjectRelationshipWarningModal.show();
  }

  onIncludeRow(params: ICellRendererParams): void {
    this.excludeIncludeRequest({ isExcluded: false, ids: [params.data.id] })
  }

  excludeIncludeBulk(isExcluded: boolean): void {
    this.gridApi.stopEditing();
    this.gridOptions.api?.setPinnedBottomRowData([{}]);
    const ids = this.gridApi.getSelectedRows().map(row => row.id);
    if (!isExcluded) {
      this.excludeIncludeRequest({ isExcluded, ids })
    } else {
      this.excludeRowIds = ids;
      this.excludeSubjectRelationshipWarningModal.show();
    }
  }

  excludeIncludeRequest(data: { isExcluded: boolean, ids: number[] }) {
    this.excludeSubjectRelationshipWarningModal?.modalComponentRef?.nativeElement.hide();
    this.timetableSpecificationsService.excludeSubjectOnDays(data)
      .pipe(switchMap(() => this.timetableSpecificationsService.getSubjectOnDaysList(this.timetableId)))
      .subscribe(data => {
        this.gridData = data;
        this.setRowData();
        this.snackbar.success(this.translate.instant('Successful operation'));
      })
  }

  viewTypeChange(event: Event): void {
    this.gridApi.stopEditing();
    if ((event as CustomEvent).detail.innerText.toLowerCase() === INCLUDED_TYPES.ACTIVE) {
      this.viewType = INCLUDED_TYPES.ACTIVE
      this.gridOptions.api?.setPinnedBottomRowData([{}]);
    } else {
      this.viewType = INCLUDED_TYPES.EXCLUDED
      this.gridOptions.api?.setPinnedBottomRowData([]);
    }
    this.setRowData();
  }

  setRowData(): void {
    this.viewType === INCLUDED_TYPES.ACTIVE
      ? setTimeout(() => {
        this.gridApi?.setRowData(this.gridData.filter(data => !data.isExcluded));
        this.gridApi?.redrawRows();
      }, 0)
      : setTimeout(() => {
        this.gridApi?.setRowData(this.gridData.filter(data => data.isExcluded));
        this.gridApi?.redrawRows();
      }, 0)
  }

  private markPeriodsAndRemoveAMPMPeriods(periodStructure: IWithPeriodStructureResponse) {
    const filtered = {
      ...periodStructure,
      periodStructure: {
        ...periodStructure.periodStructure,
        weeks: periodStructure.periodStructure.weeks.map((week) => {
          week.days = week.days.map((day) => {
            day.periods = day.periods
              .filter((period) => (['PERIOD'].includes(period.periodCode)))
            return day;
          });
          return week;
        })
      }
    };
    return filtered;
  }
}
