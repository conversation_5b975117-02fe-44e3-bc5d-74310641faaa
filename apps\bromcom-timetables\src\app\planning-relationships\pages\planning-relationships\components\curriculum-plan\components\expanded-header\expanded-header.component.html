<div class="expanded-header">
  <div class="header">
    <div class="circle" *ngIf="selectedBlock">{{BLOCK_TYPE[selectedBlock.blockTypeId]}}</div>

    <div class="code" *ngIf="!editBlockCode"
         (dblclick)="onEditBlockCode()">
      {{selectedBlock?.blockCode}}
    </div>

    <div class="code" *ngIf="editBlockCode">
      <bcm-tooltip [message]="editCode.errors?.['custom']"
                   trigger="hover"
                   class="name"
                   open-delay="500">
        <bcm-icon class="icon error"
                  [hidden]="!editCode.errors?.['custom']"
                  icon="fas fa-times-circle">
        </bcm-icon>
      </bcm-tooltip>

      <input type="text"
             [formControl]="editCode"
             maxlength="3"
             class="input"
             bromcomNoLeadingSpace>
      <div class="icon-wrapper">
        <bcm-icon class="icon"
                  icon="far fa-check"
                  [class.disabled]="editCode.errors?.['required']"
                  (click)="saveCode()">
        </bcm-icon>
      </div>
      <div class="icon-wrapper">
        <bcm-icon class="icon" icon="far fa-times" (click)="cancelCode()"></bcm-icon>
      </div>
    </div>

    <div>
      <bcm-icon class="icon"
                icon="far fa-ellipsis-h"
                (click)="openBlockActionsMenu()">
      </bcm-icon>

      <bromcom-curriculum-plan-block-actions *ngIf=selectedBlock
                                             [block]="selectedBlock"
                                             (transformClicked)="transformBlock($event)"
                                             (copyClicked)="copyBlock($event)"
                                             (deleteClicked)="deleteBlock(selectedBlock!.id)"
                                             (unScheduleClicked)="unScheduleSessions($event)"
                                             (spreadToBandsClicked)="spreadToBands($event)"
                                             (splitToBandsClicked)="splitToBands($event)"
                                             (openEditClassCodesModalEvent)="openEditClassCodesModal($event)"
                                             (openLinkBlocksModalComponentEvent)="openLinkBlocksModal($event)"
                                             [openBlockActionsMenu$]="openBlockActionsMenu$">
      </bromcom-curriculum-plan-block-actions>

      <bcm-icon class="icon"
                icon="far fa-compress-alt"
                (click)="compressBlock()">
      </bcm-icon>
    </div>
  </div>

  <div class="title"
       *ngIf="!editBlockName"
       (dblclick)="onEditBlockName()">
    <div class="linked-icon" *ngIf="selectedBlock?.linkedPairID" [matTooltip]="(linkedBlockName$ | async) || '-'"
         [matTooltipPosition]="'above'" (mouseenter)="getLinkedBlockName()">
      <bcm-icon class="icon"
                icon="far fa-link"></bcm-icon>
    </div>

    {{selectedBlock?.blockName}}

    <div class="cross-band-icon" *ngIf="selectedBlock && selectedBlock.bandIds.length > 1" [matTooltip]="bandNames"
         [matTooltipPosition]="'above'">X
    </div>
  </div>

  <div class="title" *ngIf="editBlockName">
    <bcm-tooltip [message]="editName.errors?.['custom']"
                 trigger="hover"
                 class="name"
                 open-delay="500">
      <bcm-icon class="icon error"
                [hidden]="!editName.errors?.['custom']"
                icon="fas fa-times-circle">
      </bcm-icon>
    </bcm-tooltip>
    <input type="text"
           [formControl]="editName"
           maxlength="20"
           class="input"
           bromcomNoLeadingSpace>
    <div class="icon-wrapper">
      <bcm-icon class="icon"
                icon="far fa-check"
                [class.disabled]="editName.errors?.['required']"
                (click)="saveName()">
      </bcm-icon>
    </div>
    <div class="icon-wrapper">
      <bcm-icon class="icon" icon="far fa-times" (click)="cancelName()"></bcm-icon>
    </div>
  </div>
</div>
