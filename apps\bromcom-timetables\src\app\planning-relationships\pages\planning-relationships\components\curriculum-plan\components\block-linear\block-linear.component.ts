import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { ICurriculumSessions } from '../../../../../../../_shared/models/ICurriculumSessions';
import { SnackbarService } from '@bromcom/ui';
import { TranslateService } from '@ngx-translate/core';
import { BaseBlockSimpleView } from '../../_shared/base-block-simple-view';
import { CurriculumPlanService } from "../../../../../../services/curriculum-plan.service";
import { BandService } from '../../../../../../services/band.service';
import { PlanningRelationshipsService } from '../../../../../../services/planning-relationships.service';
import { RelationshipsService } from '../../../../../../services/relationships.service';
import { InformationService } from '../../../../../../services/information.service';

@Component({
  selector: 'bromcom-block-linear',
  templateUrl: './block-linear.component.html',
  styleUrls: ['./block-linear.component.scss']
})
export class BlockLinearComponent extends BaseBlockSimpleView implements OnInit {
  @Output() addSubjectToLinearBlock = new EventEmitter();
  transformedSessions: ICurriculumSessions[][] = []

  classNames: string[] = [];
  lastVisibleIndex = 0;
  originalLastVisibleIndex = 0;

  constructor(
    protected override curriculumPlan: CurriculumPlanService,
    protected override curriculumPlanBlocks: CurriculumPlanBlocksService,
    protected override snackbar: SnackbarService,
    protected override translate: TranslateService,
    protected override band: BandService,
    protected override planningRelationships: PlanningRelationshipsService,
    public override relationshipsService: RelationshipsService,
    protected override informationService: InformationService
  ) {
    super(curriculumPlan, curriculumPlanBlocks, snackbar, translate, band, planningRelationships, relationshipsService, informationService)
  }

  override onMouseEnter() {
    this.isHovered = true;
    this.curriculumPlanBlocks.isAnyBlockHovered$.next(true);
    this.isSubjectDraggingActive && this.isHovered
      ? this.lastVisibleIndex = this.block.subjectToYearGroups.length + 1
      : this.lastVisibleIndex = this.originalLastVisibleIndex;
  }

  override onMouseLeave() {
    this.isHovered = false;
    this.curriculumPlanBlocks.isAnyBlockHovered$.next(false);
    if (this.isSubjectDraggingActive) {
      this.lastVisibleIndex = this.originalLastVisibleIndex
    }
  }

  formatSessions(): void {
    const groupedSessionsBySubject = this.block.subjectToYearGroups
      .map(subject => this.block.sessions.filter(session => session.subjectToYearGroupId === subject.id))

    const groupedSessions = groupedSessionsBySubject.map(groupedSessions => groupedSessions
      .reduce((groups: { [key: string]: ICurriculumSessions[] }, session: ICurriculumSessions) => {
        const className = session.className;
        if (groups[className]) {
          groups[className].push(session);
        } else {
          groups[className] = [session];
        }
        return groups;
      }, {}));

    this.transformedSessions = groupedSessions.map(sessions => Object.values(sessions).map(this.transformSessions));

    const droppableRoomIds = this.transformedSessions.map(sessionGroup => sessionGroup.map(session => 'room' + session.id.toString())).flat();
    const droppableStaffIds = this.transformedSessions.map(sessionGroup => sessionGroup.map(session => 'staff' + session.id.toString())).flat();
    this.curriculumPlanBlocks.droppableRoomPlaceIds$.next([...this.curriculumPlanBlocks.droppableRoomPlaceIds$.getValue(), ...droppableRoomIds]);
    this.curriculumPlanBlocks.droppableStaffPlaceIds$.next([...this.curriculumPlanBlocks.droppableStaffPlaceIds$.getValue(), ...droppableStaffIds]);
  }

  dropSubject(event: CdkDragDrop<any>): void {
    if (!event.isPointerOverContainer) {
      this.setVisibleIndex(this.originalLastVisibleIndex);
      return;
    }

    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      this.curriculumPlanBlocks.isShowLastSubjectBlockId$.next(this.block.id);
      this.addSubjectToLinearBlock.emit({
        ...event.item.data,
        currentIndex: this.block.subjectToYearGroups.length,
        listIndex: this.listIndex,
        blockTypeId: this.block.blockTypeId
      });
      this.setVisibleIndex(this.block.subjectToYearGroups.length + 1);
    }
  }

  dropStaff(event: CdkDragDrop<any>, subjectIndex: number): void {
    const session = this.transformedSessions[subjectIndex].find(session => session.id.toString() === event.container.id.replace('staff', ''))
    if (session) {
      this.handleDropStaff(event, session);
    }
  }

  dropRoom(event: CdkDragDrop<any>, subjectIndex: number) {
    const session = this.transformedSessions[subjectIndex].find(session => session.id.toString() === event.container.id.replace('room', ''))
    if (session) {
      this.handleDropRoom(event, session);
    }
  }

  override ngOnInit() {
    super.ngOnInit();

    this.setVisibleIndex(this.curriculumPlanBlocks.lastVisibleIndexForBlocks[this.block.id]);

    const isShowLastSubjectBlockId = this.curriculumPlanBlocks.isShowLastSubjectBlockId$.getValue();
    if (isShowLastSubjectBlockId === this.block.id) {
      this.curriculumPlanBlocks.isShowLastSubjectBlockId$.next(null);
    }

    this.classNames = Array.from(new Set(this.block.sessions.map(session => session.className)));
  }

  setVisibleIndex(value: number): void {
    if (value < 3 && this.block.subjectToYearGroups.length > value && this.block.subjectToYearGroups.length >= 3) {
      value = 3;
    }
    this.lastVisibleIndex = value;
    this.originalLastVisibleIndex = value;
    this.curriculumPlanBlocks.lastVisibleIndexForBlocks[this.block.id] = value;
  }

}
