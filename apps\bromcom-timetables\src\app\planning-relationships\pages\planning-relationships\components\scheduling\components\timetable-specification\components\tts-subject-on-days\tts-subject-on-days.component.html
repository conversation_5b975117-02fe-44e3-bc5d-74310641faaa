<div class="tts-subject-on-days-container">
  <div class="action-bar">
    <bromcom-input-field class="search-field"
                         [formControl]="searchControl"
                         [icon]="'fal fa-search'"
                         [placeholder]="'Search' | translate "
    ></bromcom-input-field>

    <div class="action-buttons">
      <bcm-button *ngIf="viewType === INCLUDED_TYPES.ACTIVE"
                  class="button" icon="far fa-minus-circle"
                  kind="ghost"
                  disabled="{{isRemoveBulkDisabled}}"
                  (click)="excludeIncludeBulk(true)">{{'Exclude Rules' | translate }}</bcm-button>

      <bcm-button *ngIf="viewType === INCLUDED_TYPES.EXCLUDED"
                  class="button" icon="far fa-check-circle"
                  kind="ghost"
                  disabled="{{isRemoveBulkDisabled}}"
                  (click)="excludeIncludeBulk(false)">{{'Activate Rules' | translate }}</bcm-button>

      <bcm-button-group class="button-group" type="radio" (bcm-click)="viewTypeChange($event)">
        <bcm-button kind="ghost" icon="far fa-check-circle"
                    checked="{{viewType === INCLUDED_TYPES.ACTIVE}}">{{'Active' | translate }}</bcm-button>
        <bcm-button kind="ghost" icon="far fa-minus-circle"
                    checked="{{viewType === INCLUDED_TYPES.EXCLUDED}}">{{'Excluded' | translate }}</bcm-button>
      </bcm-button-group>
    </div>
  </div>

  <div class="table-container">
    <ag-grid-angular style="width: 100%; height:100%;"
                     id="tts-ag-grid"
                     class="ag-theme-alpine"
                     domLayout="normal"
                     [gridOptions]="gridOptions"
                     (gridReady)="onGridReady($event)">
    </ag-grid-angular>
  </div>
</div>

<bromcom-general-modal #deleteSubjectOnDaysWarningModal
                       icon="far fa-exclamation-triangle"
                       type="warningAmber"
                       [dismiss]="false"
                       [header]="'Do you want to continue?' | translate"
                       [cancelText]="'Cancel' | translate"
                       [nextText]="'Confirm' | translate"
                       [description]="'The selected rule will be deleted.' | translate"
                       (nextEvent)="onDeleteRow()">
</bromcom-general-modal>

<bromcom-general-modal #excludeSubjectRelationshipWarningModal
                       icon="far fa-exclamation-triangle"
                       type="warningAmber"
                       [dismiss]="false"
                       [header]="'Do you want to continue?' | translate"
                       [cancelText]="'Cancel' | translate"
                       [nextText]="'Confirm' | translate"
                       [description]="'The selected rule(s) will be excluded.' | translate"
                       (nextEvent)="excludeIncludeRequest({ isExcluded: true, ids: excludeRowIds })">
</bromcom-general-modal>
