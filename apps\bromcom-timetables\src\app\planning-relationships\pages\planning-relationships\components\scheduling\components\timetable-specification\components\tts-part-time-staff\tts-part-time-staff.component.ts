import { Component, ElementRef, OnDestroy, OnInit, Input, ViewChild } from '@angular/core';
import {
  GeneralModalComponent
} from '../../../../../../../../../_shared/components/general-modal/general-modal.component';
import { TimetableSpecificationsService } from '../../../../../../../../services/timetable-specifications.service';
import { ITTSPartTimeStaffDaysPerWeek } from '../../../../../../../../../_shared/models/ITTSPartTimeStaffDaysPerWeek';
import { GridApi } from 'ag-grid-community/dist/lib/gridApi';
import { GridOptions, GridReadyEvent, ICellRendererParams } from 'ag-grid-community';
import { gridOptions } from './tts-part-time-staff-gridoptions';
import { Subject, switchMap, takeUntil } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@bromcom/ui';
import { FormControl } from '@angular/forms';
import { ISubject } from '../../../../../../../../../_shared/models/ISubject';
import { INCLUDED_TYPES } from '../../../../../../../../../_shared/enums/IncludedTypes';
import { RelationshipsService } from '../../../../../../../../services/relationships.service';
import { IStaff } from '../../../../../../../../../_shared/models/IStaff';
import { StaffService } from '../../../../../../../../../projects/services/staff.service';

@Component({
  selector: 'bromcom-tts-part-time-staff',
  templateUrl: './tts-part-time-staff.component.html',
  styleUrls: ['./tts-part-time-staff.component.scss'],
})
export class TtsPartTimeStaffComponent implements OnInit, OnDestroy {
   @ViewChild('ttsSubjectRelationshipsModal', { static: false }) ttsSubjectRelationshipsModal!: ElementRef;
    @ViewChild('deleteSubjectOnDaysWarningModal') deleteSubjectOnDaysWarningModal!: GeneralModalComponent;
    @ViewChild('excludeSubjectRelationshipWarningModal') excludeSubjectRelationshipWarningModal!: GeneralModalComponent;

    @Input() projectId!: number;
    @Input() timetableId!: number;

    staffOptions: Partial<IStaff>[] = [];
    searchControl = new FormControl(null);
    params!: GridReadyEvent;
    INCLUDED_TYPES = INCLUDED_TYPES;
    viewType: INCLUDED_TYPES = INCLUDED_TYPES.ACTIVE;
    gridApi!: GridApi;
    originalGridData: ITTSPartTimeStaffDaysPerWeek[] = [];
    gridData: ITTSPartTimeStaffDaysPerWeek[] = [];
    gridOptions!: GridOptions;
    excludeRowIds: number[] = [];
    isRemoveBulkDisabled = true;
    subjectsOptions: Partial<ISubject>[] = [];
    dayList: any[] = [];
    deleteRowId!: number;
    filteredStaffOptions: any[] = [];

    tableHeight: string = '45vh';
    readonly unsubscribe$: Subject<void> = new Subject();

    constructor(
      private timetableSpecificationsService: TimetableSpecificationsService,
      private staffService: StaffService,
      private snackbar: SnackbarService,
      protected relationshipsService: RelationshipsService,
      protected translate: TranslateService,
    ) {
    }

    ngOnInit(): void {
      this.staffService.getList(this.projectId)
        .pipe(takeUntil(this.unsubscribe$))
        .subscribe(staffs => {
          this.staffOptions = staffs.map(staff => ({
            id: staff.id,
            typeId: staff.typeId,
            name: staff.firstName + " " + staff.lastName,
            firstName: staff.firstName,
            lastName: staff.lastName,
            isExcluded: staff.isExcluded,
            fte: staff.fte
          })).filter((staff) => !staff.isExcluded && (staff.fte > 0 && staff.fte < 1))
          this.setRowData();
        });
      this.relationshipsService.subjectsData$
        .pipe(takeUntil(this.unsubscribe$))
        .subscribe(subjects => this.subjectsOptions = subjects.map(subject => ({
          id: subject.id,
          name: subject.name,
          text: subject.name,
          code: subject.code,
          departmentId: subject.departmentId,
          departmentName: subject.departmentName,
          color: '#' + subject.color
        })));

      this.timetableSpecificationsService.getPartTimeStaffList(this.timetableId)
        .subscribe(data => {
          this.originalGridData = JSON.parse(JSON.stringify(data))
          this.gridData = data;
          this.setRowData();
        })


      setTimeout(() => {
        this.setRowData();
      }, 50)

      this.searchControl.valueChanges
        .pipe(takeUntil(this.unsubscribe$))
        .subscribe(searchValue => {
          this.gridApi.setQuickFilter(searchValue ?? '');
        })

      this.gridOptions = gridOptions.call(this, {
        onGridReady: this.onGridReady.bind(this),
        onAddNewRow: this.onAddNewRow.bind(this),
        onAcceptNewRow: this.onAcceptNewRow.bind(this),
        onCancelAddingNewRow: this.onCancelAddingNewRow.bind(this),
        onEditRow: this.onEditRow.bind(this),
        onDeleteRow: this.onDeleteRowClicked.bind(this),
        onAcceptRow: this.onAcceptRow.bind(this),
        onCancelEditRow: this.onCancelEditRow.bind(this),
        onExcludeRow: this.onExcludeRow.bind(this),
        onIncludeRow: this.onIncludeRow.bind(this)
      });
    }

    setRowData(): void {
      this.viewType === INCLUDED_TYPES.ACTIVE
        ? setTimeout(() => {
          this.gridApi?.setRowData(this.gridData.filter(data => !data.isExcluded));
          this.gridApi?.redrawRows();
        }, 0)
        : setTimeout(() => {
          this.gridApi?.setRowData(this.gridData.filter(data => data.isExcluded));
          this.gridApi?.redrawRows();
        }, 0)
    }

    onExcludeRow(params: ICellRendererParams): void {
      this.excludeRowIds = [params.data.id];
      this.excludeSubjectRelationshipWarningModal.show();
    }

    onIncludeRow(params: ICellRendererParams): void {
      this.excludeIncludeRequest({ isExcluded: false, ids: [params.data.id] })
    }

    excludeIncludeBulk(isExcluded: boolean): void {
      this.gridApi.stopEditing();
      this.gridOptions.api?.setPinnedBottomRowData([{}]);
      const ids = this.gridApi.getSelectedRows().map(row => row.id);
      if (!isExcluded) {
        this.excludeIncludeRequest({ isExcluded, ids })
      } else {
        this.excludeRowIds = ids;
        this.excludeSubjectRelationshipWarningModal.show();
      }
    }

    excludeIncludeRequest(data: { isExcluded: boolean, ids: number[] }) {
      this.excludeSubjectRelationshipWarningModal?.modalComponentRef?.nativeElement.hide();
      this.timetableSpecificationsService.excludePartTimeStaffs(data)
        .pipe(switchMap(() => this.timetableSpecificationsService.getPartTimeStaffList(this.timetableId)))
        .subscribe(data => {
          this.originalGridData = JSON.parse(JSON.stringify(data));
          this.gridData = data;
          this.setRowData();
          this.snackbar.success(this.translate.instant('Successful operation'));
        })
    }

    onGridReady(params: GridReadyEvent): void {
      this.params = params;
      this.gridApi = params.api;
      this.gridApi.setDomLayout('normal');
    }

    onAddNewRow(params: ICellRendererParams): void {
      this.gridOptions.api?.setPinnedBottomRowData([
        {
          id: 'newRowId',
          staffId: null,
          daysPerWeek: null,
          isExcluded: false
        }
      ]);
      const rowColumns = this.params.columnApi.getColumns() ?? [];
      this.gridApi.startEditingCell({ rowIndex: 0, rowPinned: 'bottom', colKey: rowColumns[1]?.getColId() });
    }

    onAcceptNewRow(params: ICellRendererParams) {
      if(!this.validateDaysPerWeek(params, true)) return;

      this.timetableSpecificationsService.addPartTimeStaff(this.timetableId, {
        ...params.data,
        id: 0,
        daysPerWeek: Number(params.data.daysPerWeek)
      }).pipe(switchMap(() => this.timetableSpecificationsService.getPartTimeStaffList(this.timetableId)))
        .subscribe({
          next: data => {
            this.originalGridData = JSON.parse(JSON.stringify(data));
            this.gridData = data;
            this.setRowData();
            this.gridOptions.api?.setPinnedBottomRowData([{}]);
            this.snackbar.saved();
          },
          error: ({ error }) => {
            if (error?.validationErrors && error?.validationErrors[0]) {
              this.snackbar.error(error.validationErrors[0].errorMessage);
            }
            this.errorAcceptNewRow(params);
          }
        })
    }

    errorAcceptNewRow(params: ICellRendererParams){
      this.gridOptions.api?.setPinnedBottomRowData([
        {
          id: 'newRowId',
          staffId: null,
          daysPerWeek: null,
          isExcluded: false
        }
      ]);
      const rowColumns = this.params.columnApi.getColumns() ?? [];
      this.gridApi.startEditingCell({ rowIndex: 0, rowPinned: 'bottom', colKey: rowColumns[1]?.getColId() });
    }


    onCancelAddingNewRow(): void {
      this.gridOptions.api?.setPinnedBottomRowData([{}]);
    }

    onEditRow(params: ICellRendererParams): void {
      const rowIndex = params.node.rowIndex;
      const rowColumns = this.params.columnApi.getColumns() ?? []
      if (rowIndex || rowIndex === 0) {
        this.gridApi.startEditingCell({ rowIndex, colKey: rowColumns[0].getColId() })
      }
    }

    onAcceptRow(params: ICellRendererParams): void {
      if(!this.validateDaysPerWeek(params)) return;
      this.timetableSpecificationsService.editPartTimeStaff(params.data.id, params.data)
        .pipe(switchMap(() => this.timetableSpecificationsService.getPartTimeStaffList(this.timetableId)))
        .subscribe(
          {
            next: data => {
              this.originalGridData = JSON.parse(JSON.stringify(data));
              this.gridData = data;
              this.setRowData();
              this.snackbar.saved();
            },
            error: ({ error }) => {
              //this.gridOptions.api?.undoCellEditing();
              if (error?.validationErrors && error?.validationErrors[0]) {
                this.snackbar.error(error.validationErrors[0].errorMessage);
              }
              this.errorAcceptRow(params);
            }
          })
    }

    validateDaysPerWeek(params: ICellRendererParams, isNewRow: boolean = false){
      if(!Number.isInteger(Number(params.data.daysPerWeek)) || !(Number(params.data.daysPerWeek) > 0) || !(Number(params.data.daysPerWeek) < 1000)){
        this.snackbar.error("Value must be between 1 and 999");
        const rowIndex = params.node.rowIndex;
        if (isNewRow) {
          this.errorAcceptNewRow(params);
        }
        else{
          this.errorAcceptRow(params);
        }
        return false;
      }
      return true;
    }

    errorAcceptRow(params: ICellRendererParams){
      const rowIndex = params.node.rowIndex;
      const rowColumns = params.columnApi.getColumns() ?? [];
      if (rowIndex || rowIndex === 0) {
        this.gridApi.startEditingCell({ rowIndex, colKey: rowColumns[0]?.getColId() });
      }
    }

    validateSubjects(subjectIds: number[]): boolean {
      return subjectIds && subjectIds.length > 0;
    }


    onCancelEditRow(params: ICellRendererParams): void {
      this.timetableSpecificationsService.getPartTimeStaffList(this.timetableId)
        .subscribe(data => {
          this.originalGridData = JSON.parse(JSON.stringify(data));
          this.gridData = data;
          this.setRowData();
        })
    }

    onDeleteRowClicked(params: ICellRendererParams): void {
      this.deleteRowId = params.data.id;
      this.deleteSubjectOnDaysWarningModal.show();
    }

    onDeleteRow(): void {
      this.deleteSubjectOnDaysWarningModal.hide();
      this.timetableSpecificationsService.deletePartTimeStaff(this.deleteRowId)
        .pipe(switchMap(() => this.timetableSpecificationsService.getPartTimeStaffList(this.timetableId)))
        .subscribe(data => {
          this.originalGridData = JSON.parse(JSON.stringify(data));
          this.gridData = data;
          this.setRowData();
          this.snackbar.success(this.translate.instant('Successful operation'));
        })
    }

    viewTypeChange(event: Event): void {
      this.gridApi.stopEditing();
      this.gridData = JSON.parse(JSON.stringify(this.originalGridData));
      this.gridApi?.setRowData(this.gridData.filter(data => data.isExcluded == (this.viewType == INCLUDED_TYPES.EXCLUDED)));
      this.viewType = (event as CustomEvent).detail.innerText.toLowerCase() === INCLUDED_TYPES.ACTIVE
        ? INCLUDED_TYPES.ACTIVE
        : INCLUDED_TYPES.EXCLUDED;
      if (this.viewType == INCLUDED_TYPES.EXCLUDED) {
        this.gridOptions.api?.setPinnedBottomRowData([]);
      } else {
        this.gridOptions.api?.setPinnedBottomRowData([{}]);
      }
      this.setRowData();
    }

    groupSubjectsBySection(subjects: any[]): any {
      return subjects.reduce((acc, subject) => {
          const section = subject.section || 'Other';
          if (!acc[section]) {
              acc[section] = [];
          }
          acc[section].push(subject);
          return acc;
      }, {} as { [key: string]: any[] });
    }

    GetFilterdStaffList(staffId: string): any[] {
      const rowsToDisplay = this.gridApi?.getRenderedNodes()?.map(node => node?.data);
      if (rowsToDisplay && rowsToDisplay.length >= 0) {
        this.filteredStaffOptions = this.staffOptions;
        let mergedStaffIds: number[] = [];
        const selectedIds = rowsToDisplay.map(({ staffId }) => staffId);

        if (this.viewType === INCLUDED_TYPES.ACTIVE) {
          const excludedStaffs = this.gridData.filter(data => data.isExcluded).map(row => row.staffId);
          mergedStaffIds = selectedIds.concat(excludedStaffs);
        } else {
          const includedStaffs = this.gridData.filter(data => !data.isExcluded).map(row => row.staffId);
          mergedStaffIds = selectedIds.concat(includedStaffs);
        }

        this.filteredStaffOptions.forEach((obj) => {
          if (mergedStaffIds.includes(obj.id)) {
            obj.disabled = true;
          } else {
            obj.disabled = false;
          }
        })
        return this.filteredStaffOptions;
      }
      return this.staffOptions;
    }

    ngOnDestroy(): void {
    }

}
