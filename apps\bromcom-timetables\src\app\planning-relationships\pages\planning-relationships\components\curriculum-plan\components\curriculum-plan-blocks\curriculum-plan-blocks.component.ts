import { Compo<PERSON>, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormArray, FormControl, FormGroup } from "@angular/forms";
import { CurriculumPlanService } from "../../../../../../services/curriculum-plan.service";
import { BandService } from "../../../../../../services/band.service";
import { IBand } from "../../../../../../../_shared/models/IBand";
import { distinctUntilChanged, Subject, takeUntil } from "rxjs";
import { IYearGroup } from "../../../../../../../_shared/models/IYearGroup";
import { CurriculumPlanBlocksService } from "../../../../../../services/curriculum-plan-blocks";
import { ICurriculumPlanBlock } from "../../../../../../../_shared/models/ICurriculumPlanBlock";
import { ISubject } from "../../../../../../../_shared/models/ISubject";
import { PlanningRelationshipsService } from '../../../../../../services/planning-relationships.service';
import { ViewDetailsModalComponent } from '../view-details-modal/view-details-modal.component';
import { TranslateService } from '@ngx-translate/core';
import { MatMenuTrigger } from "@angular/material/menu";

@Component({
  selector: 'bromcom-curriculum-plan-blocks',
  templateUrl: './curriculum-plan-blocks.component.html',
  styleUrls: ['./curriculum-plan-blocks.component.scss']
})
export class CurriculumPlanBlocksComponent implements OnInit, OnDestroy {
  @ViewChild('viewDetailsModal') viewDetailsModal!: ViewDetailsModalComponent;
  @ViewChild(MatMenuTrigger) trigger: MatMenuTrigger | undefined;
  @Input() projectId!: number;
  @Input() yearGroups: IYearGroup[] = [];
  @Output() openEditClassCodesModalEvent: EventEmitter<any> = new EventEmitter();

  timetableId!: number;
  selectedBandsGroup = new FormGroup({
    bands: new FormArray<FormControl<boolean | null>>([])
  });
  blocks!: ICurriculumPlanBlock[];
  bands!: IBand[];
  selectedBandIds: number[] = [];
  selectedYearGroup!: number;
  selectedYearGroupObject: IYearGroup | undefined;
  subjects: ISubject[] | undefined;
  isBandFilterApplied = false;
  blocksFilterModalOpened = false;

  protected readonly unsubscribe$: Subject<void> = new Subject();

  @HostListener('click', ['$event'])
  divClickHandler(event: MouseEvent) {
    const targetId = (event.target as HTMLElement).id;
    if (targetId === 'blocksBody') {
      this.curriculumPlanBlocksService.selectedBlockId$.next(null);
    }
  }

  constructor(
    private planningRelationShipsService: PlanningRelationshipsService,
    private curriculumPlanService: CurriculumPlanService,
    private curriculumPlanBlocksService: CurriculumPlanBlocksService,
    private bandService: BandService,
    private translate: TranslateService) {
  }

  ngOnInit(): void {
    this.planningRelationShipsService.timetableId$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(timetableId => {
        if (timetableId) {
          this.timetableId = timetableId
        }
      });

    this.curriculumPlanService.selectedYearGroup$.pipe(
      distinctUntilChanged(),
      takeUntil(this.unsubscribe$))
      .subscribe(selectedYearGroup => {
        this.selectedYearGroup = selectedYearGroup as number;
        if (selectedYearGroup) {
          this.updateState(selectedYearGroup, this.curriculumPlanBlocksService.blocksByYearGroup$.getValue(), true);
          this.selectedYearGroupObject = this.yearGroups?.find(yearGroup => yearGroup.id === selectedYearGroup);
        }
      });

    this.bandService.fetchedBands$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(fetchedBands => {
        if (fetchedBands) {
          this.bands = fetchedBands.map(band => ({
            ...band,
            text: this.translate.instant('Band') + ' ' + band.bandName
          }));

          if (this.selectedYearGroupObject?.id) {
            this.updateState(this.selectedYearGroupObject?.id, this.curriculumPlanBlocksService.blocksByYearGroup$.getValue(), true);
          }
        }
      });

    this.updateSubjects();

    this.curriculumPlanBlocksService.stateChanged$
      .subscribe(() => {
        this.updateSubjects();
      });

    this.curriculumPlanBlocksService.blocksByYearGroup$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((blocksByYearGroup) => {
        const id = this.selectedYearGroupObject?.id;
        if (id) {
          this.updateState(id, blocksByYearGroup, false);
        }
      })

    this.curriculumPlanBlocksService.blockSideFilterStateChanged$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(() => {
        const id = this.selectedYearGroupObject?.id;
        if (id) {
          this.updateState(id, this.curriculumPlanBlocksService.blocksByYearGroup$.getValue(), true);
        }
      });

    this.curriculumPlanBlocksService.scrollToSelectedBlockOnSidePanel$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(() => {
        const selectedBlockId = this.curriculumPlanBlocksService.selectedBlockId$.getValue();
        if (selectedBlockId) {
          setTimeout(() => {
            const element = document.getElementById('block' + selectedBlockId);
            if (element) {
              element.scrollIntoView({ behavior: 'auto', block: 'start' });
            }
          }, 500)
        }
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  updateState(selectedYearGroup: number, blocksByYearGroup: ICurriculumPlanBlock[], setAllToTrue = true) {
    if (!this.bands) {
      return
    }

    if (!setAllToTrue) {
      this.isBandFilterApplied = this.selectedBandIds.length !== this.bands.length;
    } else {
      this.selectedBandIds = this.bands.map(band => band.id);
      this.isBandFilterApplied = false;
    }

    this.blocks = blocksByYearGroup.filter(item => {
      const intersection = new Set([...item.bandIds].filter(value => this.selectedBandIds.includes(value)));
      return [...intersection].length > 0;
    });
  }

  showBlocksFilterModal() {
    this.blocksFilterModalOpened = !this.blocksFilterModalOpened;
    this.trigger?.openMenu();
  }

  selectActiveBlock(event: number) {
    this.curriculumPlanBlocksService.selectedBlockId$.next(event);
    this.curriculumPlanBlocksService.scrollToSelectedBlock$.next();
    const currentBlock = this.blocks.find(block => block.id === event);
    if (currentBlock) {
      this.curriculumPlanService.currentSelectedBlock$.next(currentBlock);
      this.curriculumPlanBlocksService.expandedSelectedSubjectIndex$.next(0);
    }
  }

  filterIds(data: { selectedItemIds: number[] }): void {
    this.isBandFilterApplied = data.selectedItemIds.length !== this.bands.length;
    this.selectedBandIds = data.selectedItemIds;

    this.curriculumPlanBlocksService.selectedBlockId$.next(null);
    const filterData = this.curriculumPlanBlocksService.filterOptionsForBlocks$.getValue();
    this.curriculumPlanBlocksService
      .getBlocksByYearGroupFilter(this.timetableId, this.selectedYearGroup, filterData)
      .subscribe(response => {
        this.blocks = response.filter(item => {
          const intersection = new Set([...item.bandIds].filter(value => data.selectedItemIds.includes(value)));
          return [...intersection].length > 0;
        });
        this.closeFilterMenu();
      });
  }

  closeFilterMenu(): void {
    this.blocksFilterModalOpened = false;
    this.trigger?.closeMenu();
  }

  selectAll(): void {
    this.bands?.forEach((band, index) => {
      this.selectedBandsGroup.controls.bands.controls[index].setValue(true);
    })
  }

  clear(): void {
    this.bands?.forEach((band, index) => {
      this.selectedBandsGroup.controls.bands.controls[index].setValue(this.selectedBandIds.includes(band.id));
    })
  }

  openViewDetails(): void {
    this.viewDetailsModal.show();
  }

  openEditClassCodesModal(event: any) {
    this.openEditClassCodesModalEvent.emit(event);
  }

  updateSubjects() {
    this.curriculumPlanService
      .getSubjectsList(this.projectId)
      .subscribe(subjects => {
        this.subjects = subjects;
      });
  }
}
