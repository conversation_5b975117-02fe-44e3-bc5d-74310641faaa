<div class="filter-popup" id="filterItems" #filterPopup>
  <div class="filter-header" *ngIf="showHeader">
        <span class="filter-icon">
            <i class="far fa-filter"></i>
        </span>
  </div>
  <div class="search" *ngIf="showSearchBar">
    <bromcom-input-field [formControl]="searchControl"
                         [icon]="'fal fa-search'"
                         [iconPosition]="'prefix'"
                         [placeholder]="'Search' | translate"
    ></bromcom-input-field>
  </div>
  <div class="filter-options">
    <span (click)="selectAll()">{{ 'Select All' | translate }}</span>
    <span (click)="clearFilter()">{{ 'Clear Filter' | translate }}</span>
  </div>
  <div class="vertical" [formGroup]="filterGroup">
    <ng-container formArrayName="items">
      <ng-container *ngIf="appearance === 'checkbox'">
        <bromcom-checkbox *ngFor="let item of filteredDataItems; let i = index" class="data-item"
                          [text]="filteredDataItems[i].text"
                          [formControlName]="i" [color]="filteredDataItems[i].color"
                          [matTooltip]="filteredDataItems[i].text"
                          [matTooltipDisabled]="filteredDataItems[i].text.length < 19"
                          [matTooltipPosition]="'above'">
        </bromcom-checkbox>
      </ng-container>
      <ng-container *ngIf="appearance === 'switch'">
                <span class="switch-container">
                    <bromcom-switch *ngFor="let item of filteredDataItems; let i = index"
                                    class="data-item"
                                    [label]="filteredDataItems[i].text"
                                    [formControlName]="i"
                                    [color]="filteredDataItems[i].color"
                                    [matTooltip]="filteredDataItems[i].text"
                                    [matTooltipDisabled]="filteredDataItems[i].text.length < 19"
                                    [matTooltipPosition]="'above'"
                    ></bromcom-switch>
                </span>
      </ng-container>
    </ng-container>
  </div>
  <div class="filter-buttons" *ngIf="showActions">
    <bcm-button class="button" kind="ghost" (click)="onCancel($event)">
      {{'Cancel' | translate}}
    </bcm-button>
    <bcm-button class="button apply-btn" (click)="applyFilter($event)">
      {{'Apply' | translate}}
    </bcm-button>
  </div>
</div>
