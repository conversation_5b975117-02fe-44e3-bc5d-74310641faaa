<div class="block-container"
     [ngClass]="{'active-block': isActiveBlock}"
     (click)="selectActiveSideBlock()"
     (mouseenter)="onMouseEnter()"
     (mouseleave)="onMouseLeave()">
  <div class="header">
    <div class="block-type-short-name">
      <div class="circle">{{BLOCK_TYPE[block.blockTypeId]}}</div>
    </div>
    <div class="block-name">{{block.blockCode}}</div>

    <div class="menu-container">
      <div class="menu">
        <bcm-icon (click)="openBlockActionsMenu()" class="icon" icon="far fa-ellipsis-h"></bcm-icon>
        <bromcom-curriculum-plan-block-actions [block]="block"
                                               (transformClicked)="transformBlock($event)"
                                               (copyClicked)="copyBlock($event)"
                                               (deleteClicked)="deleteBlock(this.block.id)"
                                               (unScheduleClicked)="unScheduleSessions($event)"
                                               (spreadToBandsClicked)="spreadToBands($event)"
                                               (splitToBandsClicked)="splitToBands($event)"
                                               (openEditClassCodesModalEvent)="openEditClassCodesModal($event)"
                                               (openLinkBlocksModalComponentEvent)="openLinkBlocksModal($event)"
                                               [openBlockActionsMenu$]="openBlockActionsMenu$"></bromcom-curriculum-plan-block-actions>
      </div>
      <div class="full-size">
        <bcm-icon class="icon" icon="far fa-expand-alt" (click)="expandBlock()"></bcm-icon>
      </div>
    </div>
  </div>

  <div class="block-type-full-name"
       [ngStyle]="{'max-width': block.subjectToYearGroups.length === 1 ? '168px' : '100%'}">
    <div class="linked-icon" *ngIf="block.linkedPairID" [matTooltip]="(linkedBlockName$ | async) || '-'"
         [matTooltipPosition]="'above'" (mouseenter)="getLinkedBlockName()">
      <bcm-icon class="icon"
                icon="far fa-link"></bcm-icon>
    </div>

    <div class="name"
         [matTooltipDisabled]="block.blockName.length < 15"
         [matTooltip]="block.blockName"
         [matTooltipPosition]="'above'">
      {{block.blockName}}
    </div>

    <div class="cross-band-icon" *ngIf="block.bandIds.length > 1" [matTooltip]="bandNames"
         [matTooltipPosition]="'above'">X
    </div>
  </div>

  <div class="subject-code-color-header">
    <div *ngFor="let subject of block.subjectToYearGroups"
         [matTooltipDisabled]="subject.code && subject.code.length < 5"
         [matTooltip]="subject.code!"
         [matTooltipPosition]="'above'"
         [ngStyle]="{'background-color': '#' + subject?.color, 'color': '#' + subject?.textColor}"
         class="code-color-header">
      <span class="subject-code">{{subject.code ?? '--'}}</span>
    </div>
  </div>

  <div class="period-count">{{block.periodCount}}</div>

  <div class="linear-body-container">
    <div class="subject-info-container-side">
      <bcm-icon class="icon" icon="far fa-arrow-to-left"
                [class.disable]="lastVisibleIndex <= 3 || block.subjectToYearGroups.length <= 3"
                (click)="setVisibleIndex(originalLastVisibleIndex - 1)"></bcm-icon>
      <div *ngFor="let className of classNames; let i = index" class="session-info-container-side class-name"
           [class.remove-class]="classNames.length > 1"
           [matMenuTriggerFor]="classContextMenuRef"
           [matMenuTriggerData]="{data: {block, blockId: block.id, subjectToYearGroupId: block.sessions[0].subjectToYearGroupId, className, disableRemoveClass: classNames.length === 1}}">
        {{className}}
      </div>


    </div>

    <div *ngFor="let subject of block.subjectToYearGroups, index as subjectIndex">
      <div [ngClass]="{'display-none': subjectIndex < lastVisibleIndex - 3 || subjectIndex >= lastVisibleIndex}"
           class="subject-info-container">
        <div [matMenuTriggerFor]="subjectMenuRef"
             (menuOpened)="subjectMenuOpened({data: {block, blockId: block.id, subject, showExpandedViewButtons: false}})"
             [class.remove-subject]="subject.code && !isSubjectDraggingActive">
          <div class="subject-info-container-center"
               [class.dashed]="isHovered && isSubjectDraggingActive && !subject.subjectId"
               cdkDropList
               [ngStyle]="{'background-color': '#' + subject?.color, 'color': '#' + subject?.textColor}"
               [id]="subject.subjectId ? block.id.toString() : 'emptySubjectPlaceholder' + block.id"
               (cdkDropListDropped)="dropSubject($event)"
               [cdkDropListData]="block.subjectToYearGroups">
            <div class="subject-name">{{ subject.code ?? '--' }}</div>
            <div class="subject-periods">
              <div class="single-period">S: {{ subject.singlePeriodCount ?? '--' }}</div>
              <div class="double-period">D: {{ subject.doublePeriodCount ?? '--' }}</div>
            </div>
          </div>
        </div>
      </div>

      <div *ngFor="let session of transformedSessions[subjectIndex]" class="session-info-container">
        <div [ngClass]="{'display-none': subjectIndex < lastVisibleIndex - 3 || subjectIndex >= lastVisibleIndex}"
             class="session-info-container-center"
             [matMenuTriggerFor]="sessionMenuRef"
             (menuOpened)="sessionMenuOpened({block, blockId: block.id, blockMenuId: session.id, session})">

          <div class="staff-side"
               [class.dashed]="isHovered && isStaffDraggingActive &&
               ((!shiftKeyPressed && (block.sessions | hasEmptyStaffSessionsClass:session.subjectToYearGroupId:session.className)) || shiftKeyPressed) &&
               (block | getSubjectToSession:session.subjectToYearGroupId).subjectId &&
               (block | getSubjectToSession: session.subjectToYearGroupId).subjectId !== freeSubjectId"
               cdkDropList
               [id]="'staff' + session.id"
               (cdkDropListDropped)="dropStaff($event, subjectIndex)"
               [cdkDropListData]="session.staffIds"
               (mouseenter)="bulkInformation($event, session.className, session.subjectToYearGroupId, block.sessions, !this.shiftKeyPressed, isStaffDraggingActive)"
               (mouseleave)="staffDragLeaveInformation()">

            <bromcom-simple-view-staff-section [sessions]="block.sessions"
                                               [session]="session"></bromcom-simple-view-staff-section>
          </div>

          <div class="room-side"
               [class.dashed]="isHovered && isRoomDraggingActive &&
               (block.sessions | hasEmptyRoomSessionsClass:session.subjectToYearGroupId:session.className) &&
               (block | getSubjectToSession:session.subjectToYearGroupId).subjectId &&
               (block | getSubjectToSession: session.subjectToYearGroupId).subjectId !== freeSubjectId"
               cdkDropList
               [id]="'room' + session.id"
               (cdkDropListDropped)="dropRoom($event, subjectIndex)"
               [cdkDropListData]="session.roomId"
               (mouseenter)="roomDragEnterBulkInformation($event, session.className, session.subjectToYearGroupId, block.sessions)"
               (mouseleave)="roomDragLeaveInformation()">
            <div class="session-room-icon">
              <bcm-icon class="icon" icon="far fa-map-marker-alt"></bcm-icon>
            </div>

            <div class="session-room"
                 [matTooltip]="'Room: ' + ((session.allRoomCodes | joinRooms).length > 0 ? (session.allRoomCodes | joinRooms) : '--')"
                 [matTooltipPosition]="'above'">{{session.allRoomCodes.includes('--') ? '--' : session.allRoomCodes.length === 1 ? session.allRoomCodes[0] : '**' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--    ////////////////////////    -->
    <!--    ADD NEW  SUBJECT SECTION    -->
    <!--    ////////////////////////    -->

    <div *ngIf="!!block.subjectToYearGroups[0].subjectId"
         [ngClass]="{
         'set-visible': isHovered && isSubjectDraggingActive,
         'hide-add-new-block': (!isHovered || !isSubjectDraggingActive || !block.subjectToYearGroups[0].subjectId) && block.subjectToYearGroups.length < 3,
         'hide-add-new-block-three-subject': (!isHovered || !isSubjectDraggingActive) && block.subjectToYearGroups.length >= 3
         }">
      <bromcom-block-new-subject-placeholder [block]="block"
                                             [listIndex]="listIndex"
                                             [showSides]="false"
                                             [classNames]="classNames"
                                             [emptySubjectPlaceholderId]="'emptySubjectPlaceholder' + this.block.id"
                                             (addSubjectToBlock)="dropSubject($event)"></bromcom-block-new-subject-placeholder>
    </div>

    <div class="subject-info-container-side">
      <bcm-icon class="icon" icon="far fa-arrow-to-right"
                [class.disable]="lastVisibleIndex === block.subjectToYearGroups.length || block.subjectToYearGroups.length <= 3 || isHovered && isSubjectDraggingActive"
                (click)="setVisibleIndex(originalLastVisibleIndex + 1)"></bcm-icon>
      <div *ngFor="let className of classNames" class="session-info-container-side class-name"></div>
    </div>
  </div>

  <div class="bottom"></div>
</div>
