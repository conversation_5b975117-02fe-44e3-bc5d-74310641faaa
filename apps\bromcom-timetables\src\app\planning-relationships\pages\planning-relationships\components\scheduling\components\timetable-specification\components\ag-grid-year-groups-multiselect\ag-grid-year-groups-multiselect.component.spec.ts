import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AgGridYearGroupsMultiselectComponent } from './ag-grid-year-groups-multiselect.component';

describe('AgGridYearGroupsMultiselectDropdownComponent', () => {
  let component: AgGridYearGroupsMultiselectComponent<any>;
  let fixture: ComponentFixture<AgGridYearGroupsMultiselectComponent<any>>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AgGridYearGroupsMultiselectComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(AgGridYearGroupsMultiselectComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
