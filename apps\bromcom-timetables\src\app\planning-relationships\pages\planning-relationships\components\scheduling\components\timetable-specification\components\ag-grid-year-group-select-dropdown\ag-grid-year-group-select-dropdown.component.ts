import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, ViewChild } from '@angular/core';
import { ICellEditorParams } from 'ag-grid-community';
import { BehaviorSubject, Subject, takeUntil } from 'rxjs';
import { IListOption } from '@bromcom/core';
import { ICellEditorAngularComp } from 'ag-grid-angular';

export interface IAgGridYearGroupSelectDropdownParams<TListOptions> extends ICellEditorParams {
  values: BehaviorSubject<TListOptions[]>;
  valueChanged: (data: number | null) => void;
  placeholder: string;
  checkboxes: boolean;
  allowDisabling: boolean;
  isDisabled?: BehaviorSubject<boolean>;
}

@Component({
  selector: 'bromcom-ag-grid-year-group-select-dropdown',
  templateUrl: './ag-grid-year-group-select-dropdown.component.html',
  styleUrls: ['./ag-grid-year-group-select-dropdown.component.scss'],
})
export class AgGridYearGroupSelectDropdownComponent<TListOptions extends IListOption> implements ICellEditorAngularComp, AfterViewInit {
  @ViewChild('agGridYearGroupSelectDropdown') agGridYearGroupSelectDropdown: ElementRef = {} as ElementRef;
  params!: IAgGridYearGroupSelectDropdownParams<TListOptions>;
  gridApi!: any;
  data!: any[];
  value!: any;
  placeholder = '';
  isDisabled = false;
  template = `<div style="display: flex; align-items: center; justify-content: space-between; width: 100%">
              <span style="display: flex; align-items: center; justify-content: flex-start; width: 100%">
                <span class="{{class}}" style="width:16px; display: none; margin-right: 10px;">
                    <bcm-icon icon="{{icon}}" slot="suffix"></bcm-icon>
                </span>
                <span class="{{class}}">{{text}}</span>
              </span>
             </div>`;
  readonly unsubscribe$: Subject<void> = new Subject();
  
  constructor(private cdr: ChangeDetectorRef) {}

  agInit(params: any): void {
    this.params = params;
    this.gridApi = params.gridApi;

    this.data = params.values.map((value: { id: number, name: string }) => ({ ...value, text: value.name }));
    
    params.isDisabled?.pipe(takeUntil(this.unsubscribe$)).subscribe((value: boolean) => {
      if (params.allowDisabling) {
        this.isDisabled = value;
      } else {
        this.isDisabled = false;
      }
      if (this.isDisabled && params.allowDisabling) {
        this.agGridYearGroupSelectDropdown?.nativeElement?.set(null);
      } else if (!this.isDisabled && params.allowDisabling && params.value) {
        this.agGridYearGroupSelectDropdown?.nativeElement?.set(params.value);
      }
      this.cdr.detectChanges();
    });
    
    this.value = params.value;
    this.placeholder = params.placeholder;
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.agGridYearGroupSelectDropdown.nativeElement.set(this.value)
    }, 0)
  }

  getValue(): number[] {
    return this.value;
  }

  updateValue(): void {
    this.agGridYearGroupSelectDropdown.nativeElement.get().then((data: number) => {
      this.value = data;

      if (this.params.node.rowPinned === 'bottom') {
        this.gridApi.getPinnedBottomRow(0).setDataValue([this.params.colDef.field], data);
      } else {
        this.gridApi.getRowNode(this.params.data.id).setDataValue([this.params.colDef.field], data);
      }
      
      setTimeout(() => {
        this.params?.valueChanged && this.params?.valueChanged(data);
        this.agGridYearGroupSelectDropdown?.nativeElement.set(this.value?.toString() === 'Not selected' ? null : this.value)
      }, 50)
    })
  }
}
