import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { GridApi, ICellEditorParams } from 'ag-grid-community';
import { BehaviorSubject, Subject, takeUntil } from 'rxjs';
import { IListOption } from '@bromcom/core';
import { ICellEditorAngularComp } from 'ag-grid-angular';

export interface IAgGridYearGroupsMultiselectParams<TListOptions> extends ICellEditorParams {
  values: BehaviorSubject<TListOptions[]>;
  placeholder: string;
  checkboxes: boolean;
  gridApi: GridApi<any>;
  keyString: string;
  allowDisabling: boolean;
  isDisabled?: BehaviorSubject<boolean>;
}

@Component({
  selector: 'bromcom-ag-grid-year-groups-multiselect',
  templateUrl: './ag-grid-year-groups-multiselect.component.html',
  styleUrls: ['./ag-grid-year-groups-multiselect.component.scss'],
})
export class AgGridYearGroupsMultiselectComponent<TListOptions extends IListOption> implements ICellEditorAngularComp, OnDestroy, AfterViewInit {
  @ViewChild('agGridYearGroupMultiselect') agGridYearGroupMultiselectDropdown: ElementRef = {} as ElementRef;
  params!: any;
  gridApi!: any;
  data!: any[];
  keyString = 'blockIds';
  checkboxes = false;
  value: any[] = [];
  placeholder = '';
  isDisabled = false;
  readonly unsubscribe$: Subject<void> = new Subject();   
  
  constructor(private cdr: ChangeDetectorRef) {}

  agInit(params: IAgGridYearGroupsMultiselectParams<TListOptions>): void {
    this.params = params;
    this.gridApi = params.gridApi;
    this.keyString = params.keyString;

    this.checkboxes = params.checkboxes ?? false;
    params.values.pipe(takeUntil(this.unsubscribe$)).subscribe((value) => {
      this.setData(value);
    });
    
    params.isDisabled?.pipe(takeUntil(this.unsubscribe$)).subscribe((value: boolean) => {
      if (params.allowDisabling) {
        this.isDisabled = value;
      } else {
        this.isDisabled = false;
      }
      if (this.isDisabled && params.allowDisabling) {
        this.agGridYearGroupMultiselectDropdown?.nativeElement?.set(null);
      } else if (!this.isDisabled && params.allowDisabling && params.value) {
        this.agGridYearGroupMultiselectDropdown?.nativeElement?.set(params.value);
      }
      this.cdr.markForCheck();
    });
    
    this.value = params.value.length ? params.value : [];
    this.placeholder = params.placeholder;
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      if (this.value) {
        this.agGridYearGroupMultiselectDropdown.nativeElement.set(this.value)
      }
    }, 200);
  }
  
  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
  
  setData(value: TListOptions[]): void {
    this.data = value?.map((value) => {
      return {
        ...value,
        text: value.name
      }
    })
  }

  getValue(): number[] {
    return this.value;
  }

  updateValue(): void {
    this.agGridYearGroupMultiselectDropdown.nativeElement.get().then((data: number[]) => {
      if (data === undefined) return;
      this.params.data[`${this.keyString}`] = data;
      this.value = data ?? [];

      if (this.params.node.rowPinned === 'bottom') {
        this.gridApi.getPinnedBottomRow(0).setDataValue([this.params.colDef.field], data);
      } else {
        this.gridApi.getRowNode(this.params.data.id).setDataValue([this.params.colDef.field], data);
      }
      
      // Update cell value in Ag-Grid
      const newValue = this.value.join(', '); // Convert array to string if needed
      const colId = this.params.column.getId();
      this.gridApi.applyTransaction({ update: [{ id: this.params.node.id, [colId]: newValue, ...this.params.data }] });
      
      setTimeout(() => {
        this.params?.valueChanged && this.params?.valueChanged(data);
      }, 50)
    })
  }
}
