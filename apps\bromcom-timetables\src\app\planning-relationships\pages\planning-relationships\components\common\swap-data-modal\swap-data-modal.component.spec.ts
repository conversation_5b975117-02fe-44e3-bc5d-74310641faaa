import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SwapDataModalComponent } from './swap-data-modal.component';

describe('SwapDataModalComponent', () => {
  let component: SwapDataModalComponent;
  let fixture: ComponentFixture<SwapDataModalComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [SwapDataModalComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(SwapDataModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
