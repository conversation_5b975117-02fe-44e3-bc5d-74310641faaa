import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { ISubject } from '../../../../../../_shared/models/ISubject';
import { GridApi, GridOptions, GridReadyEvent } from 'ag-grid-community';
import { TranslateService } from '@ngx-translate/core';
import { staffGridOptions } from './swap-staff-modal-helper';
import { FormControl } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { IListOption, filterByValue } from '@bromcom/core';
import { FILTER_TYPES } from '../../../../../../_shared/enums/FilterTypes';
import { SwapDataService } from '../../../../../services/swap-data.service';
import { MatMenuTrigger } from '@angular/material/menu';
import { RelationshipsService } from '../../../../../services/relationships.service';
import { IDepartment } from '../../../../../../_shared/models/IDepartment';
import { SchedulingService } from '../../../../../services/scheduling.service';
import { IFilterRequest } from '../../../../../../_shared/models/IFilterRequest';
import { IStaff } from '../../../../../../_shared/models/IStaff';
import { SnackbarService } from '@bromcom/ui';
import { BaseModalComponent } from '../../../../../../_shared/components/BaseModalComponent';
import { ISwapStaffDataEntity } from '../../../../../../_shared/models/ISwapStaffDataEntity';
import { ISwapRoomDataEntity } from '../../../../../../_shared/models/ISwapRoomDataEntity';
import { roomGridOptions } from './swap-room-modal.helper';
import { IRooms } from '../../../../../../_shared/models/IRooms';

@Component({
  selector: 'bromcom-swap-data-modal',
  templateUrl: './swap-data-modal.component.html',
  styleUrls: ['./swap-data-modal.component.scss']
})
export class SwapDataModalComponent extends BaseModalComponent implements OnInit {
  @ViewChild('swapDataModal', { static: false }) swapDataModal!: ElementRef;
  @ViewChild('subjectsFilter', { static: false }) subjectsFilter!: ElementRef;
  @ViewChild('departmentsFilter', { static: false }) departmentsFilter!: ElementRef;
  @ViewChild('staffTypesFilter', { static: false }) staffTypesFilter!: ElementRef;
  @ViewChild(MatMenuTrigger) trigger: MatMenuTrigger | undefined;
  subjectOptions: Partial<ISubject>[] = [];
  departmentOptions: Partial<IDepartment>[] = [];
  staffTypeOptions: Partial<IListOption>[] = [];
  isSubjectFilterApplied = false;
  isDepartmentFilterApplied = false;
  isStaffFilterApplied = false;
  filterByList: { subjects: number[], departments: number[], staffTypes: number[] } = {
    subjects: [],
    departments: [],
    staffTypes: []
  };
  resetSubjectFilter = false;
  isSubjectFilterOpen = false;
  isDepartmentFilterOpen = false;
  isStaffTypeFilterOpen = false;
  resetDepartmentFilter = false;
  resetStaffTypeFilter = false;
  filterTypes = FILTER_TYPES;
  staffWarning = '';
  originalRoomWarning = this.translate.instant('You may wish to reconsider this swap because of the following issues. If you accept the swap any issues will be ignored.');
  originalStaffWarning = this.translate.instant('Please note: Where a room was not allocated and the teacher\'s preferred room is available for the swapped period, preferred room will be applied to the new period.');
  showWarning = false;
  filteredStaffList: ISwapStaffDataEntity[] = [];
  filteredRoomList: ISwapRoomDataEntity[] = [];

  @Input() set rowData(data: ISwapStaffDataEntity[] | ISwapRoomDataEntity[]) {
    this.originalRowData = data || [];
    this.setRowData('');
  }

  get rowData() {
    return this.originalRowData;
  }

  @Input() selectedData!: ISwapStaffDataEntity | ISwapRoomDataEntity;
  @Input() timetableId!: number;
  @Input() isSwapStaffView!: boolean;
  @Output() updateData = new EventEmitter<any>();
  gridApi!: GridApi;
  gridOptions!: GridOptions;
  originalRowData: ISwapStaffDataEntity[] | ISwapRoomDataEntity[] = [];
  selectedStaffData!: ISwapStaffDataEntity | null;
  selectedRoomData!: ISwapRoomDataEntity | null;
  searchControl: FormControl = new FormControl<string>('');
  swapMessage!: string;
  feedbackType!: string;
  feedbackData: string[] = [];
  showFeedbackView = false;
  swapModalHeader!: string;
  isSwapAllowed = false;
  isRoomAllocationAllowed = false;
  swapSelectionControl: FormControl = new FormControl<boolean>(false);
  defaultStaffType: number[] = [];
  freeSubjectId!: number | null;
  readonly unsubscribe$: Subject<void> = new Subject();

  constructor(
    protected translate: TranslateService,
    private relationshipsService: RelationshipsService,
    private schedulingService: SchedulingService,
    private swapDataService: SwapDataService,
    private snackbar: SnackbarService
  ) {
    super();
  }

  ngOnInit(): void {
    this.gridOptions = this.isSwapStaffView ? staffGridOptions.call(this) as GridOptions : roomGridOptions.call(this) as GridOptions;
    this.searchControl.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(searchValue => {
        this.setRowData(searchValue || '');
      });

    this.relationshipsService.freeSubjectId$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(id => {
        this.freeSubjectId = id;
      });

    this.relationshipsService.subjectsData$.pipe(takeUntil(this.unsubscribe$)).subscribe(
      (subjects) =>
        (this.subjectOptions = subjects.filter(s => s.id !== this.freeSubjectId).map((subject) => ({
          ...subject,
          text: `${subject.code.toUpperCase()} - ${subject.name}`,
          color: '#' + subject.color
        })))
    );

    this.schedulingService.fetchedDepartments$.pipe(takeUntil(this.unsubscribe$)).subscribe(
      (departments) =>
        (this.departmentOptions = departments ? departments.map((department) => ({
          ...department,
          text: department.name
        })) : [])
    );

    this.schedulingService.getStaffTypes().pipe(takeUntil(this.unsubscribe$)).subscribe(
      (staffTypes) => {
        (this.staffTypeOptions = staffTypes ? staffTypes.map((staffType) => ({
          ...staffType,
          text: staffType.description
        })) : [])
      }
    );
  }

  show(): void {
    this.gridOptions = this.isSwapStaffView ? staffGridOptions.call(this) as GridOptions : roomGridOptions.call(this) as GridOptions;
    this.swapModalHeader = this.isSwapStaffView ? this.translate.instant('Swap Staff') : this.translate.instant('Swap Room');
    this.isOpen = true;
    this.showFeedbackView = false;
    this.searchControl.setValue('');
    this.swapSelectionControl.setValue(false);
    this.isSubjectFilterApplied = this.isDepartmentFilterApplied = this.isStaffFilterApplied = false;
    setTimeout(() => {
      if (this.isSwapStaffView) {
        const teacherStaffTypeId = this.staffTypeOptions.find(staffType => staffType.description === 'Teacher')?.id
        this.defaultStaffType = teacherStaffTypeId ? [teacherStaffTypeId] : [];
        this.onApplyFilter(this.filterTypes.StaffTypes, { selectedItemIds: this.defaultStaffType, clearFilter: false })
      }
      this.setRowData(this.searchControl.getRawValue());
      this.swapDataModal.nativeElement.show();
    }, 100);
  }

  close(): void {
    this.isOpen = false;
    this.showFeedbackView = false;
    this.swapDataModal.nativeElement.hide();
  }

  onGridReady(params: GridReadyEvent): void {
    this.gridApi = params.api;
    this.gridApi.setDomLayout('normal');
  }

  setRowData(searchValue: string): void {
    let data: ISwapStaffDataEntity[] | ISwapRoomDataEntity[] = [];
    if (this.isSwapStaffView) {
      data = filterByValue<ISwapStaffDataEntity>(
        searchValue || '',
        (this.filteredStaffList.length ? this.filteredStaffList : this.originalRowData as ISwapStaffDataEntity[]),
        ['firstName', 'lastName', 'code', 'totalContactTime', 'assigned', 'remaining']);
    } else {
      data = filterByValue<ISwapRoomDataEntity>(
        searchValue || '',
        (this.filteredRoomList.length ? this.filteredRoomList : this.originalRowData as ISwapRoomDataEntity[]),
        ['name', 'code', 'type', 'site', 'contactTime', 'assigned', 'remaining']);
    }


    setTimeout(() => {
      this.gridApi?.setRowData(data);
    }, 0);
  }

  openSubjectFilter() {
    if (this.isDepartmentFilterApplied || (this.isStaffFilterApplied || this.filterByList.staffTypes?.length)) {
      this.resetSubjectFilter = true;
    }
    this.isDepartmentFilterOpen = this.isStaffTypeFilterOpen = false;
    this.isSubjectFilterOpen = !this.isSubjectFilterOpen;
  }

  openDepartmentFilter() {
    if (this.isSubjectFilterApplied || (this.isStaffFilterApplied || this.filterByList.staffTypes?.length)) {
      this.resetDepartmentFilter = true;
    }
    this.isSubjectFilterOpen = this.isStaffTypeFilterOpen = false;
    this.isDepartmentFilterOpen = !this.isDepartmentFilterOpen;
  }

  openStaffTypeFilter() {
    if (this.isSubjectFilterApplied || this.isDepartmentFilterApplied) {
      this.resetStaffTypeFilter = true;
    }
    this.isSubjectFilterOpen = this.isDepartmentFilterOpen = false;
    this.isStaffTypeFilterOpen = !this.isStaffTypeFilterOpen;
  }

  onSubjectFilterBlur() {
    if (this.isSubjectFilterOpen) {
      this.isSubjectFilterOpen = false;
    }
  }

  onDepartmentFilterBlur() {
    if (this.isDepartmentFilterOpen) {
      this.isDepartmentFilterOpen = false;
    }
  }

  onStaffTypeFilterBlur() {
    if (this.isStaffTypeFilterOpen) {
      this.isStaffTypeFilterOpen = false;
    }
  }

  onApplyFilter(filterType: string, data: { selectedItemIds: number[], clearFilter: boolean }) {
    if (filterType === FILTER_TYPES.Subjects) {
      this.filterByList.subjects = [...data.selectedItemIds];
      this.resetSubjectFilter = data.clearFilter; //If clear filter is opted, filter should reset to default selection
      //If clear filter is opted and subject filter is active reset the other filters to default selection
      this.resetDepartmentFilter = this.resetStaffTypeFilter = (data.clearFilter && this.isSubjectFilterApplied);
      if (this.filterByList.departments.length && !data.clearFilter) {
        this.filterByList.departments = [];
      } else if (this.filterByList.staffTypes.length && !data.clearFilter) {
        this.filterByList.staffTypes = [];
      }
    } else if (filterType === FILTER_TYPES.Departments) {
      this.filterByList.departments = [...data.selectedItemIds];
      this.resetDepartmentFilter = data.clearFilter;
      this.resetSubjectFilter = this.resetStaffTypeFilter = (data.clearFilter && this.isDepartmentFilterApplied);
      if (this.filterByList.subjects.length && !data.clearFilter) {
        this.filterByList.subjects = [];
      } else if (this.filterByList.staffTypes.length && !data.clearFilter) {
        this.filterByList.staffTypes = [];
      }
    } else if (filterType === FILTER_TYPES.StaffTypes) {
      this.filterByList.staffTypes = [...data.selectedItemIds];
      this.resetStaffTypeFilter = data.clearFilter;
      this.resetDepartmentFilter = this.resetSubjectFilter = (data.clearFilter && this.isStaffFilterApplied);
      if (this.filterByList.subjects.length && !data.clearFilter) {
        this.filterByList.subjects = [];
      } else if (this.filterByList.departments.length && !data.clearFilter) {
        this.filterByList.departments = [];
      }
    }

    const filterRequest: IFilterRequest = {
      subjectIds: this.filterByList.subjects,
      departmentIds: this.filterByList.departments,
      staffTypeIds: this.filterByList.staffTypes
    }

    if (this.isSwapStaffView) {
      this.swapDataService.getStaffFilteredList(this.timetableId, filterRequest).subscribe(filteredList => {
        this.clearFilter(data.clearFilter, filterType);
        this.trigger?.closeMenu();
        if (this.isSwapStaffView) {
          this.filteredStaffList = (this.originalRowData as ISwapStaffDataEntity[]).filter((item: any) =>
            filteredList.some((filteredItem: IStaff) => filteredItem.id === item.id)
          );
          this.gridApi.setRowData(this.filteredStaffList);
        }

      });
    } else {
      this.swapDataService.getRoomFilteredList(this.timetableId, filterRequest).subscribe(filteredList => {
        this.clearFilter(data.clearFilter, filterType);
        this.trigger?.closeMenu();
        this.filteredRoomList = (this.originalRowData as ISwapRoomDataEntity[]).filter((item: any) =>
          filteredList.some((filteredItem: IRooms) => filteredItem.id === item.id)
        );
        this.gridApi.setRowData(this.filteredRoomList);
      });
    }

  }

  clearFilter(clearFilter: boolean, filterType: string) {
    if (clearFilter) {
      this.isSubjectFilterApplied = (filterType === FILTER_TYPES.Subjects && this.isSubjectFilterApplied) ? false : this.isSubjectFilterApplied;
      this.isDepartmentFilterApplied = (filterType === FILTER_TYPES.Departments && this.isDepartmentFilterApplied) ? false : this.isDepartmentFilterApplied;
      this.isStaffFilterApplied = (filterType === FILTER_TYPES.StaffTypes && this.isStaffFilterApplied) ? false : this.isStaffFilterApplied;
    } else {
      this.isSubjectFilterApplied = filterType === FILTER_TYPES.Subjects;
      this.isDepartmentFilterApplied = filterType === FILTER_TYPES.Departments;
      this.isStaffFilterApplied = filterType === FILTER_TYPES.StaffTypes;
    }
  }

  onCancelFilter(filterType: string, e: boolean) {
    if (filterType === FILTER_TYPES.Subjects) {
      this.isSubjectFilterOpen = false;
    } else if (filterType === FILTER_TYPES.Departments) {
      this.isDepartmentFilterOpen = false;
    } else if (filterType === FILTER_TYPES.StaffTypes) {
      this.isStaffTypeFilterOpen = false;
    }
    this.trigger?.closeMenu();
  }

  getSwapStaffFeedback() {
    if (!this.selectedStaffData) {
      return;
    }
    const selectedData = this.selectedData as ISwapStaffDataEntity;
    this.showFeedbackView = true;
    this.swapModalHeader = this.translate.instant('Swap teacher ') + this.selectedData.code + this.translate.instant(' with ') + this.selectedStaffData.code;
    const swapAllowText = selectedData.firstName + ' ' + selectedData.lastName + ' ' + '(' + selectedData.code + this.translate.instant(')\'s timetable will be swapped with ')
      + this.selectedStaffData.firstName + ' ' + this.selectedStaffData.lastName + ' ' + '(' + this.selectedStaffData.code + this.translate.instant(')\'s timetable.');
    const swapNotAllowText = selectedData.firstName + ' ' + selectedData.lastName + ' ' + '(' + selectedData.code + this.translate.instant(')\'s timetable cannot be swapped with ')
      + this.selectedStaffData.firstName + ' ' + this.selectedStaffData.lastName + ' ' + '(' + this.selectedStaffData.code + this.translate.instant('). See the feedback below.');
    this.swapMessage = swapAllowText;
    const staffIds: number[] = [];
    staffIds.push(this.selectedStaffData.id, this.selectedData.id);
    this.swapDataService.getStaffFeedbackData(this.timetableId, staffIds).subscribe(feedback => {
      this.isSwapAllowed = feedback?.isSwapAllowed;
      this.isRoomAllocationAllowed = feedback?.isRoomAllocationAllowed ?? false;
      if (!this.isSwapAllowed) {
        this.swapMessage = swapNotAllowText;
      }
      this.isRoomAllocationAllowed ? this.swapSelectionControl.enable() : this.swapSelectionControl.disable();
      this.feedbackType = 'Teacher';
      this.feedbackData = feedback.feedbackMessages;
      if (this.feedbackData.length) {
        this.staffWarning = this.originalStaffWarning + ' ' + this.originalRoomWarning;
      } else {
        this.staffWarning = this.originalStaffWarning;
      }
      this.showWarning = true;
    });
  }

  swapStaff() {
    if (!this.isSwapAllowed || !this.selectedStaffData) {
      return;
    }
    const staffIds: number[] = [];
    staffIds.push(this.selectedStaffData.id, this.selectedData.id);
    this.swapDataService.swapStaff(this.timetableId, staffIds, this.swapSelectionControl.getRawValue()).subscribe({
      next: () => {
        this.updateData.emit();
        this.close();
        this.snackbar.success(this.translate.instant('Swapped successfully'));
      },
      error: ({ error }) => {
        if (error?.validationErrors && error?.validationErrors[0]) {
          this.snackbar.error(this.translate.instant('An error occurred, data has not been swapped!'));
        }
      }
    })
  }

  getSwapRoomFeedback() {
    if (!this.selectedRoomData) {
      return;
    }
    const selectedData = this.selectedData as ISwapRoomDataEntity;
    this.showFeedbackView = true;
    this.swapModalHeader = this.translate.instant('Swap Room ') + this.selectedData.code + this.translate.instant(' with Room ') + this.selectedRoomData.code;
    const swapAllowText = this.translate.instant('This will swap Room ') + selectedData.name + this.translate.instant('\'s timetable with Room ') + this.selectedRoomData.name + '\'s timetable.'
    const swapNotAllowText = this.translate.instant('Room ') + selectedData.name + this.translate.instant('\'s timetable cannot be swapped with Room ') + this.selectedRoomData.name + this.translate.instant('. See the feedback below.');
    this.swapMessage = swapAllowText;
    const roomIds: number[] = [];
    roomIds.push(this.selectedRoomData.id, this.selectedData.id);
    this.swapDataService.getRoomFeedbackData(this.timetableId, roomIds).subscribe(feedback => {
      this.isSwapAllowed = feedback?.isSwapAllowed;
      if (!this.isSwapAllowed) {
        this.swapMessage = swapNotAllowText;
      }
      this.feedbackType = 'Room';
      this.feedbackData = feedback.feedbackMessages;
      this.showWarning = !!this.feedbackData.length;
    });
  }

  swapRoom() {
    if (!this.isSwapAllowed || !this.selectedRoomData) {
      return;
    }
    const roomIds: number[] = [];
    roomIds.push(this.selectedRoomData.id, this.selectedData.id);
    this.swapDataService.swapRoom(this.timetableId, roomIds).subscribe({
      next: () => {
        this.updateData.emit();
        this.close();
        this.snackbar.success(this.translate.instant('Swapped successfully'));
      },
      error: ({ error }) => {
        if (error?.validationErrors && error?.validationErrors[0]) {
          this.snackbar.error(this.translate.instant('An error occurred, data has not been swapped!'));
        }
      }
    })
  }

  onCancel() {
    this.showFeedbackView = false;
    this.selectedStaffData = this.selectedRoomData = null;
    this.gridApi.deselectAll();
  }

  onBackClick() {
    this.showFeedbackView = false;
    this.selectedStaffData = this.selectedRoomData = null;
    this.swapModalHeader = this.isSwapStaffView ? this.translate.instant('Swap Staff') : this.translate.instant('Swap Room');
    this.gridOptions = this.isSwapStaffView ? staffGridOptions.call(this) as GridOptions : roomGridOptions.call(this) as GridOptions;
    this.setRowData('');
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
