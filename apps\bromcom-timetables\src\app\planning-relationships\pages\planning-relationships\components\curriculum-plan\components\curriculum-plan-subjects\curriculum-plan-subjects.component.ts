import {
  ChangeDetector<PERSON>ef,
  Component,
  ElementRef,
  EventEmitter,
  OnDestroy,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { distinctUntilChanged, takeUntil } from 'rxjs';
import { LoadingSpinnerService } from '@bromcom/ui';
import { RelationshipsService } from '../../../../../../services/relationships.service';
import { BaseCurriculumPlanSidePanel } from '../../_shared/base-curriculum-plan-side-panel';
import { ICurriculumPlanSubject } from '../../../../../../../_shared/models/ICurriculumPlanSubject';
import { CurriculumPlanService } from '../../../../../../services/curriculum-plan.service';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';

@Component({
  selector: 'bromcom-curriculum-plan-subjects',
  templateUrl: './curriculum-plan-subjects.component.html',
  styleUrls: ['./curriculum-plan-subjects.component.scss']
})
export class CurriculumPlanSubjectsComponent extends BaseCurriculumPlanSidePanel<ICurriculumPlanSubject> implements OnInit, OnDestroy {
  @ViewChild('editSubjectsModal') editSubjectsModal!: ElementRef;
  @Output() editSubjects: EventEmitter<boolean> = new EventEmitter();

  droppableBlockIds: string[] = [];

  constructor(
    protected override relationships: RelationshipsService,
    protected override curriculumPlan: CurriculumPlanService,
    private curriculumPlanBlocks: CurriculumPlanBlocksService,
    protected override loading: LoadingSpinnerService,
    protected override changeDetectorRef: ChangeDetectorRef
  ) {
    super(relationships, curriculumPlan, loading, changeDetectorRef);
  }

  override ngOnInit(): void {
    super.ngOnInit()

    this.filterGroup.addControl('code', new FormControl(false));
    this.filterGroup.addControl('name', new FormControl(false));
    this.filterGroup.addControl('shortName', new FormControl(false));
    this.filterGroup.addControl('departmentName', new FormControl(false));

    this.sorting = {
      code: 'default',
      name: 'asc',
      shortName: 'default',
      departmentName: 'default'
    }

    this.curriculumPlanBlocks.droppableSubjectIds$
      .pipe(
        takeUntil(this.unsubscribe$))
      .subscribe(ids => {
        this.droppableBlockIds = ids;
        this.changeDetectorRef.detectChanges();
      });

    this.relationships.subjectsData$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(subjects => {
        this.visibleData = [];

        const sorted = this.initSort(subjects, 'name');
        this.originalData = [...sorted];
        this.visibleData = [...sorted];
        this.changeDetectorRef.detectChanges();
      });
  }

  onEditSubjects(): void {
    this.editSubjects.emit(true);
  }
}
