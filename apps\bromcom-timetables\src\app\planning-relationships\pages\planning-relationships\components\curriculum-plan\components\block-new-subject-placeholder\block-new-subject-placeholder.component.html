<div class="subject-info-container">
  <div *ngIf="showSides" class="subject-info-container-side"></div>
  <div class="subject-info-container-center dashed"
       cdkDropList
       [id]="emptySubjectPlaceholderId"
       (cdkDropListDropped)="drop($event)"
       [cdkDropListData]="block.subjectToYearGroups">
    <div class="subject-name"></div>
    <div class="subject-periods">
      <div class="single-period">S: --</div>
      <div class="double-period">D: --</div>
    </div>
  </div>
  <div *ngIf="showSides" class="subject-info-container-side"></div>
</div>

<div class="session-info-container"
     *ngFor="let className of isComplexBlock ? (classNames | slice:1) : classNames, index as classNameIndex">
  <div *ngIf="showSides" class="session-info-container-side class-name">{{className}}</div>
  <div
    *ngIf="!isComplexBlock || !className.includes('emptySubjectPlaceholder-') || className.includes('emptySubjectPlaceholder-') && classNameIndex === 0 else emptySubject"
    class="session-info-container-center">
    <div class="staff-side">
      <div class="session-staff-icon">
        <bcm-icon class="icon" icon="far fa-users-class"></bcm-icon>
      </div>
      <div class="session-staff">--</div>
    </div>
    <div class="room-side">
      <div class="session-room-icon">
        <bcm-icon class="icon" icon="far fa-map-marker-alt"></bcm-icon>
      </div>
      <div class="session-room">--</div>
    </div>
  </div>

  <ng-template #emptySubject>
    <div class="subject-info-container-center dashed"
         cdkDropList
         [id]="className"
         (cdkDropListDropped)="drop($event)"
         [cdkDropListData]="block.subjectToYearGroups">
      <div class="subject-name"></div>
      <div class="subject-periods">
        <div class="single-period">S: --</div>
        <div class="double-period">D: --</div>
      </div>
    </div>
  </ng-template>
  <div *ngIf="showSides" class="session-info-container-side"></div>
</div>
