@import "apps/bromcom-timetables/src/assets/styles/variables";

.box {
  display: flex;
  justify-content: space-between;
  cursor: move;
  align-items: center;
  background: $color-blue-grey-100;
  border-radius: 4px;
  padding: 4px 12px;
  margin-bottom: 4px;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: $color-blue-grey-600;
  width: 100%;
  height: 32px;

  &.active {
    background-color: $color-blue-300;
  }

  img {
    margin-right: 6px;
  }

  .part-one {
    display: flex;
    align-items: center;
    width: 100%;

    .text {
      padding-right: 6px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 180px;
    }
  }

  .part-two {
    display: flex;
    align-items: center;
    height: 24px;

    img {
      margin-right: 0;
      cursor: pointer;
    }
  }

  bromcom-checkbox {
    height: 21px;
    width: 16px;
    margin-top: -5px;
    margin-right: 8px;
  }

  .input {
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    width: 40px;
    height: 24px;
    margin-right: 10px;
    border: 1px solid $color-blue-grey-300;
    border-radius: 4px;
  }

  .checkbox {
    display: flex;
    flex-direction: row;
    cursor: pointer;
    align-items: center;
    padding: 0;
    margin-right: 6px;
    width: 16px;
    height: 16px;
    background: $color-white-0;
    border: 1px solid $color-blue-grey-300;
    border-radius: 2px;
  }

  .circle {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    background: $color-blue-400;
    border: 1px solid $color-white-0;
    border-radius: 100px;
    margin-right: 6px;
  }
}

.first-menu-row {
  padding-right: 10px;
}

::ng-deep .linked {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 221px !important;
  height: 112px !important;
  background: $color-white-0;
  box-shadow: 0 2px 6px 1px rgba(17, 24, 38, 0.1);
  border-radius: 4px;
  padding: 8px;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
  0 8px 10px 1px rgba(0, 0, 0, 0.14),
  0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.example-box:last-child {
  border: none;
}

.cdk-drop-list-dragging .example-box:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.ng-invalid {
  border-color: $color-red-tertiary-500 !important;
}

::ng-deep .linked-block-action-modal {

  button {
    cursor: pointer;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .text {
      color: $color-blue-grey-600;
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }

    &:hover {
      background-color: $color-blue-grey-100;
    }
  }

  .disabled {
    pointer-events: none;
  }

}
