<div class="room-container"
     [ngClass]="{'error': room.assignedContactTime > room.contactTime}"
     cdkDrag
     [cdkDragData]="room"
     (cdkDragStarted)="roomDragStarted()"
     (cdkDragEnded)="roomDragEnded()">
  <div class="drag-icon">
    <img src="assets/images/drag-icon.png" alt="">
  </div>
  <div class="code" [matTooltipPosition]="'above'"
       [matTooltip]="room.code">{{room.code}}</div>

  <div *ngIf="room.teacher.length > 1"
       class="teacher"
       [matTooltipPosition]="'above'"
       [matTooltip]="room.teacher.join(', ')">
    <div>**</div>
  </div>

  <div *ngIf="room.teacher.length < 2" class="teacher truncated"
       [matTooltip]="room.teacher[0]"
       [matTooltipDisabled]="room.teacher.length === 1 && room.teacher[0].length < 5"
       [matTooltipPosition]="'above'">{{room.teacher}}</div>

  <div class="type" [matTooltipPosition]="'above'"
       [matTooltip]="room.type"
       [matTooltipShowDelay]="1000">
    {{room.type.slice(0, 3)}}
  </div>

  <div class="site"
       [matTooltipPosition]="'above'"
       [matTooltip]="room.site"
       [matTooltipShowDelay]="1000">
    {{room.site}}
  </div>

  <div type="line-rounded" class="room-total bcm-progress"
       [class.error]="room.assignedContactTime > room.contactTime">
    {{room.assignedContactTime}}/{{room.contactTime}}
    <div class="bcm-progress__line">
      <progress class="bcm-progress__line-progress medium line-rounded"
                value="{{room.assignedContactTime}}"
                max="{{room.contactTime}}"></progress>
    </div>
  </div>
  <!--  Need this to be able to overwrite bcm-progress component-->
  <bcm-progress [style.display]="'none'" type="line-rounded"></bcm-progress>

  <div class="actions">
    <!-- TODO implement these menu options in the future -->
    <img *ngIf="showActions" (click)="openRoomActions()" src="assets/images/vertical-three-points.png" alt="">
    <span *ngIf="showActions" [matMenuTriggerFor]="menu"></span>
  </div>

  <div *cdkDragPreview>
    <div class="code">{{room.code}}</div>
  </div>

  <div *cdkDragPlaceholder></div>
</div>

<mat-menu #menu="matMenu"
          class="block-action-modal">
  <div class="block-vertical">
    <button mat-menu-item [disabled]="true">{{'Substitute room' | translate}}</button>

    <button mat-menu-item (click)="onDisplayTimetableClick(room)">{{'Display this room\'s timetable' | translate}}</button>

    <button mat-menu-item (click)="onSwapRoomClick(room)">{{'Swap room' | translate}}</button>
  </div>
</mat-menu>
