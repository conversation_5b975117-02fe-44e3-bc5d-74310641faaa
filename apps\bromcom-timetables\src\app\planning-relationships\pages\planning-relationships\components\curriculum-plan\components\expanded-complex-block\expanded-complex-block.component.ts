import { Component, OnInit } from '@angular/core';
import { BaseBlockComponent } from "../../_shared/base-block-component";
import { distinctUntilChanged, takeUntil } from "rxjs";

@Component({
  selector: 'bromcom-expanded-complex-block',
  templateUrl: './expanded-complex-block.component.html',
  styleUrls: ['./expanded-complex-block.component.scss'],
})
export class ExpandedComplexBlockComponent extends BaseBlockComponent implements OnInit {

  override ngOnInit() {
    super.ngOnInit();
    this.curriculumPlanBlocksService.isSubjectDraggingActive$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(isSubjectDraggingActive => {
        this.isSubjectDraggingActive = isSubjectDraggingActive;
      })
  }

}
