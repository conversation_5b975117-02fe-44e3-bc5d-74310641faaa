@import '../../../../../../../../assets/styles/variables';

@mixin flex() {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin preview() {
  @include flex;

  min-width: 110px;
  width: 110px;
  height: 68px;
  border-radius: 4px;
  margin-right: 4px;
  font-size: 13px;
}

@mixin item-card() {
  @include flex;

  min-width: 110px;
  width: 110px;
  height: 68px;
  border-radius: 4px;
  margin-right: 4px;

  cursor: move;
}

@mixin item-card-small() {
  @include flex;

  min-width: 34px;
  width: 34px;
  height: 68px;
  border-radius: 4px;
  margin-right: 4px;

  cursor: move;
}

::ng-deep .class-name-modal.bcm-modal__show {
  z-index: 5 !important;
}

::ng-deep .class-name-modal {
  .modal-body {
    padding: 24px;
    font-size: 13px;

    .year-group-selector-row {
      display: flex;

      .list, .dropdown {
        width: 256px;
        margin-right: 16px;

        &.copy {
          width: 150px !important;
        }
      }
    }

    .special-settings-row {
      display: flex;

      .radio-group-container {
        display: flex;
        margin-right: 16px;

        .radio-group-title {
          margin: 4px 8px 0 0;
          font-weight: bold;
        }
      }
    }

    .draggable-row {
      display: flex;
      margin-bottom: 16px;
      box-sizing: border-box;

      .item-container-title {
        margin-bottom: 10px;
        font-weight: bold;
      }

      .drag-item-container {
        display: flex;
        align-items: center;
        overflow-x: auto;
        overflow-y: hidden;
        padding: 10px;

        height: 100px;
        border-radius: 4px;
        background-color: $color-blue-grey-100;

        .item-card {
          @include item-card;
        }

        .item-card-small {
          @include item-card-small;
        }
      }

      .optional-items {
        min-width: 134px;
        width: 134px;
        margin-right: 16px;
      }

      .optional-separators {
        min-width: 134px;
        width: 134px;
        margin-right: 16px;
      }

      .core-items {
        width: 100%;
        overflow: hidden;
      }
    }

    .droppable-row {
      margin-bottom: 16px;

      .drop-item-container-title {
        margin-bottom: 10px;
        font-weight: bold;
      }

      .drop-area {
        display: flex;
        align-items: center;
        overflow-x: auto;
        overflow-y: hidden;

        height: 100px;
        background-color: $color-blue-50;
        border: 2px dashed $color-blue-300;
        border-radius: 4px;
        padding: 10px;

        .item-card {
          @include item-card;
        }

        .item-card-small {
          @include item-card-small;
        }
      }
    }

    .check-row {
      display: flex;

      .check-row-example-container {
        display: flex;
        flex-direction: column;
        margin-right: 16px;
        min-width: 300px;

        .green .bcm-input__container {
          background-color: $color-emerald-100;
        }

        .red .bcm-input__container {
          background-color: $color-red-tertiary-100;
        }

        .check-row-example-title {
          margin-bottom: 10px;
          font-weight: bold;
        }

        .test-your-code-title {
          font-weight: 400;
          color: $color-blue-grey-500;

          &.green {
            color: $color-emerald-500;
          }

          &.red {
            color: $color-red-tertiary-500;
          }
        }
      }

      .check-button {
        margin-top: 32px;
      }
    }
  }

}

.cdk-drag {
  z-index: 10600 !important;
}

.cdk-drag-preview {
  cursor: move;
  font-size: 13px;
  border-radius: 4px;

  .item-card {
    @include item-card;
  }

  .item-card-small {
    @include item-card-small;
  }
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.year-group-selector-row bromcom-list {
  ::ng-deep .mdc-menu-surface .mat-mdc-select-panel .mat-mdc-option .mdc-list-item__primary-text {
    float: left;
    margin-top: 8px;
    max-width: 168px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
