﻿<div class="container">
  <bcm-icon *ngIf="!params.data.isAccepted" [ngClass]="{ 'disabled': params.data.issueDetails.length > 1 || params.data.issueDetails[0].issueEntityId == 0 }"
            class="icon" icon="far fa-check-circle" slot="suffix"
            (click)="onAcceptRow()"
  ></bcm-icon>
  <bcm-icon *ngIf="params.data.isAccepted" [ngClass]="{ 'disabled': params.data.issueDetails.length > 1 || params.data.issueDetails[0].issueEntityId == 0 }"
            class="icon" icon="far fa-times-circle" slot="suffix"
            (click)="onAcceptRow()"
  ></bcm-icon>
</div>
