import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { GridApi, GridOptions, GridReadyEvent } from 'ag-grid-community';
import { AutoScheduleService } from '../../../../../../services/auto-schedule.service';
import { gridOptions } from './auto-schedule-ncc-issues-gridoptions';

@Component({
  selector: 'bromcom-auto-schedule-ncc-issues',
  templateUrl: './auto-schedule-ncc-issues.component.html',
  styleUrls: ['./auto-schedule-ncc-issues.component.scss'],
})
export class AutoScheduleNccIssuesComponent implements OnInit {
  @Input() timetableId!: number;
  params!: GridReadyEvent;
  gridApi!: GridApi;
  gridOptions!: GridOptions;

  constructor(
    private autoScheduleService: AutoScheduleService,
    protected translate: TranslateService
  ) {
  }

  ngOnInit(): void {
    this.gridOptions = gridOptions.call(this);
    this.autoScheduleService.fetchNccIssues(this.timetableId, this.autoScheduleService.data.getValue()).subscribe(response => {
      this.gridApi.setRowData(response);
    });
  }

  onGridReady(params: GridReadyEvent): void {
    this.params = params;
    this.gridApi = params.api;
    this.gridApi.setDomLayout('normal');
  }
}
