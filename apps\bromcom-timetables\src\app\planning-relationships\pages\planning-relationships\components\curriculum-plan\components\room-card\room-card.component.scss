@import "apps/bromcom-timetables/src/assets/styles/variables";

.room-container {
  display: flex;
  justify-content: space-evenly;
  align-items: center;

  background: $color-blue-grey-50;
  border-radius: 8px;

  margin: 8px 0;
  padding: 4px;
  height: 40px;

  &.error {
    background-color: $color-red-tertiary-100;
  }

  .drag-icon {
    width: 10%;
    min-width: 10%;
    text-align: center;
  }

  .code {
    width: 15%;
    max-width: 55px;
    min-width: 55px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .teacher, .type {
    padding: 0 8px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    white-space: pre-wrap;
  }

  .tooltip {
    width: 100% !important;
  }

  .teacher {
    width: 22%;
    min-width: 22%;
    max-width: 22%;
  }

  .type {
    width: 15%;
    min-width: 15%;
    max-width: 15%;
  }

  .site {
    padding: 0 8px;
    width: 20%;
    min-width: 20%;
    max-width: 20%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  ::ng-deep .room-total {
    width: 14%;
    text-align: center;
    margin-bottom: 4px;
    position: relative;

    &.error {
      ::ng-deep &.bcm-progress {
        --bcm-progress-color: #EF4444 !important;
      }
    }
  }

  .actions {
    width: 5%;
    padding: 0 0 0 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
}

.cdk-drag-preview {
  width: 75px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  .code {
    width: auto !important;
    min-width: 55px !important;
    height: 30px;
    padding: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 24px;
    border: 1px solid $color-blue-grey-300;
    background-color: $color-white-0;
    white-space: nowrap;
    flex-shrink: 0; 
  }
}

.truncated {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap !important;
  display: block !important;
}
