<div class="container">
  <!--  EDIT ICON-->
  <bcm-icon *ngIf="!isNewRow && !isEdit"
            class="icon" icon="far fa-pen" slot="suffix"
            (click)="onEditRow()"
  ></bcm-icon>

  <!--ACCEPT EDIT-->
  <bcm-icon *ngIf="!isNewRow && isEdit"
            class="icon" icon="far fa-check" slot="suffix"
            [class.disable]="disable"
            (click)="onAcceptRow()"
  ></bcm-icon>

  <!--CANCEL EDIT-->
  <bcm-icon *ngIf="!isNewRow && isEdit"
            class="icon" icon="far fa-times" slot="suffix"
            (click)="onCancelEditRow()"
  ></bcm-icon>

  <!--  EXCLUDE ROW-->
  <bcm-icon *ngIf="!isNewRow && !isEdit && !data.isExcluded"
            class="icon" icon="far fa-minus-circle" slot="suffix"
            (click)="onExcludeRow()"
  ></bcm-icon>

  <!--  INCLUDE ROW-->
  <bcm-icon *ngIf="!isNewRow && !isEdit && data.isExcluded"
            class="icon" icon="far fa-check-circle" slot="suffix"
            (click)="onIncludeRow()"
  ></bcm-icon>

  <!--  DELETE ROW-->
  <bcm-icon *ngIf="!isNewRow && !isEdit"
            class="icon" icon="far fa-trash-alt" slot="suffix"
            (click)="onDeleteRow()"
  ></bcm-icon>


  <!--  ACCEPT NEW ROW-->
  <bcm-icon *ngIf="isNewRow" class="icon" icon="far fa-check" slot="suffix"
            [class.disable]="disable"
            (click)="onAcceptNewRow()"
  ></bcm-icon>

  <!--  CANCEL ADD NEW ROW-->
  <bcm-icon *ngIf="isNewRow" class="icon" icon="far fa-times" slot="suffix"
            (click)="onCancelAddingNewRow()"
  ></bcm-icon>
</div>
