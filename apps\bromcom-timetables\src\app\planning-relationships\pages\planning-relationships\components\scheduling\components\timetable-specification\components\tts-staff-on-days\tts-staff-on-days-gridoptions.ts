import { IListOption, NOOP, transformToAGGridConfig } from '@bromcom/core';
import {
  AgGridNoDataComponent
} from '@bromcom/ui';
import {
  CheckboxSelectionCallbackParams, ICellEditorParams, ICellRendererParams,
  IsFullWidthRowParams
} from 'ag-grid-community';
import {
  AddNewRowComponent
} from '../../../../../../../../../projects/pages/new-project-wizard/components/add-new-row/add-new-row.component';
import { TtsGridActionsComponent } from '../tts-grid-actions/tts-grid-actions.component';
import { AgGridPeriodDropdownComponent } from '../ag-grid-period-dropdown/ag-grid-period-dropdown.component';
import { PeriodsCellRenderer } from '../../cell-renderers/periods-cell-renderer';
import { TtsStaffOnDaysComponent } from './tts-staff-on-days.component';
import { AgGridSelectDropdownComponent } from '../ag-grid-select-dropdown/ag-grid-select-dropdown.component';

export function gridOptions(this: TtsStaffOnDaysComponent, config: any) {
  const {
    onAddNewRow = NOOP,
    onAcceptNewRow = NOOP,
    onCancelAddingNewRow = NOOP,
    onEditRow = NOOP,
    onDeleteRow = NOOP,
    onAcceptRow = NOOP,
    onCancelEditRow = NOOP,
    onExcludeRow = NOOP,
    onIncludeRow = NOOP
  } = config

  return transformToAGGridConfig({
    getRowId: params => params.data.id,
    animateRows: false,
    suppressRowClickSelection: true,
    suppressClickEdit: true,
    suppressCellFocus: true,
    domLayout: 'autoHeight',
    editType: 'fullRow',
    rowSelection: 'multiple',
    noRowsOverlayComponent: AgGridNoDataComponent,
    noRowsOverlayComponentParams: {
      style: 'display: flex; justify-content: center; align-items: center; height: 100%; width: 100%; min-height: 162px; padding: 48px 0 0px 0',
      noRowsMessageFunc: () => this.translate.instant(`No data available! No Staff on days were found.`)
    },
    pinnedBottomRowData: [{}],
    fullWidthCellRenderer: AddNewRowComponent,
    fullWidthCellRendererParams: {
      onAddNewRow,
      label: this.translate.instant('Add New Row')
    },
    tooltipShowDelay: 500,
    isFullWidthRow: (params: IsFullWidthRowParams) => {
      return !params.rowNode.data.id;
    },
    onSelectionChanged: () => {
      this.isRemoveBulkDisabled = !this.gridApi.getSelectedRows().length;
    },
    rowHeight: 56,
    columnDefs: [
      {
        minWidth: 48,
        width: 48,
        headerCheckboxSelection: true,
        checkboxSelection: (params: CheckboxSelectionCallbackParams<IListOption>) => {
          return !!params.data;
        }
      },
      {
        field: 'staffId',
        headerName: this.translate.instant('Staff'),
        minWidth: 160,
        flex: 1,
        editable: true,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: params => params.data.staffId ? this.staffOptions.find(staff => staff.id === params.data.staffId)?.firstName + ' ' + this.staffOptions.find(staff => staff.id === params.data.staffId)?.lastName : null,
        valueGetter: params => {
          return this.staffOptions.find(staff => staff.id === params.data.staffId)?.firstName + ' ' + this.staffOptions.find(staff => staff.id === params.data.staffId)?.lastName 
        },
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            staff: this.staffOptions.find(staff => staff.id === params.data.staffId)
          }
        },
        cellEditor: AgGridSelectDropdownComponent,
        cellEditorParams: (params: ICellEditorParams) => {
          return {
            placeholder: this.translate.instant('Staff'),
            checkboxes: false,
            values: this.GetFilterdStaffList(params.data.staffId),
            value: params.data.staffId,
            gridApi: this.gridApi,
            searchable: true,
            params
          }
        },
        cellStyle: {
          fontWeight: '400',
          display: 'block',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }
      },
      {
        field: 'undesirablePeriodIds',
        headerName: this.translate.instant('Undesirable'),
        minWidth: 178,
        flex: 1.5,
        editable: true,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: params => this.dayList.filter(period => params.data.undesirablePeriodIds?.includes(period.id)).map(period => period.text).join(', '),

        valueGetter: params => {
          return this.dayList.filter(period => params.data.undesirablePeriodIds?.includes(period.id)).map(period => period.text);
        },
        cellRenderer: PeriodsCellRenderer,
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            periods: this.dayList.filter(period => params.data.undesirablePeriodIds?.includes(period.id)).map(period => period.text),
            color: 'gray'
          }
        },
        cellEditorSelector: params => {
          return {
            component: AgGridPeriodDropdownComponent,
            popup: true,
            popupPosition: 'under',
            params: {
              values: this.dayList,
              value: params.data.undesirablePeriodIds,
              gridApi: this.gridApi,
              params,
              color: 'gray',
              placeholder: this.translate.instant('Undesirable')
            }
          }
        }
      },
      {
        field: 'preferredPeriodIds',
        headerName: this.translate.instant('Preferred'),
        minWidth: 178,
        flex: 1.5,
        editable: true,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: params => this.dayList.filter(period => params.data.preferredPeriodIds?.includes(period.id)).map(period => period.text).join(', '),
        valueGetter: params => {
          return this.dayList.filter(period => params.data.preferredPeriodIds?.includes(period.id)).map(period => period.text);
        },
        cellRenderer: PeriodsCellRenderer,
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            periods: this.dayList.filter(period => params.data.preferredPeriodIds?.includes(period.id)).map(period => period.text),
            color: 'green'
          }
        },
        cellEditorSelector: params => {
          return {
            component: AgGridPeriodDropdownComponent,
            popup: true,
            popupPosition: 'under',
            params: {
              values: this.dayList,
              value: params.data.preferredPeriodIds,
              gridApi: this.gridApi,
              params,
              color: 'green',
              placeholder: this.translate.instant('Preferred')
            }
          }
        }
      },
      {
        field: 'actions',
        headerName: this.translate.instant('Actions'),
        minWidth: 128,
        filter: false,
        sortable: false,
        menuTabs: [],
        flex: 0.8,
        headerClass: 'text-center',
        cellStyle: { display: 'flex', justifyContent: 'center' },
        cellRenderer: TtsGridActionsComponent,
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            gridId: 'tts-ag-grid',
            type: 'staffOnDays',
            onAcceptNewRow,
            onCancelAddingNewRow,
            onEditRow,
            onDeleteRow,
            onAcceptRow,
            onCancelEditRow,
            onExcludeRow,
            onIncludeRow
          }
        },
        editable: false
      }
    ]
  })
}

