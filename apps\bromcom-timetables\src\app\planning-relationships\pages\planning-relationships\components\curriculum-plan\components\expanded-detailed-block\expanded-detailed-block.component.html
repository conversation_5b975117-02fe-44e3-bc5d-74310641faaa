<div class="detailed-container"
     (mouseenter)="onMouseEnter()"
     (mouseleave)="onMouseLeave()"
     #detailedContainer>
  <div class="block">
    <div class="title"
         [class.dashed]="isHovered && isSubjectDraggingActive && !selectedBlock?.subjectToYearGroups?.[0]?.subjectId && selectedBlock?.blockTypeId === BLOCK_TYPE.Simple"
         cdkDropList
         [matMenuTriggerFor]="subjectMenuRef"
         (menuOpened)="subjectMenuOpened({data: {block: selectedBlock, blockId: selectedBlock!.id, subject: selectedBlock!.subjectToYearGroups[subjectIndex], expandedSelectedSubjectIndex: subjectIndex, showExpandedViewButtons: true}})"
         [id]="!selectedBlock?.subjectToYearGroups?.[subjectIndex]?.subjectId
         ? 'expandedEmptySubjectPlaceholder' + selectedBlock?.id
         : expandedSubjectString + selectedBlock?.subjectToYearGroups?.[subjectIndex]?.subjectId"
         (cdkDropListDropped)="dropSubject($event)"
         [ngClass]="{
          'white-color': isColorDarkHelper(selectedBlock?.subjectToYearGroups?.[subjectIndex]?.subjectId | getSubjectColor: subjects ),
          'clickable': selectedBlock?.blockTypeId !== blockTypes.Simple && selectedBlock?.subjectToYearGroups?.[subjectIndex]?.subjectId
        }"
         [ngStyle]="{'background-color' : selectedBlock?.subjectToYearGroups?.[subjectIndex]?.subjectId | getSubjectColor: subjects}">
      <div *ngIf="selectedBlock?.subjectToYearGroups?.[subjectIndex]">
        <span
          *ngIf="isHovered && isSubjectDraggingActive && !selectedBlock?.subjectToYearGroups?.[0]?.subjectId && selectedBlock?.blockTypeId === BLOCK_TYPE.Simple">{{'Drag here' | translate}}</span>
        {{selectedBlock?.subjectToYearGroups?.[subjectIndex]?.subjectId | getSubjectCode: subjects}}
      </div>
    </div>

    <table>
      <thead>
      <tr>
        <th class="action border left-action"
            (click)="leftClick($event)"
            [class.disable]="isLeftStepperDisabled()">
          <bcm-icon class="icon"
                    icon="far fa-arrow-to-left">
          </bcm-icon>
        </th>

        <th class="border period period-cell"
            *ngFor="let item of (selectedBlock?.subjectToYearGroups?.[subjectIndex]?.id | getSessions:selectedBlock?.sessions)[0] | slice:firstIndex:lastIndex; let i = index;"
            [class.selected]="selectedPeriodIndexes.includes(firstIndex + i)"
            (contextmenu)="addToSelectedPeriods($event, firstIndex + i)"
            [matMenuTriggerFor]="periodMenuRef"
            (menuOpened)="periodMenuOpened({block: selectedBlock, subjectIndex, selectedPeriodIndexes, index: i +1, blockId: selectedBlock!.id, sessions: selectedBlock?.sessions, onlySessionRelated: true, showExpandedViewButtons: true, isCombineSessionsEnabled: isSessionsNextToEachOther, selectedSessionIndexes, selectedSessionIndexes$, selectedSessionIds, selectedSessionIds$, firstIndex})"
            [matMenuTriggerData]="{data: {disableDeletion: (selectedBlock?.subjectToYearGroups?.[subjectIndex]?.id | getSessions:selectedBlock?.sessions)[0].length === 1 || selectedPeriodIndexes.length === (selectedBlock?.subjectToYearGroups?.[subjectIndex]?.id | getSessions:selectedBlock?.sessions)[0].length, blockMenuId: firstIndex + i, selectedPeriodIndexes: (selectedPeriodIndexes.length > 0 ? selectedPeriodIndexes : [firstIndex + i]), selectedPeriodIndexes$, blockId: selectedBlock!.id, subjectToYearGroupId: selectedBlock!.subjectToYearGroups[subjectIndex].id,currentIndex:  firstIndex + i, isLinear: selectedBlock?.blockTypeId === BLOCK_TYPE.Linear, lastIndex}}">
          <span>P{{firstIndex + i + 1}}</span><bcm-chip *ngIf="(selectedBlock?.subjectToYearGroups?.[subjectIndex]?.id | getSessions:selectedBlock?.sessions)[0][firstIndex + i].linkedBlockPeriod" class="chip link-chip" color="blue"><bcm-icon class="icon" icon="fa-solid fa-link" style="font-size: 8px"></bcm-icon>{{ (selectedBlock?.subjectToYearGroups?.[subjectIndex]?.id | getSessions:selectedBlock?.sessions)[0][firstIndex + i].linkedBlockPeriod?.linkIndex }}</bcm-chip>
        </th>

        <th class="action border"
            (click)="rightClick()"
            [class.disable]="(selectedBlock?.subjectToYearGroups?.[subjectIndex]?.id | getSessions:selectedBlock?.sessions)[0] && lastIndex >= (selectedBlock?.subjectToYearGroups?.[subjectIndex]?.id | getSessions:selectedBlock?.sessions)[0].length">
          <bcm-icon class="icon"
                    icon="far fa-arrow-to-right">
          </bcm-icon>
        </th>
      </tr>
      </thead>

      <tbody>
      <ng-container
        *ngFor="let row of (selectedBlock?.subjectToYearGroups?.[subjectIndex]?.id | getSessions:selectedBlock?.sessions); let rowIndex = index;">
        <tr>
          <td class="class-name border"
              [class.dashed]="isHovered &&
              (isStaffDraggingActive &&
                ((!shiftKeyPressed && (selectedBlock!.sessions | hasEmptyStaffSessionsClass:row![0]!.subjectToYearGroupId:row![0].className)) || shiftKeyPressed)
              || (isRoomDraggingActive &&
                  (selectedBlock!.sessions | hasEmptyRoomSessionsClass:row![0]!.subjectToYearGroupId:row![0]!.className))) &&
              (selectedBlock! | getSubjectToSession: row[0].subjectToYearGroupId).subjectId &&
              (selectedBlock! | getSubjectToSession: row[0].subjectToYearGroupId).subjectId !== freeSubjectId"
              [class.selected]="selectedClassIndexes.includes(subjectIndex * 10 + rowIndex + 1)"
              cdkDropList
              [id]="expandedString + row[0].subjectToYearGroupId + '-' + row[0].className"
              (cdkDropListDropped)="dropAll($event, row[0].className)"
              (mouseenter)="bulkInformation($event, row[0].className, row[0].subjectToYearGroupId, selectedBlock!.sessions, !this.shiftKeyPressed, isStaffDraggingActive)"
              (mouseleave)="staffDragLeaveInformation()" (contextmenu)="addToSelectedClasses($event, subjectIndex * 10 + rowIndex + 1, row[0].className, row[0].subjectToYearGroupId, subjectIndex)"
              [matMenuTriggerFor]="classMenuRef"
              [matMenuTriggerData]="{data: {currentIndex:  subjectIndex * 10 + rowIndex + 1}}"
              (menuOpened)="classMenuOpened({block: selectedBlock, subjectIndex, selectedClassIndexes, classData, currentIndex: subjectIndex * 10 + rowIndex + 1, blockId: selectedBlock!.id, sessions: selectedBlock?.sessions, isSimpleBlock: selectedBlock?.blockTypeId === BLOCK_TYPE.Simple, isLinear: selectedBlock?.blockTypeId === BLOCK_TYPE.Linear})">
            {{ row[0].className }}
          </td>

          <td *ngFor="let session of (row | slice:firstIndex:lastIndex); let i = index;"
              class="session-td"
              [id]="session.id.toString()"
              [class.selected]="selectedSessionIds.includes(session.id)"
              [class.conflict]="staffConflictedSessionIds.includes(session.id) || roomConflictedSessionIds.includes(session.id)"
              [class.missing]="missingStaffRoomSessionId === session.id"
              [ngClass]="{
              'left-double': session.joinedSessions[0] < session.joinedSessions[1] && session.joinedSessions.length > 1,
               'right-double': session.joinedSessions[0] > session.joinedSessions[1] && session.joinedSessions.length > 1}">
            <div class="block-body"
                 [id]="expandedSessionString + session.id"
                 (contextmenu)="addToSelectedSessions(session.id, session.periodIndex)"
                 [matMenuTriggerFor]="sessionMenuRef"
                 (menuOpened)="sessionMenuOpened({block: selectedBlock, blockId: selectedBlock!.id, blockMenuId: expandedSessionString + session.id, session, onlySessionRelated: true, showExpandedViewButtons: true, isCombineSessionsEnabled: isSessionsNextToEachOther, selectedSessionIndexes, selectedSessionIndexes$, selectedSessionIds, selectedSessionIds$})">

              <div class="session"
                   [class.edited]="session.hasClassNameDefinition">
                {{ session.sessionName || '--'}}
              </div>

              <div class="period">
                {{session.periodName || '--'}}
                <div class="lock-icon">
                  <bcm-icon *ngIf="session.isLocked" icon="fa fa-lock"></bcm-icon>
                </div>
              </div>

              <div class="line staff-side"
                   [class.dashed]="isHovered && isStaffDraggingActive && (!session.mainStaffId || shiftKeyPressed) &&
                   (selectedBlock! | getSubjectToSession: row[0].subjectToYearGroupId).subjectId &&
                   (selectedBlock! | getSubjectToSession: session.subjectToYearGroupId).subjectId !== freeSubjectId"
                   [class.staff-filter-active-border]="staffToSessionActiveId === session.id"
                   cdkDropList
                   [id]="expandedStaffString + session.id"
                   (cdkDropListDropped)="dropStaff($event)"
                   (mouseenter)="staffDragEnterInformation($event, selectedBlock!.sessions, expandedStaffString, !this.shiftKeyPressed)"
                   (mouseleave)="staffDragLeaveInformation()">
                <bromcom-expanded-view-staff-section [session]="session"
                                                     [staffConflictedSessionIds]="staffConflictedSessionIds"
                                                     [staffs]="staffs"
                                                     [staffToSessionActiveId]="staffToSessionActiveId"
                ></bromcom-expanded-view-staff-section>
              </div>

              <div class="line room-side"
                   [class.dashed]="isHovered && isRoomDraggingActive && !session.roomId &&
                   (selectedBlock! | getSubjectToSession: row[0].subjectToYearGroupId).subjectId &&
                   (selectedBlock! | getSubjectToSession: session.subjectToYearGroupId).subjectId !== freeSubjectId"
                   [class.room-filter-active-border]="roomToSessionActiveId === session.id"
                   cdkDropList
                   [id]="expandedRoomString + session.id"
                   (cdkDropListDropped)="dropRoom($event)"
                   (mouseenter)="roomDragEnterInformation($event, selectedBlock!.sessions, expandedRoomString)"
                   (mouseleave)="roomDragLeaveInformation()">
                <div class="session-room-icon marker">
                  <bcm-icon class="icon" icon="far fa-map-marker-alt"
                            [class.room-conflict]="roomConflictedSessionIds.includes(session.id)"
                            [class.room-filter-active]="roomToSessionActiveId === session.id"
                            (click)="roomToSession($event, session)"></bcm-icon>
                </div>
                <div class="text">
                  <bcm-tooltip *ngIf="session.roomId; else empyRoomIdTemplate"
                               [message]="'Room: ' + (session.roomId | roomPipe: rooms)"
                               trigger="hover">
                    <div class="session-room">{{session.roomId | roomPipe: rooms}}</div>
                  </bcm-tooltip>
                  <ng-template #empyRoomIdTemplate>
                    <bcm-tooltip
                        [message]="'Room: --'"
                        trigger="hover">
                        <div class="session-room">--</div>
                    </bcm-tooltip>
                  </ng-template>
                </div>
              </div>
            </div>
          </td>

          <td class="action border">
            <bcm-icon class="icon"
                      icon="far fa-ellipsis-h"
                      [id]="expandedBulkSessionString + row[0].className + selectedBlock!.sessions[0].id"
                      [matMenuTriggerFor]="sessionMenuRef"
                      (menuOpened)="sessionMenuOpened({block: selectedBlock, blockId: selectedBlock!.id, blockMenuId: expandedBulkSessionString + row[0].className + selectedBlock!.sessions[0].id, session: transformSessionsByClass((selectedBlock?.sessions ?? []), row[0].subjectToYearGroupId, row[0].className), showExpandedViewButtons: true, onlySessionRelated: false })">
            </bcm-icon>
          </td>
        </tr>
      </ng-container>
      </tbody>
    </table>
    <div class="footer"></div>
  </div>
</div>
