import { Component, Input } from '@angular/core';
import { ICurriculumSessions } from '../../../../../../../_shared/models/ICurriculumSessions';
import { IStaff } from '../../../../../../../_shared/models/IStaff';
import { RelationshipsService } from '../../../../../../services/relationships.service';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@bromcom/ui';

@Component({
  selector: 'bromcom-expanded-view-staff-section',
  templateUrl: './expanded-view-staff-section.component.html',
  styleUrls: ['./expanded-view-staff-section.component.scss']
})
export class ExpandedViewStaffSectionComponent {
  @Input() session!: ICurriculumSessions;
  @Input() staffConflictedSessionIds: number[] = [];
  @Input() staffs: IStaff[] = [];
  @Input() staffToSessionActiveId: number | null = null;
  @Input() isFreeSession = false;
  @Input() isFeedbackSession = false;

  constructor(
    private relationshipService: RelationshipsService,
    private translate: TranslateService,
    protected snackbar: SnackbarService
  ) {
  }

  staffToSession(event: Event): void {
    event.preventDefault();
    event.stopPropagation();

    if (this.isFeedbackSession) {
      return;
    }

    if (this.isFreeSession) {
      this.snackbar.info(this.translate.instant('Free sessions cannot have teachers or rooms assigned.'))
      return;
    }

    if (this.staffToSessionActiveId === this.session.id) {
      this.relationshipService.staffToSessionActiveId$.next(!this.staffToSessionActiveId ? this.session.id : null);
    } else {
      this.relationshipService.staffToSessionActiveId$.next(this.session.id);
    }
    this.relationshipService.roomToSessionActiveId$.next(null);
  }
}
