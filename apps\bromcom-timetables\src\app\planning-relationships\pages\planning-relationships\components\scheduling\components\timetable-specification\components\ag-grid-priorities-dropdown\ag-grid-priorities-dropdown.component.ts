import { Component, ElementRef, ViewChild } from '@angular/core';

@Component({
  selector: 'bromcom-ag-grid-priorities-dropdown',
  templateUrl: './ag-grid-priorities-dropdown.component.html',
  styleUrls: ['./ag-grid-priorities-dropdown.component.scss']
})
export class AgGridPrioritiesDropdownComponent {
  @ViewChild('agGridPriorityDropdown') agGridPriorityDropdown: ElementRef = {} as ElementRef;
  params!: any;
  gridApi!: any;
  data: any[] = [];
  placeholder = '';
  value: number[] = [];

  agInit(params: any): void {
    this.params = params;
    this.gridApi = params.gridApi;
    this.value = params.data.order;
    this.data = [...Array(params.arrayLength).keys()].map(number => {
      return {
        id: number + 1,
        text: number + 1
      }
    })
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.agGridPriorityDropdown.nativeElement.set(this.value)
    }, 0)
  }

  getValue(): number[] {
    return this.value;
  }

  updateValue(): void {
    this.agGridPriorityDropdown.nativeElement.get().then((data: number[]) => {
      this.value = data;

      this.gridApi.getRowNode(this.params.data.id).setDataValue([this.params.colDef.field], data);
    })
  }
}
