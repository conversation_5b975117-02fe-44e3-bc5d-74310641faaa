import { customComparator, IListOption, transformToAGGridConfig } from '@bromcom/core';
import { AgGridNoDataComponent } from '@bromcom/ui';
import {
  CheckboxSelectionCallbackParams,
  EditableCallbackParams,
  IRowNode,
  ITooltipParams,
  RowSelectedEvent
} from 'ag-grid-community';
import { SubstituteStaffModalComponent } from './substitute-staff-modal.component';

export function substituteStaffGridOptions(this: SubstituteStaffModalComponent) {

  const editable = (params: EditableCallbackParams | IRowNode | CheckboxSelectionCallbackParams) => params.data && params.data?.assigned === 0;

  return transformToAGGridConfig({
    rowSelection: 'single',
    isRowSelectable: (params) => editable(params),
    noRowsOverlayComponent: AgGridNoDataComponent,
    noRowsOverlayComponentParams: {
      style: 'display: flex; justify-content: center; align-items: center; height: 100%; width: 100%; min-height: 162px; padding: 48px 0 0 0',
      noRowsMessageFunc: () => this.translate.instant(`No data available!`)
    },
    onRowSelected: (event: RowSelectedEvent) => {
      if (!event.node.isSelected() && !this.gridApi.getSelectedRows().length) {
        event.node.setSelected(true);
        return
      }
      this.selectedStaffData = this.gridApi.getSelectedRows().length ? this.gridApi.getSelectedRows()[0] : null;
    },
    tooltipShowDelay: 100,
    columnDefs: [
      {
        width: 48,
        headerCheckboxSelection: false,
        showDisabledCheckboxes: true,
        checkboxSelection: (params: CheckboxSelectionCallbackParams<IListOption>) => {
          return !!params.data && editable(params);
        },
        cellClass: (params) => params.node.data.assigned > 0 ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params)
      },
      {
        field: 'firstName',
        headerName: this.translate.instant('First Name'),
        wrapHeaderText: true,
        minWidth: 110,
        editable: false,
        flex: 1,
        filter: false,
        comparator: customComparator,
        cellClass: (params) => params.node.data.assigned > 0 ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params),
        menuTabs: []
      },
      {
        field: 'lastName',
        headerName: this.translate.instant('Last Name'),
        wrapHeaderText: true,
        minWidth: 110,
        editable: false,
        flex: 1,
        filter: false,
        comparator: customComparator,
        cellClass: (params) => params.node.data.assigned > 0 ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params),
        sort: 'asc',
        menuTabs: []
      },
      {
        field: 'code',
        headerName: this.translate.instant('Staff Code'),
        wrapHeaderText: true,
        minWidth: 110,
        editable: false,
        flex: 1,
        comparator: customComparator,
        cellClass: (params) => params.node.data.assigned > 0 ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params),
        filter: false,
        menuTabs: []
      },
      {
        field: 'totalContactTime',
        headerName: this.translate.instant('Available'),
        wrapHeaderText: true,
        minWidth: 110,
        editable: false,
        flex: 1,
        comparator: customComparator,
        cellClass: (params) => params.node.data.assigned > 0 ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params),
        sort: 'asc',
        filter: false,
        menuTabs: []
      },
      {
        field: 'assigned',
        headerName: this.translate.instant('Assigned'),
        minWidth: 110,
        editable: false,
        flex: 1,
        filter: false,
        comparator: customComparator,
        cellClass: (params) => params.node.data.assigned > 0 ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params),
        sort: 'asc',
        menuTabs: []
      },
      {
        field: 'remaining',
        headerName: this.translate.instant('Remaining'),
        minWidth: 110,
        editable: false,
        flex: 1,
        filter: false,
        comparator: customComparator,
        cellClass: (params) => params.node.data.assigned > 0 ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params),
        sort: 'asc',
        menuTabs: []
      }
    ]
  })
}

function tooltipValueGetter(this: SubstituteStaffModalComponent, params: ITooltipParams) {
  if (params.value && params.value.length > 5) {
    return params.value;
  } else {
    return;
  }
}
