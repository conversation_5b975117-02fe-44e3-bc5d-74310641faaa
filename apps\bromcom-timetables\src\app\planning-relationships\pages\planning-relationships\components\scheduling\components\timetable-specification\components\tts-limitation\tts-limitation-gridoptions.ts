import { IListOption, NOOP, transformToAGGridConfig } from '@bromcom/core';
import { AgGridNoDataComponent } from '@bromcom/ui';
import { CheckboxSelectionCallbackParams, ICellEditorParams, ICellRendererParams, IsFullWidthRowParams } from 'ag-grid-community';
import { AddNewRowComponent } from '../../../../../../../../../projects/pages/new-project-wizard/components/add-new-row/add-new-row.component';
import { TtsGridActionsComponent } from '../tts-grid-actions/tts-grid-actions.component';
import { AgGridPeriodDropdownComponent } from '../ag-grid-period-dropdown/ag-grid-period-dropdown.component';
import { PeriodsCellRenderer } from '../../cell-renderers/periods-cell-renderer';
import { TtsLimitationComponent } from './tts-limitation.component';
import { SubjectCellRenderer } from '../../cell-renderers/subject-cell-renderer';
import {
    AgGridSubjectDropdownComponentComponent
  } from '../ag-grid-subject-dropdown-component/ag-grid-subject-dropdown-component.component';
import {
    AgGridDepartmentSubjectDropDownComponent
  } from '../ag-grid-department-subject-drop-down/ag-grid-department-subject-drop-down.component';
import { AgGridSelectDropdownComponent } from '../ag-grid-select-dropdown/ag-grid-select-dropdown.component';
import { AgGridNumericComponent } from '@bromcom/ui';
import { SubjectsCellRenderer } from '../../cell-renderers/subjects-cell-renderer';
import { AgGridCustomNumericFieldComponent } from '../ag-grid-custom-numeric-field/ag-grid-custom-numeric-field.component';

export function gridOptions(this: TtsLimitationComponent, config: any) {
  const {
    onAddNewRow = NOOP,
    onAcceptNewRow = NOOP,
    onCancelAddingNewRow = NOOP,
    onEditRow = NOOP,
    onDeleteRow = NOOP,
    onAcceptRow = NOOP,
    onCancelEditRow = NOOP,
    onExcludeRow = NOOP,
    onIncludeRow = NOOP
  } = config

  return transformToAGGridConfig({
    getRowId: params => params.data.id,
    animateRows: false,
    suppressRowClickSelection: true,
    suppressClickEdit: true,
    suppressCellFocus: true,
    domLayout: 'autoHeight',
    editType: 'fullRow',
    rowSelection: 'multiple',
    noRowsOverlayComponent: AgGridNoDataComponent,
    noRowsOverlayComponentParams: {
      style: 'display: flex; justify-content: center; align-items: center; height: 100%; width: 100%; min-height: 162px; padding: 48px 0 0px 0',
      noRowsMessageFunc: () => this.translate.instant(`No data available! No Limitations were found.`)
    },
    pinnedBottomRowData: [{}],
    fullWidthCellRenderer: AddNewRowComponent,
    fullWidthCellRendererParams: {
      onAddNewRow,
      label: this.translate.instant('Add New Row')
    },
    tooltipShowDelay: 500,
    isFullWidthRow: (params: IsFullWidthRowParams) => {
      return !params.rowNode.data.id;
    },
    onSelectionChanged: () => {
      this.isRemoveBulkDisabled = !this.gridApi.getSelectedRows().length;
    },
    rowHeight: 56,
    columnDefs: [
      { 
        minWidth: 48,
        width: 48,
        headerCheckboxSelection: true,
        checkboxSelection: (params: CheckboxSelectionCallbackParams<IListOption>) => {
          return !!params.data;
        }
      },
      {
        field: 'subjectsIds',
        headerName: this.translate.instant('Subjects'),
        minWidth: 178,
        flex: 1.5,
        editable: true,
        menuTabs: ['filterMenuTab'],
        headerClass: 'left-padding',
        tooltipValueGetter: params => {
          if (!params.data.subjectsIds || !this.subjectsOptions) {
            return '';
          }
      
          const selectedSubjectNames = this.subjectsOptions
            .filter(subject => params.data.subjectsIds.includes(subject.id))
            .map(subject => subject.name);
      
          return selectedSubjectNames.join(', ') || '';
        },
        valueGetter: params => {
            const selectedIds = params.data.subjectsIds || [];
            const allIds = this.subjectsOptions.map(subject => subject.id);
            const allSelected = allIds.length > 0 && allIds.every(id => selectedIds.includes(id));
            if (allSelected) {
                return this.translate.instant('All subjects');
            }

            return this.subjectsOptions
                .filter(subject => params.data.subjectsIds?.includes(subject.id))
                .map(subject => subject.name)
                .join(', ');
          },
        cellRenderer: SubjectsCellRenderer,
        cellEditorSelector: params => {
            return {
              component: AgGridDepartmentSubjectDropDownComponent,
              popup: true,
              popupPosition: 'under',
              params: {
                values: this.subjectsOptions,
                value: params.data.subjectsIds || [],
                gridApi: this.gridApi,
                params,
                color: 'gray',
                placeholder: this.translate.instant('Subjects'),
                isValid: this.validateSubjects(params.data.subjectsIds || []),
                validate: this.validateSubjects
              }
            }
        },
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            subjects: this.subjectsOptions.filter(subject => params.data.subjectsIds?.includes(subject.id)).map(subject => subject.name),
            isValid: this.validateSubjects(params.data.subjectsIds || [])
          }
        },
        cellEditorParams: (params: ICellEditorParams) => {
            const groupedSubjects = this.groupSubjectsBySection(this.subjectsOptions);
            return {
                placeholder: this.translate.instant('Subjects'),
                checkboxes: true,
                values: this.subjectsOptions,
                value: params.data.subjectsIds || [],
                gridApi: this.gridApi,
                gridData: this.gridData,
                allowDisableSubjectOptions: true,
                params
            }
        }
      },
      {
        field: 'sessionsPerPeriod',
        headerName: this.translate.instant('Sessions per period'),
        minWidth: 178,
        flex: 1.5,
        editable: true,
        menuTabs: ['filterMenuTab'],
        valueGetter: params => params.data.sessionsPerPeriod,
        valueParser: (params) => {
          const value = parseFloat(params.newValue);
          if (!isNaN(value) && value > 0) {
            return value;
          } else {
            return params.oldValue;
          }
        },
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            sessionsPerPeriod: params.data.sessionsPerPeriod,
            isValid: this.validateSessionPerPeriod(params.data.sessionsPerPeriod)
          };
        },
        cellEditorFramework: AgGridCustomNumericFieldComponent, 
        cellEditorParams: {
          placeholder: this.translate.instant('Sessions per period'),
        }
      },           
      {
        field: 'actions',
        headerName: this.translate.instant('Actions'),
        minWidth: 128,
        filter: false,
        sortable: false,
        menuTabs: [],
        flex: 0.8,
        headerClass: 'text-center',
        cellStyle: { display: 'flex', justifyContent: 'center' },
        cellRenderer: TtsGridActionsComponent,
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            gridId: 'tts-ag-grid',
            type: 'limitation',
            onAcceptNewRow,
            onCancelAddingNewRow,
            onEditRow,
            onDeleteRow,
            onAcceptRow,
            onCancelEditRow,
            onExcludeRow,
            onIncludeRow
          }
        },
        editable: false
      }
    ]
  })
}

