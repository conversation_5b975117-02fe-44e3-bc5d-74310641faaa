@import "apps/bromcom-timetables/src/assets/styles/variables";

@mixin displayFlexRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

$body-height: 70vh;
$header-height: 49px;
$inner-padding: 24px * 2;
$action-bar-margin-bottom: calc(8px);

.swap-data-modal {
  z-index: 10;

  .modal-body {
    padding: 24px;
    height: fit-content;
    padding-bottom: 5px !important;

    .action-bar {
      @include displayFlexRow;
      flex-wrap: wrap;
      margin-bottom: $action-bar-margin-bottom;

      .left-side {
        @include displayFlexRow;
        align-items: center;

        .search-field {
          min-width: 256px;
        }
      }
    }

    .table-container {
      height: 60vh;
    }

    .swap-feedback-holder {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 24px;
      align-self: stretch;
      padding-bottom: 0px;
    }

    .info-holder {
      display: flex;
      padding: 8px 16px;
      align-items: center;
      gap: 12px;
      align-self: stretch;
      border-radius: 8px;
      background: $color-blue-100;
      color: $color-blue-900;
      font-size: 16px;
      font-weight: 500;

      &.swap-not-allowed-warning {
        background: $color-red-tertiary-100 !important;
        color: $color-red-tertiary-800 !important;
      }
    }

    .warning-holder {
      display: flex;
      padding: 8px 16px;
      align-items: center;
      gap: 12px;
      align-self: stretch;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
      background: $color-amber-tertiary-700;
      color: $color-amber-tertiary-800;
    }

    .table-holder {
      width: 100%;
      max-height: 50vh;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      overflow-y: auto;
      -ms-overflow-style: none;
      scrollbar-width: none;

      table {
        width: 100%;
        border-radius: 6px;
        border: 1px solid $color-blue-grey-200;
      }

      tr {
        height: 48px;
      }

      th {
        padding-left: 5px;
        background: $color-blue-grey-100;
      }

      td {
        padding: 5px;
      }
    }

    .swap-selection-holder {
      display: flex;
      padding: 0px;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 500;
    }

    ::ng-deep .disabled {
      opacity: 0.7;
    }
  }

  .swap-feedback-butons {
    justify-content: space-between;
    display: flex;
    width: 100%;

    .feedback-action-buttons {
      gap: 8px;
      display: flex;
    }
  }
}

.dot {
  display: flex;
  width: 8px;
  height: 8px;
  padding: 0px 4px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 24px;
  background: $color-primary-blue-600;
}

.mr-15 {
  margin-right: 15px;
}
