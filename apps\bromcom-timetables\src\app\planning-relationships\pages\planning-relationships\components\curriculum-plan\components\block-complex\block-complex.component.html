<div class="block-container"
     [ngClass]="{'active-block': isActiveBlock}"
     (click)="selectActiveSideBlock()"
     (mouseenter)="onMouseEnter()"
     (mouseleave)="onMouseLeave()">
  <div class="header">
    <div class="block-type-short-name">
      <div class="circle">{{BLOCK_TYPE[_complexBlock.blockTypeId]}}</div>
    </div>
    <div class="block-name">{{_complexBlock.blockCode}}</div>

    <div class="menu-container">
      <div class="menu">
        <bcm-icon (click)="openBlockActionsMenu()" class="icon" icon="far fa-ellipsis-h"></bcm-icon>
        <bromcom-curriculum-plan-block-actions [block]="_complexBlock"
                                               [complexBlockTransformedSessions]="_complexBlockTransformedSessions"
                                               (transformClicked)="transformBlock($event)"
                                               (copyClicked)="copyBlock($event)"
                                               (deleteClicked)="deleteBlock(_complexBlock.id)"
                                               (unScheduleClicked)="unScheduleSessions($event)"
                                               (spreadToBandsClicked)="spreadToBands($event)"
                                               (splitToBandsClicked)="splitToBands($event)"
                                               (openEditClassCodesModalEvent)="openEditClassCodesModal($event)"
                                               (openLinkBlocksModalComponentEvent)="openLinkBlocksModal($event)"
                                               [openBlockActionsMenu$]="openBlockActionsMenu$"></bromcom-curriculum-plan-block-actions>
      </div>
      <div class="full-size">
        <bcm-icon class="icon" icon="far fa-expand-alt" (click)="expandBlock()"></bcm-icon>
      </div>
    </div>
  </div>

  <div class="block-type-full-name">
    <div class="linked-icon" *ngIf="block.linkedPairID" [matTooltip]="(linkedBlockName$ | async) || '-'"
         [matTooltipPosition]="'above'" (mouseenter)="getLinkedBlockName()">
      <bcm-icon class="icon"
                icon="far fa-link"></bcm-icon>
    </div>

    <div class="name"
         [matTooltipDisabled]="block.blockName.length < 15"
         [matTooltip]="block.blockName"
         [matTooltipPosition]="'above'">
      {{_complexBlock.blockName}}
    </div>

    <div class="cross-band-icon" *ngIf="block.bandIds.length > 1" [matTooltip]="bandNames"
         [matTooltipPosition]="'above'">X
    </div>
  </div>

  <div class="subject-code-color-header" [ngStyle]="{'max-width.px': complexBodyContainerWidth}" [class.drop-active]="isHovered && isSubjectDraggingActive">
    <div *ngFor="let subject of _complexBlock.subjectToYearGroups | filterToContainsSubject"
         [matTooltipDisabled]="subject.code!.length < 5"
         [matTooltip]="subject.code!"
         [matTooltipPosition]="'above'"
         [ngStyle]="{'background-color': '#' + subject?.color, 'color': '#' + subject?.textColor}"
         class="code-color-header">
      <span class="subject-code">{{subject.code}}</span>
    </div>

    <div *ngIf="_complexBlock.subjectToYearGroups.length === 1">--</div>
  </div>

  <div class="period-count">{{_complexBlock.periodCount}}</div>

  <div #complexBodyContainer class="complex-body-container">
    <div class="subject-info-container-side">
      <bcm-icon class="icon" icon="far fa-arrow-to-left" [class.disable]="lastVisibleIndex <= 3 || lengthOfRow <= 3"
                (click)="setVisibleIndex(originalLastVisibleIndex - 1)"></bcm-icon>
      <div
        *ngFor="let className of (!!_complexBlock.subjectToYearGroups[1]?.subjectId && isSubjectDraggingActive && isHovered ? classNames.concat(['', '']) : classNames) | slice: 1, index as classNameIndex"
        class="session-info-container-side class-name"
        [class.remove-class]="(_complexBlock | getIsOneClassForSubjectComplex:originalUniqueClassNames[classNameIndex]) > 1"
        [matMenuTriggerFor]="classContextMenuRef"
        [matMenuTriggerData]="{data: {block, blockId: block.id, subjectToYearGroupId: _complexBlock | getSubjectToYearGroupForSession:originalUniqueClassNames[classNameIndex], className: originalUniqueClassNames[classNameIndex], disableRemoveClass: (_complexBlock | getIsOneClassForSubjectComplex:originalUniqueClassNames[classNameIndex]) === 1 || className.includes(emptySubjectPlaceholderString)}}"
        [class.dashed]="!className.includes(emptySubjectPlaceholderString) && (isStaffDraggingActive || isRoomDraggingActive) && isHovered"
        cdkDropList
        [id]="'simpleComplexFullClass-' + block.id + '-' + className.slice(1)"
        (cdkDropListDropped)="dropToClass($event, +className.slice(1))">
        {{className.includes(emptySubjectPlaceholderString) ? '' : className}}
      </div>
    </div>

    <div class="column">
      <div *ngFor="let row of _complexBlockTransformedSessions; index as rowIndex" class="subject-row-container">
        <div *ngFor="let classArray of row, index as classArrayIndex" class="class-row-container">
          <div *ngFor="let session of classArray, index as sessionIndex">
            <div
              *ngIf="classArrayIndex === 0"
              [ngClass]="{'display-none': sessionIndex < lastVisibleIndex - 3 || sessionIndex >= lastVisibleIndex && classArrayIndex === 0}"
              class="subject-info-container">
              <div [matMenuTriggerFor]="subjectMenuRef"
                   (menuOpened)="subjectMenuOpened({data: {block, blockId: block.id, subjectToYearGroupId: session.subjectToYearGroupId, className: session.className, sessionIndex, isComplexBlock: true, selectedSubjectSessionIds$, selectedSubjectSessionIds, showExpandedViewButtons: false}})"
                   (contextmenu)="addToSelectedSubjectSessions(session.id)"
                   [class.remove-subject]="!isSubjectDraggingActive">
                <div class="subject-info-container-center"
                     [class.selected]="selectedSubjectSessionIds.includes(session.id)"
                     [class.dashed]="isHovered && isSubjectDraggingActive &&
                     (!(_complexBlock | getSubjectToSession: session.subjectToYearGroupId).subjectId ||
                     (_complexBlock | getSubjectToSession: session.subjectToYearGroupId).subjectId === freeSubjectId)"
                     cdkDropList
                     [ngStyle]="{
                   'background-color': '#' + (_complexBlock | getSubjectToSession: session.subjectToYearGroupId).color,
                   'color': '#' + (_complexBlock | getSubjectToSession: session.subjectToYearGroupId).textColor
                   }"
                     [id]="'subject' + session.id"
                     (cdkDropListDropped)="dropSubject($event)"
                     [cdkDropListData]="_complexBlock.subjectToYearGroups">

                  <div
                    class="subject-name">{{(_complexBlock | getSubjectToSession: session.subjectToYearGroupId).code}}</div>

                  <div class="subject-periods">
                    <div class="single-period">
                      S: {{session.joinedSessions.length === 1 ? 1 : 0 }}
                    </div>
                    <div class="double-period">
                      D: {{session.joinedSessions.length === 2 ? 1 : 0 }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div
              [ngClass]="{'display-none': sessionIndex < lastVisibleIndex - 3 || sessionIndex >= lastVisibleIndex,
              'left-double': session.joinedSessions.length > 1 && block && (block.sessions | getSessionPeriodIndex:session.joinedSessions[1]) > session.periodIndex!,
               'right-double': session.joinedSessions.length > 1 && block && (block.sessions | getSessionPeriodIndex:session.joinedSessions[1]) < session.periodIndex!
               }"
              class="session-info-container-center"
              [matMenuTriggerFor]="sessionMenuRef"
              (menuOpened)="sessionMenuOpened({block, blockId: block.id, blockMenuId: session.id, session, isComplexBlock: true, onlySessionRelated: true})">

              <div class="staff-side"
                   [class.dashed]="isHovered && isStaffDraggingActive && (!session.mainStaffId || shiftKeyPressed) &&
                   (block | getSubjectToSession:session.subjectToYearGroupId).subjectId &&
                   (block | getSubjectToSession: session.subjectToYearGroupId).subjectId !== freeSubjectId"
                   cdkDropList
                   [id]="'staff' + session.id"
                   (cdkDropListDropped)="dropStaff($event)"
                   [cdkDropListData]="session.staffIds"
                   (mouseenter)="staffDragEnterInformation($event, block.sessions, 'staff', !this.shiftKeyPressed)"
                   (mouseleave)="staffDragLeaveInformation()">
                <bcm-icon *ngIf="session.additionalStaffIds.length" class="plus-icon" icon="far fa-plus"></bcm-icon>

                <div class="session-staff-icon">
                  <bcm-icon class="icon" icon="far fa-users-class"></bcm-icon>
                </div>

                <div class="session-staff"
                     [matTooltipDisabled]="!(session.staffCodes | csv: (session.mainStaffCode ? [session.mainStaffCode] : []):session.additionalStaffCodes).length"
                     [matTooltip]="session.staffCodes | csv: (session.mainStaffCode ? [session.mainStaffCode] : []):session.additionalStaffCodes"
                     [matTooltipPosition]="'above'">{{session.mainStaffCode || '&#45;&#45;'}}
                </div>
              </div>

              <div class="room-side"
                   [class.dashed]="isHovered && isRoomDraggingActive && !session.roomId &&
                   (block | getSubjectToSession:session.subjectToYearGroupId).subjectId &&
                   (block | getSubjectToSession: session.subjectToYearGroupId).subjectId !== freeSubjectId"
                   cdkDropList
                   [id]="'room' + session.id"
                   (cdkDropListDropped)="dropRoom($event)"
                   [cdkDropListData]="session.roomId"
                   (mouseenter)="roomDragEnterInformation($event, block.sessions, 'room')"
                   (mouseleave)="roomDragLeaveInformation()">
                <div class="session-room-icon">
                  <bcm-icon class="icon" icon="far fa-map-marker-alt"></bcm-icon>
                </div>

                <div class="session-room"
                     [matTooltip]="'Room: ' + (session.roomCode  ?? '--')"
                     [matTooltipPosition]="'above'">{{session.roomCode ?? '&#45;&#45;' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bottom-add-new-subject-row"
           [ngClass]="{
                'set-visible': isHovered && isSubjectDraggingActive,
                'hide-add-new-block-bottom': (!isHovered || !isSubjectDraggingActive)
               }">

        <div *ngFor="let element of lengthOfRowWithNull, index as i"
             [ngClass]="{'display-none': i < lastVisibleIndex - 3 || i >= lastVisibleIndex }">
          <bromcom-block-new-subject-placeholder
            [block]="_complexBlock"
            [listIndex]="listIndex"
            [showSides]="false"
            [isComplexBlock]="false"
            [emptySubjectPlaceholderId]="this._complexBlock.id + '*' + emptySubjectPlaceholderString + (_complexBlockTransformedSessions.length + 1) + '-' + i"
            (addSubjectToBlock)="dropSubject($event)"></bromcom-block-new-subject-placeholder>
        </div>
      </div>

    </div>

    <!--    ////////////////////////    -->
    <!--    ADD NEW  SUBJECT SECTION    -->
    <!--    ////////////////////////    -->

    <div [ngClass]="{
              'set-visible': isHovered && isSubjectDraggingActive,
              'hide-add-new-block': (!isHovered || !isSubjectDraggingActive)
              }">
      <bromcom-block-new-subject-placeholder [block]="_complexBlock"
                                             [listIndex]="listIndex"
                                             [showSides]="false"
                                             [classNames]="classNames"
                                             [isComplexBlock]="true"
                                             [emptySubjectPlaceholderId]="this._complexBlock.id + '*' + 'emptySubjectPlaceholder-1'"
                                             (addSubjectToBlock)="dropSubject($event)"></bromcom-block-new-subject-placeholder>
    </div>

    <div class="subject-info-container-side">
      <bcm-icon class="icon" icon="far fa-arrow-to-right"
                [class.disable]="lastVisibleIndex === lengthOfRow || lengthOfRow <= 3 || isHovered && isSubjectDraggingActive"
                (click)="setVisibleIndex(originalLastVisibleIndex + 1)"></bcm-icon>
      <div
        *ngFor="let className of (!!_complexBlock.subjectToYearGroups[1]?.subjectId && isSubjectDraggingActive && isHovered ? classNames.concat(['', 'C1']) : classNames) | slice: 1"
        class="session-info-container-side class-name"></div>
    </div>
  </div>

  <div class="bottom"></div>
</div>
