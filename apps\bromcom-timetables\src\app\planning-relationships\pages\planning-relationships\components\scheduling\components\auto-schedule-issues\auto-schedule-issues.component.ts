import {Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import { FormControl } from '@angular/forms';
import {GridApi, GridOptions, GridReadyEvent, ICellRendererParams} from 'ag-grid-community';
import { Subject, takeUntil } from 'rxjs';
import { AutoScheduleService } from '../../../../../../services/auto-schedule.service';
import { TranslateService } from '@ngx-translate/core';
import { gridOptions } from './auto-schedule-issues-gridoptions';
import { IPreSchedule } from '../../../../../../../_shared/models/IPreSchedule';
import { IIssueData } from '../../../../../../../_shared/models/IIssueData';
import { SnackbarService } from '@bromcom/ui';
import * as saveAs from 'file-saver';
import { TitleHelperService } from '../../../../../../../_shared/services/title-helper.service';
import {
  IAutoScheduleIssuesActions
} from "./components/auto-schedule-issues-actions/auto-schedule-issues-actions.component";
import {BaseModalComponent} from "../../../../../../../_shared/components/BaseModalComponent";
import {IIssueDetailsData} from "../../../../../../../_shared/models/IIssueDetailsData";
import {IAutoScheduleReasons} from "./components/auto-schedule-reasons/auto-schedule-reasons.component";
import {reasonsGridOptions} from "./components/auto-schedule-reasons/auto-schedule-reasons-gridoptions";
import {SchedulingService} from "../../../../../../services/scheduling.service";

@Component({
  selector: 'bromcom-auto-schedule-issues',
  templateUrl: './auto-schedule-issues.component.html',
  styleUrls: ['./auto-schedule-issues.component.scss'],
})
export class AutoScheduleIssuesComponent extends BaseModalComponent implements OnInit, OnDestroy {
  @Output() changeToIssues: EventEmitter<void> = new EventEmitter();

  @Input() timetableId!: number;
  @Input() projectId!: number;
  @ViewChild('autoScheduleReasonModal') autoScheduleReasonModal!: ElementRef;

  params!: GridReadyEvent;
  gridApi!: GridApi;
  gridOptions!: GridOptions;

  reasonsGridData: Partial<any>[] = [];
  reasonsGridApi!: GridApi;
  reasonsGridOptions!: GridOptions;
  openedReasons: IIssueDetailsData[] = [];
  openedSessionName: string = "";

  acceptedArray: IIssueData[] = [];

  gridData: Partial<any>[] = [];
  searchControl: FormControl = new FormControl<string>('');
  originalRowData: IIssueData[] = [];
  autoScheduleDetails: IPreSchedule = {blockIds: [], nccGroupIds: [], keepExistingSchedule: false};
  projectName = '';
  timetableName = '';
  readonly unsubscribe$: Subject<void> = new Subject();
  isReturnToFeedback = false;

  constructor(
    protected translate: TranslateService,
    private autoScheduleService: AutoScheduleService,
    private snackbar: SnackbarService,
    private titleHelperService: TitleHelperService,
    private schedulingService: SchedulingService
  ) {
    super();
  }

  rerunAutoSchedule(): void {
    this.isReturnToFeedback = true;
    this.autoScheduleService.acceptedData.next([]);

    this.autoScheduleService.applyIssues(this.timetableId, this.originalRowData)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe({
        next: () => {
          this.schedulingService.nccChanges$.next();
          this.changeToIssues.emit();
        },
        error: ({ error }) => {
          if (error?.validationErrors?.[0]) {
            this.snackbar.error(error?.validationErrors?.[0].errorMessage);
          } else {
            this.snackbar.error();
          }
        }
      });
  }

  ngOnInit() {
    this.gridOptions = gridOptions.call(this, {
      onAcceptRow: (params: ICellRendererParams) => this.onAcceptRow(params),
      onOpenReasons: (params: ICellRendererParams) => this.onOpenReasons(params)
    } as unknown as IAutoScheduleIssuesActions & IAutoScheduleReasons) as GridOptions;
    this.searchControl.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(searchValue => {
        if (!searchValue && (searchValue == null || searchValue === '')) {
          this.gridData = [...this.originalRowData];
          this.gridApi.setRowData(this.gridData);
        }

        this.gridApi.setQuickFilter(searchValue ?? '');
      })

    this.reasonsGridOptions = reasonsGridOptions.call(this) as GridOptions;

    this.autoScheduleService.data
      .pipe(
        takeUntil(this.unsubscribe$))
        .subscribe((details) => {
          this.autoScheduleDetails = details;

          if(!this.isReturnToFeedback){
            if (this.autoScheduleDetails != null && this.autoScheduleDetails.blockIds?.length) {
              this.autoScheduleService.fetchIssues(this.timetableId, this.autoScheduleDetails)
              .pipe(takeUntil(this.unsubscribe$))
              .subscribe({
                next: (issues) => {
                  this.setupPageData(issues);
                },
                error: ({ error }) => {
                  if (error?.validationErrors?.[0]) {
                    this.snackbar.error(error?.validationErrors?.[0].errorMessage);
                  } else {
                    this.snackbar.error();
                  }
                }
              });
            }
          }
        });
  }

  setupPageData(res: IIssueData[]): void {
    this.originalRowData = res;
    this.gridData = res;
    this.gridApi.setRowData(this.gridData);
    this.gridApi.sizeColumnsToFit();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  onGridReady(params: GridReadyEvent): void {
    this.params = params;
    this.gridApi = params.api;
    this.gridApi.setDomLayout('normal');
  }

  onReasonsGridReady(params: GridReadyEvent): void {
    this.reasonsGridApi = params.api;
    this.reasonsGridApi.setDomLayout('normal');
  }

  onExport() {
    this.titleHelperService.getProjectById(this.projectId)?.subscribe(response => {
      this.projectName = response?.projectName;
    });
    this.titleHelperService.getTimetableById(this.timetableId)?.subscribe(response => {
      this.timetableName = response?.timeTableName;
    });
    this.autoScheduleService.exportIssues(this.timetableId, this.originalRowData).subscribe((data) => {
      const fileName = `${this.projectName} ${this.timetableName} Auto-Schedule Issues.xlsx`
      saveAs(data, fileName);
    })
  }

  onAcceptRow(params: ICellRendererParams): void {
    const data: IIssueData[] = [];
    this.gridApi.forEachNodeAfterFilterAndSort((node) => {
      data.push(node.data);
    });
    const newRow = data.find(row => row.sessionIds[0] === params.data.sessionIds[0])!;
    newRow.isAccepted = !newRow.isAccepted;
    const originalRow = this.originalRowData.find(row => row.sessionIds[0] === params.data.sessionIds[0]);
    if (originalRow) {
        originalRow.isAccepted = newRow.isAccepted;
    }

    this.gridData = data;

    this.gridApi.setRowData(data);
    this.gridApi.refreshCells({ force: true, columns: ['status'] });
    if (newRow.isAccepted) {
      if (!this.acceptedArray.includes(newRow)) {
        this.acceptedArray.push(newRow);
      }
    } else {
      const index = this.acceptedArray.indexOf(newRow);
      if (index !== -1) {
        this.acceptedArray.splice(index, 1);
      }
    }

    this.autoScheduleService.acceptedData.next(this.acceptedArray);
  }

  onOpenReasons(params: ICellRendererParams): void {
    this.onOpen();
    this.reasonsGridOptions = reasonsGridOptions.call(this) as GridOptions;
    this.openedSessionName = params.data.session;

    setTimeout(() => {
      this.openedReasons = params.data.issueDetails;
      this.reasonsGridData = params.data.issueDetails;
      this.reasonsGridApi.setRowData(this.reasonsGridData);
      this.autoScheduleReasonModal.nativeElement.show();
    }, 100)
  }
}
