import { <PERSON><PERSON><PERSON>es, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, SimpleChanges, ViewChild} from '@angular/core';
import { ICurriculumPlanBlock } from '../../../../../../../_shared/models/ICurriculumPlanBlock';
import { BehaviorSubject, switchMap } from 'rxjs';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';
import { SnackbarService } from '@bromcom/ui';
import { TranslateService } from '@ngx-translate/core';
import { ICurriculumSessions } from '../../../../../../../_shared/models/ICurriculumSessions';
import { BaseBlockSimpleView } from '../../_shared/base-block-simple-view';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { CurriculumPlanService } from '../../../../../../services/curriculum-plan.service';
import { BandService } from '../../../../../../services/band.service';
import { PlanningRelationshipsService } from '../../../../../../services/planning-relationships.service';
import { RelationshipsService } from '../../../../../../services/relationships.service';
import { InformationService } from '../../../../../../services/information.service';

@Component({
  selector: 'bromcom-block-complex',
  templateUrl: './block-complex.component.html',
  styleUrls: ['./block-complex.component.scss']
})
export class BlockComplexComponent extends BaseBlockSimpleView implements OnInit, OnDestroy, OnChanges {
  @ViewChild('complexBodyContainer') complexBodyContainer!: ElementRef;
  @Input()
  set complexBlock(complexBlock: ICurriculumPlanBlock) {
    const transformedData: ICurriculumSessions[][][] = [];

    complexBlock.sessions.forEach(session => {
      const className = session.className;
      const splitIndex = className.indexOf('-');
      const parentIndex = parseInt(className.substring(0, splitIndex)) - 1;
      const childIndex = parseInt(className.substring(splitIndex + 2)) - 1;
      if (!transformedData[parentIndex]) {
        transformedData[parentIndex] = [];
      }

      if (!transformedData[parentIndex][childIndex]) {
        transformedData[parentIndex][childIndex] = [];
      }
      transformedData[parentIndex][childIndex].push(session);
    });

    this._complexBlockTransformedSessions = transformedData.map(rowArray => rowArray.filter(element => element));
    this._complexBlock = complexBlock;
    this.formatClassNames();
  }

  @Output() addSubjectToComplexBlock = new EventEmitter();

  _complexBlock!: ICurriculumPlanBlock;
  _complexBlockTransformedSessions: ICurriculumSessions[][][] = [];
  transformedSessions: ICurriculumSessions[][] = []
  emptySubjectPlaceholderString = 'emptySubjectPlaceholder-'
  classNames: string[] = [];
  originalUniqueClassNames: string[] = [];
  lengthOfRow = 0;
  lastVisibleIndex = 0;
  originalLastVisibleIndex = 0;
  onlyOneEmptyPlace = false;
  lengthOfRowWithNull: null[] = [];
  selectedSubjectSessionIds$ = new BehaviorSubject<number[]>([]);
  selectedSubjectSessionIds: number[] = [];
  complexBodyContainerWidth = 0;

  constructor(
    protected override curriculumPlan: CurriculumPlanService,
    protected override curriculumPlanBlocks: CurriculumPlanBlocksService,
    protected override snackbar: SnackbarService,
    protected override translate: TranslateService,
    protected override band: BandService,
    protected override planningRelationships: PlanningRelationshipsService,
    protected override relationshipsService: RelationshipsService,
    protected override informationService: InformationService
  ) {
    super(curriculumPlan, curriculumPlanBlocks, snackbar, translate, band, planningRelationships, relationshipsService, informationService)
  }

  ngOnChanges(changes: SimpleChanges) {
    const change = changes['_complexBlock'];

    if (change) {
      this.updateComplexBodyContainerWidth();
    }
  }

  formatSessions(): void {
    const groupedSessionsBySubject = this._complexBlock.subjectToYearGroups
      .map(subject => this._complexBlock.sessions.filter(session => session.subjectToYearGroupId === subject.id))

    const groupedSessions = groupedSessionsBySubject.map(groupedSessions => groupedSessions
      .reduce((groups: { [key: string]: ICurriculumSessions[] }, session: ICurriculumSessions) => {
        const className = session.className;
        if (groups[className]) {
          groups[className].push(session);
        } else {
          groups[className] = [session];
        }
        return groups;
      }, {}));

    this.transformedSessions = groupedSessions.map(sessions => Object.values(sessions).map(this.transformSessions));

    const droppableSubjectIds = this.createDroppableSubjectIds();
    const droppableRoomIds = this._complexBlock.sessions.map(session => 'room' + session.id.toString());
    const droppableStaffIds = this._complexBlock.sessions.map(session => 'staff' + session.id.toString());

    const simpleComplexFullClassIds = this._complexBlock.sessions
      .map(session => `simpleComplexFullClass-${this._complexBlock.id}-${session.classIndex}`);

    this.curriculumPlanBlocks.droppableSubjectIds$.next([...this.curriculumPlanBlocks.droppableSubjectIds$.getValue(), ...droppableSubjectIds]);
    this.curriculumPlanBlocks.droppableRoomPlaceIds$.next([...this.curriculumPlanBlocks.droppableRoomPlaceIds$.getValue(), ...droppableRoomIds, ...simpleComplexFullClassIds]);
    this.curriculumPlanBlocks.droppableStaffPlaceIds$.next([...this.curriculumPlanBlocks.droppableStaffPlaceIds$.getValue(), ...droppableStaffIds, ...simpleComplexFullClassIds]);
  }


  dropSubject(event: CdkDragDrop<any>): void {
    if (!event.isPointerOverContainer) {
      this.setVisibleIndex(this.originalLastVisibleIndex, true);
      return;
    }

    const splitId = event.container.id.split('-')
    if (!event.container.id.includes('emptySubjectPlaceholder')) {
      const containerId = Number(splitId[0].replace(/subject/g, ''));
      if (isNaN(containerId)) return;

      const droppedSession = this._complexBlock.sessions.find(s => s.id === containerId);
      if (droppedSession && droppedSession.sessionName !== "--") return;
    }

    const {
      id,
      classCount,
      periodCount,
      singlePeriodCount,
      doublePeriodCount
    } = this._complexBlock.subjectToYearGroups[0];
    const data = {
      classCount: classCount ?? 1,
      periodCount: periodCount ?? 1,
      singlePeriodCount: singlePeriodCount ?? 1,
      doublePeriodCount: doublePeriodCount ?? 0
    }
    const endOfRow = splitId[0] === this.addEmptySessionString().slice(0, -1) && !splitId[2];
    const endOfColumn = splitId[0] === this.addEmptySessionString().slice(0, -1);

    if (endOfRow) {
      data.periodCount = periodCount ? periodCount + 1 : 1
      data.singlePeriodCount = singlePeriodCount ? singlePeriodCount + 1 : 1
    } else if (endOfColumn) {
      data.classCount = classCount ? classCount + 1 : 1
    } else {
      const dropSessionId = +event.container.id.replace('subject', '');
      const object: { [key: string]: ICurriculumSessions[] } = {};
      this._complexBlock.sessions.forEach(session => {
        object[session.className]
          ? object[session.className].push(session)
          : object[session.className] = [session];
      })
      Object.values(object).forEach((sessions) => {
        const columnIndex = sessions.findIndex(session => session.id === dropSessionId);
        const sessionClassName = sessions.find(session => session.id === dropSessionId)?.className;

        if (columnIndex !== -1 && sessionClassName) {
          const sessionIds: number[] = [];
          Object.entries(object).forEach(([key, value]) => {
            if (key.split('-')[0] === sessionClassName.split('-')[0]) {
              sessionIds.push(value[columnIndex].id);
            }
          })
          this.emitAddSubject(event, sessionIds);
        }
      });
      return;
    }

    this.curriculumPlan.updateAnExistingSubjectToYearGroup(id, data)
      .pipe(
        switchMap(() => this.curriculumPlan.getBlock(this._complexBlock.id)))
      .subscribe(response => {
        if (endOfRow) {
          const lastElements: { [key: string]: number } = {};
          const responseSessions = response.sessions
            .sort((a, b) => a.id - b.id);
          responseSessions.forEach(item => {
            if (item.className.split('-')[0] === splitId[1]) {
              lastElements[item.className] = item.id;
            }
          });
          const sessionIdToAddSubject: number[] = Object.values(lastElements);
          if (sessionIdToAddSubject) {
            this.curriculumPlanBlocks.isShowLastSubjectBlockId$.next(this._complexBlock.id);
            this.emitAddSubject(event, sessionIdToAddSubject);
            this.setVisibleIndex(this.curriculumPlanBlocks.lastVisibleIndexForBlocks[this._complexBlock.id] + 1, true);
          }
        } else if (endOfColumn) {
          const sessionIdToAddSubject = response.sessions.filter(session => session.className.split('-')[0] === splitId[1])[+splitId[2]].id;
          this.emitAddSubject(event, [sessionIdToAddSubject]);
        }
      })
  }

  emitAddSubject(event: CdkDragDrop<any>, sessionIdToAddSubject: number[]): void {
    this.addSubjectToComplexBlock.emit({
      ...event.item.data,
      currentIndex: this._complexBlock.subjectToYearGroups.length,
      listIndex: this.listIndex,
      blockTypeId: this._complexBlock.blockTypeId,
      sessionIds: sessionIdToAddSubject
    });
  }

  dropToClass(event: CdkDragDrop<any>, index: number): void {
    if (!event.isPointerOverContainer || !this.block) {
      return;
    }
    const freeSessionId = this.block?.subjectToYearGroups[0].id;
    const sessionsToWorkWith = this.block.sessions
      .filter(session => session.classIndex === index && session.subjectToYearGroupId !== freeSessionId);
    let sessionIds: number[] = [];

    if (event.item.data.staffId) {
      sessionIds = sessionsToWorkWith
        .filter(session => !this.shiftKeyPressed
          ? !session.mainStaffId && !session.additionalStaffIds.includes(event.item.data.staffId)
          : session.mainStaffId !== event.item.data.staffId && !session.additionalStaffIds.includes(event.item.data.staffId))
        .map(session => session.id);

      if (!sessionIds.length) {
        this.snackbar.error(this.translate.instant('Staff already assigned to all the sessions.'))
        return
      }

      this.addStaffToBlock.emit({
        ...event.item.data,
        sessionIds,
        staffId: event.item.data.staffId,
        listIndex: this.listIndex,
        isAdditionalStaff: this.shiftKeyPressed
      });
    } else if (event.item.data.roomId) {
      sessionIds = sessionsToWorkWith
        .filter(session => !session.roomId)
        .map(session => session.id);

      if (!sessionIds.length) {
        this.snackbar.error(this.translate.instant('All sessions in this class has assigned room.'));
        return
      }

      this.addRoomToBlock.emit({
        ...event.item.data,
        sessionIds,
        roomId: event.item.data.roomId,
        listIndex: this.listIndex
      });
    }
  }

  dropStaff(event: CdkDragDrop<any>): void {
    const session = this._complexBlock.sessions.find(session => session.id.toString() === event.container.id.replace('staff', ''))
    if (session) {
      session.classRelatedSessionIds = [session.id]
      this.handleDropStaff(event, session);
    }
  }

  dropRoom(event: CdkDragDrop<any>) {
    const session = this._complexBlock.sessions.find(session => session.id.toString() === event.container.id.replace('room', ''))
    if (session) {
      session.classRelatedSessionIds = [session.id]
      this.handleDropRoom(event, session);
    }
  }

  override ngOnInit() {
    super.ngOnInit();

    this.onlyOneEmptyPlace = this.transformedSessions.length === 1 && this.transformedSessions[0].length === 1;

    this.lengthOfRow = this._complexBlock.sessions.filter(session => session.className === '1-C1').length;
    this.lengthOfRowWithNull = Array(this.lengthOfRow).fill(null)

    const isShowLastSubjectBlockId = this.curriculumPlanBlocks.isShowLastSubjectBlockId$.getValue();
    this.setVisibleIndex(this.curriculumPlanBlocks.lastVisibleIndexForBlocks[this._complexBlock.id]);
    if (isShowLastSubjectBlockId === this._complexBlock.id) {
      this.curriculumPlanBlocks.isShowLastSubjectBlockId$.next(null);
    }

    this.formatSessions();
    this.updateComplexBodyContainerWidth();
  }

  setVisibleIndex(value: number, isSubjectAddition = false): void {
    if (!isSubjectAddition && value > this.lengthOfRow) {
      value = this.lengthOfRow;
    }
    this.lastVisibleIndex = value;
    this.originalLastVisibleIndex = value;
    this.curriculumPlanBlocks.lastVisibleIndexForBlocks[this._complexBlock.id] = value;
  }

  override selectActiveSideBlock() {
    this.curriculumPlanBlocks.selectedBlockId$.next(this._complexBlock.id);
    this.curriculumPlanBlocks.scrollToSelectedBlockOnSidePanel$.next();
  }

  // Function to update the width
  private updateComplexBodyContainerWidth() {
    this.complexBodyContainerWidth = this.complexBodyContainer?.nativeElement?.offsetWidth;
  }

  private createDroppableSubjectIds(): string[] {
    const droppableSubjectIds = this.transformedSessions.map(sessionGroup => sessionGroup.map(session => session.classRelatedSessionIds.map(id => 'subject' + id))).flat(2);
    this.lengthOfRowWithNull.forEach((_, index) => {
      droppableSubjectIds.push(this.addEmptySessionString() + (this._complexBlockTransformedSessions.length + 1) + '-' + index)
    });
    const classNames = this.classNames.filter(className => className.includes(this.emptySubjectPlaceholderString))
    classNames.forEach((className) => {
      droppableSubjectIds.push(className)
    });
    return droppableSubjectIds;
  }

  private formatClassNames(): void {
    const allClassNames = Array.from(new Set(this._complexBlock.sessions.map(session => session.className)));

    this.classNames = allClassNames.flatMap((className, index, arr) => {
      const [rowIndex, name] = className.split('-');
      if (index === 0) {
        this.originalUniqueClassNames.push(className);
        return [this.addEmptySessionString() + '1', name]
      }
      if (index > 0 && rowIndex !== arr[index - 1].split('-')[0]) {
        this.originalUniqueClassNames.push('', className);
        return [this.addEmptySessionString() + rowIndex, name];
      }
      this.originalUniqueClassNames.push(className);
      return [name];
    });
  }

  addEmptySessionString(): string {
    return this._complexBlock.id + '*' + this.emptySubjectPlaceholderString
  }

  addToSelectedSubjectSessions(sessionId: number): void {
    let selectedSubjectSessionIds = this.selectedSubjectSessionIds$.getValue();

    if (selectedSubjectSessionIds.includes(sessionId)) {
      selectedSubjectSessionIds = selectedSubjectSessionIds.filter(id => id !== sessionId);
    } else {
      selectedSubjectSessionIds.push(sessionId);
    }

    this.selectedSubjectSessionIds$.next(selectedSubjectSessionIds);
    this.selectedSubjectSessionIds = selectedSubjectSessionIds;
  }
}
