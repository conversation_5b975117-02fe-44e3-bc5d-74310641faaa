import { Component } from '@angular/core';
import { GridApi, ICellRendererParams } from 'ag-grid-community';
import { ICheckForMissingStaffRoomActions } from '../../../../../../../_shared/models/ICheckForMissingStaffRoomActions';

@Component({
  selector: 'bromcom-check-for-missing-staff-room-actions',
  templateUrl: './check-for-missing-staff-room-actions.component.html',
  styleUrls: ['./check-for-missing-staff-room-actions.component.scss']
})
export class CheckForMissingStaffRoomActionsComponent {
  private gridApi!: GridApi;
  private params!: ICheckForMissingStaffRoomActions & ICellRendererParams;

  agInit(params: ICheckForMissingStaffRoomActions & ICellRendererParams): void {
    this.params = params;
    this.gridApi = params.api;
  }

  onViewRow(): void {
    this.params.onViewRow(this.params);
  }
}
