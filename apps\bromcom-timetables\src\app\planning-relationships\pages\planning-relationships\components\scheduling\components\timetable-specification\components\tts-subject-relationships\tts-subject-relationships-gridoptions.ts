import { IListOption, NOOP, transformToAGGridConfig } from '@bromcom/core';
import {
  AgGridNoDataComponent
} from '@bromcom/ui';
import {
  CheckboxSelectionCallbackParams, ICellEditorParams, ICellRendererParams,
  IsFullWidthRowParams
} from 'ag-grid-community';
import { TtsSubjectRelationshipsComponent } from './tts-subject-relationships.component';
import {
  AddNewRowComponent
} from '../../../../../../../../../projects/pages/new-project-wizard/components/add-new-row/add-new-row.component';
import { TtsGridActionsComponent } from '../tts-grid-actions/tts-grid-actions.component';
import {
  AgGridMultiselectDropdownComponent
} from '../ag-grid-multiselect-dropdown/ag-grid-multiselect-dropdown.component';
import {
  AgGridSubjectDropdownComponentComponent
} from '../ag-grid-subject-dropdown-component/ag-grid-subject-dropdown-component.component';
import { AgGridSelectDropdownComponent } from '../ag-grid-select-dropdown/ag-grid-select-dropdown.component';
import { SubjectCellRenderer } from '../../cell-renderers/subject-cell-renderer';
import { YearGroupsCellRenderer } from '../../cell-renderers/year-groups-cell-renderer';

export function gridOptions(this: TtsSubjectRelationshipsComponent, config: any) {
  const {
    onAddNewRow = NOOP,
    onAcceptNewRow = NOOP,
    onCancelAddingNewRow = NOOP,
    onEditRow = NOOP,
    onDeleteRow = NOOP,
    onAcceptRow = NOOP,
    onCancelEditRow = NOOP,
    onExcludeRow = NOOP,
    onIncludeRow = NOOP
  } = config

  return transformToAGGridConfig({
    getRowId: params => params.data.id,
    animateRows: false,
    suppressRowClickSelection: true,
    suppressClickEdit: true,
    suppressCellFocus: true,
    domLayout: 'autoHeight',
    editType: 'fullRow',
    rowSelection: 'multiple',
    noRowsOverlayComponent: AgGridNoDataComponent,
    noRowsOverlayComponentParams: {
      style: 'display: flex; justify-content: center; align-items: center; height: 100%; width: 100%; min-height: 162px; padding: 48px 0 0 0',
      noRowsMessageFunc: () => this.translate.instant(`No data available! No Subject Relationships were found.`)
    },
    pinnedBottomRowData: [{}],
    fullWidthCellRenderer: AddNewRowComponent,
    fullWidthCellRendererParams: {
      onAddNewRow,
      label: this.translate.instant('Add New Row')
    },
    tooltipShowDelay: 500,
    isFullWidthRow: (params: IsFullWidthRowParams) => {
      return !params.rowNode.data.id;
    },
    onSelectionChanged: () => {
      this.isRemoveBulkDisabled = !this.gridApi.getSelectedRows().length;
    },
    rowHeight: 56,
    columnDefs: [
      {
        minWidth: 48,
        width: 48,
        headerCheckboxSelection: true,
        checkboxSelection: (params: CheckboxSelectionCallbackParams<IListOption>) => {
          return !!params.data;
        }
      },
      {
        field: 'yearGroupIds',
        headerName: this.translate.instant('Year Group(s)'),
        minWidth: 120,
        flex: 1,
        editable: true,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: params => {
          return this.yearGroups.filter(yearGroup => params.data.yearGroupIds?.includes(yearGroup.id)).map(yearGroup => yearGroup.name).join(', ')
        },
        valueGetter: params => {
          return this.yearGroups.filter(yearGroup => params.data.yearGroupIds?.includes(yearGroup.id)).map(yearGroup => yearGroup.name).join(', ');
        },
        cellRenderer: YearGroupsCellRenderer,
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            yearGroups: this.yearGroups.filter(yearGroup => params.data.yearGroupIds?.includes(yearGroup.id)).map(yearGroup => yearGroup.name)
          }
        },
        cellEditor: AgGridMultiselectDropdownComponent,
        cellEditorParams: (params: ICellEditorParams) => {
          return {
            placeholder: this.translate.instant('Year Group(s)'),
            checkboxes: true,
            values: this.yearGroups,
            value: params.data.yearGroupIds,
            gridApi: this.gridApi,
            params
          }
        }
      },
      {
        field: 'firstSubjectId',
        headerName: this.translate.instant('Subject 1'),
        minWidth: 160,
        flex: 1,
        editable: true,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: params => this.subjectOptions.find(subject => subject.id === params.data.firstSubjectId)?.name,
        valueGetter: params => {
          const subject = this.subjectOptions.find(subject => subject.id === params.data.firstSubjectId);
          if (!subject) {
            return '';
          }
          return subject.code + ' - ' + subject.name
        },
        cellRenderer: SubjectCellRenderer,
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            subject: this.subjectOptions.find(subject => subject.id === params.data.firstSubjectId)
          }
        },
        cellEditor: AgGridSubjectDropdownComponentComponent,
        cellEditorParams: (params: ICellEditorParams) => {
          return {
            placeholder: this.translate.instant('Subject 1'),
            checkboxes: false,
            values: this.subjectOptions,
            value: params.data.firstSubjectId,
            gridApi: this.gridApi,
            params
          }
        }
      },
      {
        field: 'typeId',
        headerName: this.translate.instant('Rule'),
        minWidth: 150,
        flex: 1,
        editable: true,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: params => this.ruleOptions.find(rule => rule.id === params.data.typeId)?.name,
        valueGetter: params => {
          return this.ruleOptions.find(rule => rule.id === params.data.typeId)?.name;
        },
        cellEditor: AgGridSelectDropdownComponent,
        cellEditorParams: (params: ICellEditorParams) => {
          return {
            placeholder: this.translate.instant('Rule'),
            values: this.ruleOptions,
            value: params.data.typeId,
            gridApi: this.gridApi,
            params
          }
        },
        cellStyle: {
          fontWeight: '400',
          display: 'block',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }
      },
      {
        field: 'secondSubjectId',
        headerName: this.translate.instant('Subject 2'),
        minWidth: 150,
        flex: 1,
        editable: true,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: params => this.subjectOptions.find(subject => subject.id === params.data.secondSubjectId)?.name,
        valueGetter: params => {
          const subject = this.subjectOptions.find(subject => subject.id === params.data.secondSubjectId);
          if (!subject) {
            return '';
          }
          return subject.code + ' - ' + subject.name
        },
        cellRenderer: SubjectCellRenderer,
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            subject: this.subjectOptions.find(subject => subject.id === params.data.secondSubjectId)
          }
        },
        cellEditor: AgGridSubjectDropdownComponentComponent,
        cellEditorParams: (params: ICellEditorParams) => {
          return {
            placeholder: this.translate.instant('Subject 2'),
            checkboxes: false,
            values: this.subjectOptions,
            value: params.data.secondSubjectId,
            gridApi: this.gridApi,
            params
          }
        }
      },
      {
        field: 'actions',
        headerName: this.translate.instant('Actions'),
        minWidth: 128,
        filter: false,
        sortable: false,
        menuTabs: [],
        flex: 0.8,
        headerClass: 'text-center',
        cellStyle: { display: 'flex', justifyContent: 'center' },
        cellRenderer: TtsGridActionsComponent,
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            gridId: 'tts-ag-grid',
            type: 'subjectRelationship',
            onAcceptNewRow,
            onCancelAddingNewRow,
            onEditRow,
            onDeleteRow,
            onAcceptRow,
            onCancelEditRow,
            onExcludeRow,
            onIncludeRow
          }
        },
        editable: false
      }
    ]
  })
}
