@use '../../../../../../styles/expanded-block' as style;
@import '../../../../../../../../assets/styles/variables';

.expanded-simple-block-container {
  background-color: $color-blue-grey-100;
  min-height: 100%;

  .disabled {
    pointer-events: none;
    color: $color-blue-grey-300;
  }

  .actions {
    @include style.actions;
  }

  .error {
    @include style.error;
  }

}


@media screen and (max-width: 1279px) {
  .expanded-simple-block-container {
    .actions {
      flex-wrap: wrap;

      div {
        max-width: 138px !important;
      }
    }
  }
}
