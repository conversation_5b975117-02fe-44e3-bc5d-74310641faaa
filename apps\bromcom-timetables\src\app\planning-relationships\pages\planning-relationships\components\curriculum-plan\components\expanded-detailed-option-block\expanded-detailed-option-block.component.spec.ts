import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ExpandedDetailedOptionBlockComponent } from './expanded-detailed-option-block.component';

describe('ExpandedDetailedOptionBlockComponent', () => {
  let component: ExpandedDetailedOptionBlockComponent;
  let fixture: ComponentFixture<ExpandedDetailedOptionBlockComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ExpandedDetailedOptionBlockComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(ExpandedDetailedOptionBlockComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
