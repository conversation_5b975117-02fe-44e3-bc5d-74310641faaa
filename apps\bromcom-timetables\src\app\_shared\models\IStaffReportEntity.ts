export interface ITimetableAssignment {
  classCode?: string;
  room?: string;
  type: 'class' | 'PLN';
}

export interface IStaffReportEntity {
  staffId?: number;
  staffName?: string;
  teacherCode?: string;
  departmentId?: number;
  departmentName?: string;
  subjectId?: number;
  subjectName?: string;
  timetableAssignments?: {[key: string]: ITimetableAssignment | null};
}

export interface IPepiodSubjectReportEntity{
  periodId?: number;
  subjectId?: number;
}

export interface IReportPreview {
  staffReportEntities: IStaffReportEntity[],
  periodNames: {[key: number]: string;}
}
