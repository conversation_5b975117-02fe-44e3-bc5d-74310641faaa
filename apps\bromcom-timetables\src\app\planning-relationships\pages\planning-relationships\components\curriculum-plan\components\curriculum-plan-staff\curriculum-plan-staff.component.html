<div class="staff-container">
  <bromcom-input-field [formControl]="searchControl"
                       [icon]="'fal fa-search'"
                       [placeholder]="'Search' | translate"
  ></bromcom-input-field>

  <bromcom-list [formControl]="staffTypeControl"
                [data]="staffTypes"
                [fullWidth]="true"
                [clearable]="false"></bromcom-list>

  <div class="staff-actions">
    <bcm-button class="action-button" kind="ghost" icon="far fa-pen" (click)="onEditStaffs()">
      {{'Edit Staff' | translate }}
    </bcm-button>

    <bromcom-switch class="switch" [label]="'Staff for all Year Groups' | translate"
                    [formControl]="showAllSwitchControl"></bromcom-switch>
  </div>

  <div class="staff-header">
    <div [matMenuTriggerFor]="menu" class="drag-icon">
      <i class="far fa-filter"></i>
      <div *ngIf="isFilterApplied" class="dot"></div>
    </div>
    <div class="initials" (click)="sort('code')">
      {{'Code' | translate }}
      <i bromcomSortIcon [sortIcon]="sorting['code']" class="fa"></i>
    </div>
    <div class="name" (click)="sort('lastNameFirstName')">
      {{'Name' | translate }}
      <i bromcomSortIcon [sortIcon]="sorting['lastNameFirstName']" class="fa"></i>
    </div>
    <div class="subject" (click)="sort('subject')">
      {{'Subject' | translate }}
      <i bromcomSortIcon [sortIcon]="sorting['subject']" class="fa"></i>
    </div>
    <div class="assigned" (click)="sort('assigned')">
      {{'Assigned' | translate }}
      <i bromcomSortIcon [sortIcon]="sorting['assigned']" class="fa"></i>
    </div>
    <div class="total" (click)="sort('totalContactTime')">
      {{'Total' | translate }}
      <i bromcomSortIcon [sortIcon]="sorting['totalContactTime']" class="fa"></i>
    </div>
  </div>

  <div class="staff-cards-container"

       cdkDropList
       [cdkDropListData]="visibleData"
       [cdkDropListConnectedTo]="droppableStaffPlaceIds">
    <bcm-empty *ngIf="!visibleData.length && staffToSessionActive" class="no-data"
               icon="fad fa-folder-open">{{'No staff is available for the selected session.' | translate }}</bcm-empty>

    <bcm-empty *ngIf="!visibleData.length && !searchControl.value && !filterControl.value" class="no-data"
               icon="fad fa-folder-open">{{'No data available! Staff member not added.' | translate }}</bcm-empty>

    <bcm-empty *ngIf="!visibleData.length && (searchControl.value || filterControl.value)" class="no-data"
               icon="fad fa-folder-open">{{'Sorry no results found. Please amend your search criteria.' | translate }}</bcm-empty>

  <div *ngFor="let staff of visibleData" #viewport>
    <bromcom-staff-card [staff]="staff"
                        [showActions]="showActions"
                        [id]="staff.staffId.toString()"
                        [sumOfAllAssignedContactTimes]="sumOfAllAssignedContactTimes"
                        [warning]="this.staff.assigned > this.staff.total && this.sumOfAllAssignedContactTimes[this.staff.staffId] <= this.staff.totalContactTime"
                        [error]="this.staff.assigned > this.staff.total && this.sumOfAllAssignedContactTimes[this.staff.staffId] > this.staff.totalContactTime"
                        [success]="this.staff.assigned === this.staff.total"
                        (displayTimetable)="onDisplayTimetableClick(staff)"
                        (swapStaff)="onSwapStaff(staff)"
                        (substituteStaff)="onSubstituteStaff(staff)"></bromcom-staff-card>
  </div>
</div>

<mat-menu #menu="matMenu"
          class="filter-popup">
  <ng-template matMenuContent>
    <bromcom-input-field [formControl]="filterControl"
                         [icon]="'fal fa-search'"
                         [placeholder]="'Search' | translate"
                         (click)="$event.stopPropagation()"
    ></bromcom-input-field>

    <div (click)="$event.stopPropagation()"
         class="side-panel-filter-box" [formGroup]="filterGroup">
      <bromcom-checkbox [text]="'Code' | translate"
                        [formControlName]="'code'"></bromcom-checkbox>
      <bromcom-checkbox [text]="'Name' | translate"
                        [formControlName]="'lastNameFirstName'"></bromcom-checkbox>
      <bromcom-checkbox [text]="'Subject' | translate"
                        [formControlName]="'subject'"></bromcom-checkbox>
    </div>
  </ng-template>
</mat-menu>

