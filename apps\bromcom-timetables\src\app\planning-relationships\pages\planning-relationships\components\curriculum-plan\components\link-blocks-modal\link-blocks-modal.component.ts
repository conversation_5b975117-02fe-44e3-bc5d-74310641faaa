import { <PERSON>mponent, ElementRef, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { LinkBlocksService } from '../../../../../../services/link-blocks.service';
import { NestedTreeControl } from '@angular/cdk/tree';
import { MatTreeNestedDataSource } from '@angular/material/tree';
import { FormArray, FormBuilder, FormControl } from '@angular/forms';
import { Subject, switchMap, takeUntil } from 'rxjs';
import { ILinkBlocksTreeNode } from '../../../../../../../_shared/models/ILinkBlocksTreeNode';
import { ILinkBlocksListBlock, ILinkBlocksListData } from '../../../../../../../_shared/models/ILinkBlocksListData';
import { GeneralModalComponent } from '../../../../../../../_shared/components/general-modal/general-modal.component';
import { CurriculumPlanService } from '../../../../../../services/curriculum-plan.service';
import { ICurriculumSessions } from '../../../../../../../_shared/models/ICurriculumSessions';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';
import { isColorDark, SnackbarService } from '@bromcom/ui';
import { TranslateService } from '@ngx-translate/core';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import {
  ILinkBlocksInfo,
  ILinkBlocksInfoPeriods,
  ILinkCreateLinkData
} from '../../../../../../../_shared/models/ILinkBlocksInfo';
import { flatten } from 'rambda';
import { RelationshipsService } from '../../../../../../services/relationships.service';
import { ISubject } from '../../../../../../../_shared/models/ISubject';
import { BaseModalComponent } from '../../../../../../../_shared/components/BaseModalComponent';
import { PlanningRelationshipsService } from '../../../../../../services/planning-relationships.service';

@Component({
  selector: 'bromcom-link-blocks-modal',
  templateUrl: './link-blocks-modal.component.html',
  styleUrls: ['./link-blocks-modal.component.scss']
})
export class LinkBlocksModalComponent extends BaseModalComponent implements OnInit, OnDestroy {
  @ViewChild('linkBlocksModal') linkBlocksModal!: ElementRef;
  @ViewChild('linkBlockToWarningModal') linkBlockToWarningModal!: GeneralModalComponent;
  @ViewChild('conflictWillBeGeneratedWarningModal') conflictWillBeGeneratedWarningModal!: GeneralModalComponent;
  treeControl = new NestedTreeControl<ILinkBlocksTreeNode>(node => node.children);
  dataSource = new MatTreeNestedDataSource<ILinkBlocksTreeNode>();
  searchControl = new FormControl('');
  blockFromNameControl = new FormControl({ value: '', disabled: true });
  blockToNameControl = new FormControl({ value: '', disabled: true });

  blockId!: number;
  originalBlocksToLink: ILinkBlocksListData[] = [];
  blocksToLink: ILinkBlocksListData[] = [];
  selectedBlockId: number | null = null;
  step = 1;
  unscheduleLinkBlockToData: { sessionIds: number[], blockId: number } | null = null;
  readonly unsubscribe$: Subject<void> = new Subject();
  linkBlocksInfo!: ILinkBlocksInfo;
  matchingPeriodCountArray: FormArray = this.fb.array([]);
  excludedPeriods: ILinkBlocksInfoPeriods[] = [];
  disableLinkBlocks = false;
  subjects: ISubject[] = [];

  constructor(
    private linkBlocksService: LinkBlocksService,
    private curriculumPlanService: CurriculumPlanService,
    private curriculumPlanBlocksService: CurriculumPlanBlocksService,
    private relationshipService: RelationshipsService,
    private planningRelationShipsService: PlanningRelationshipsService,
    private snackbar: SnackbarService,
    private translate: TranslateService,
    private fb: FormBuilder
  ) {
    super();
  }

  ngOnInit(): void {
    this.searchControl.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(searchValue => {
        if (searchValue) {
          this.setFilteredData(searchValue);
        } else {
          this.dataSource.data = this.formatEntities(this.originalBlocksToLink);
        }
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  hasChild = (_: number, node: ILinkBlocksTreeNode) => !!node.children && node.children.length > 0;

  show(blockId: number): void {
    this.blockId = blockId;
    this.searchControl.reset(null);
    this.step = 1;
    this.subjects = this.relationshipService.subjectsData$.getValue();
    this.isOpen = true;

    this.linkBlocksService.getLinkBlocksTreeData(this.blockId)
      .subscribe((res: ILinkBlocksListData[]) => {
        const data = res.sort((a, b) => Number(b.yearGroupName) - Number(a.yearGroupName));
        this.dataSource.data = this.formatEntities(data);
        this.originalBlocksToLink = data;
        this.blocksToLink = data;
        this.selectedBlockId = null;
        this.linkBlocksModal.nativeElement.show();
        this.matchingPeriodCountArray = this.fb.array([]);
        this.excludedPeriods = [];
      })
  }

  setFilteredData(searchValue: string): void {
    const filtered = this.originalBlocksToLink
      .map(yearGroup => ({
        ...yearGroup,
        bands: yearGroup.bands.map(band => ({
          ...band,
          blocks: this.getMatchingBlocks(band, searchValue)
        })).filter(band => band.blocks.length > 0)
      }))
      .filter(yearGroup => yearGroup.bands.length > 0);

    const formatted = this.formatEntities(filtered);
    this.dataSource.data = formatted;
    this.treeControl.dataNodes = formatted;
    this.treeControl.dataNodes.forEach(yearGroupLevel => {
      this.treeControl.expand(yearGroupLevel);
      yearGroupLevel.children?.forEach(bandLevel => {
        this.treeControl.expand(bandLevel);
      })
    })
  }

  selectedBlockToLink(node: ILinkBlocksListBlock): void {
    this.selectedBlockId = this.selectedBlockId === node.blockId ? null : node.blockId;
  }

  linkBlocks(): void {
    if (!this.selectedBlockId) return

    this.curriculumPlanService.getBlock(this.selectedBlockId)
      .subscribe(block => {
        if (block.sessions.some(session => session.periodId)) {
          this.unscheduleLinkBlockToData = {
            blockId: block.id,
            sessionIds: block.sessions
              .filter((session: ICurriculumSessions) => !session.isLocked)
              .map((session: ICurriculumSessions) => session.id)
          };
          this.linkBlocksModal.nativeElement.hide();
          this.linkBlockToWarningModal.show();
        } else {
          this.getLinkBlocksInfo();
        }
      })
  }

  getLinkBlocksInfo(): void {
    if (this.selectedBlockId) {
      this.linkBlocksService.getLinkBlocksInfo(this.blockId, this.selectedBlockId)
        .subscribe(res => {
          this.blockFromNameControl.setValue(res.sourceBlock.yearGroupName + res.sourceBlock.bandNames.join('') + ' - ' + res.sourceBlock.blockName);
          this.blockToNameControl.setValue(res.destinationBlock.yearGroupName + res.destinationBlock.bandNames.join('') + ' - ' + res.destinationBlock.blockName);
          this.matchingPeriodCountArray.clear();

          this.linkBlocksInfo = res;
          const sourcePeriodCount = res.sourceBlock.periods.length;
          const destinationPeriodCount = res.destinationBlock.periods.length;
          for (let i = 0; i < sourcePeriodCount; i++) {
            this.matchingPeriodCountArray.push(new FormControl(false));
          }

          if (destinationPeriodCount > sourcePeriodCount) {
            this.linkBlocksInfo.destinationBlock.periods = this.linkBlocksInfo.destinationBlock.periods.filter((period, index) => {
              if (index + 1 === sourcePeriodCount && period.joinedPeriods.length === 2 && period.joinedPeriods[0] === period.periodIndex) {
                return true;
              } else if (index + 1 === sourcePeriodCount + 1 && period.joinedPeriods.length === 2 && period.joinedPeriods[0] === sourcePeriodCount) {
                return true;
              } else if (index >= sourcePeriodCount) {
                this.excludedPeriods.push(period);
                return false;
              }
              return true;
            })
          }

          this.checkForMatchingPeriods();
        })
    }
    this.isOpen = true;
    this.step = 2;
  }

  checkForMatchingPeriods(): void {
    for (let i = 0; i < this.linkBlocksInfo.sourceBlock.periods.length; i++) {
      const control = this.matchingPeriodCountArray.controls[i];
      const sourcePeriod = this.linkBlocksInfo.sourceBlock.periods[i];
      const sourcePeriodPlusLeft = this.linkBlocksInfo.sourceBlock.periods[i - 1];
      const sourcePeriodPlusRight = this.linkBlocksInfo.sourceBlock.periods[i + 1];
      const destinationPeriod = this.linkBlocksInfo.destinationBlock.periods[i];
      const destinationPeriodPlusLeft = this.linkBlocksInfo.destinationBlock.periods[i - 1];
      const destinationPeriodPlusRight = this.linkBlocksInfo.destinationBlock.periods[i + 1];

      // S - Single
      // D - Double
      // A - Actual
      if (!destinationPeriod) {
        control.setValue(null);
      } else if (sourcePeriod.joinedPeriods.length === 1) {
        if (destinationPeriod.joinedPeriods.length === 1) {
          // S
          // S
          control.setValue(true);
        } else if (sourcePeriodPlusRight && sourcePeriodPlusRight.joinedPeriods.length === 1 && destinationPeriod.joinedPeriods.length === 2 && destinationPeriod.joinedPeriods[0] === destinationPeriod.periodIndex) {
          // A S
          // D D
          control.setValue(true);
        } else if (sourcePeriodPlusLeft && sourcePeriodPlusLeft.joinedPeriods.length === 1 && destinationPeriod.joinedPeriods.length === 2 && destinationPeriod.joinedPeriods[1] === destinationPeriod.periodIndex) {
          // S A
          // D D
          control.setValue(true);
        } else if (sourcePeriodPlusLeft && sourcePeriodPlusLeft.joinedPeriods.length === 2 && destinationPeriod.joinedPeriods.length === 2 && destinationPeriod.joinedPeriods[1] === destinationPeriod.periodIndex) {
          // D D A
          // X D D
          control.setValue(false);
        } else if (sourcePeriodPlusRight && sourcePeriodPlusRight.joinedPeriods.length === 2 && destinationPeriod.joinedPeriods.length === 2 && destinationPeriod.joinedPeriods[0] === destinationPeriod.periodIndex) {
          // A D D
          // D D X
          control.setValue(false);
        } else if (!sourcePeriodPlusRight && destinationPeriod.joinedPeriods.length === 2 && destinationPeriod.joinedPeriods[0] === destinationPeriod.periodIndex) {
          // A
          // D D
          control.setValue(true);
        } else {
          control.setValue(false);
        }
      } else if (sourcePeriod.joinedPeriods.length === 2 && sourcePeriod.joinedPeriods[0] === sourcePeriod.periodIndex) {
        if (destinationPeriod.joinedPeriods.length === 1 && destinationPeriodPlusRight && destinationPeriodPlusRight.joinedPeriods.length === 1) {
          // A D
          // S S
          control.setValue(true);
        } else if (destinationPeriod.joinedPeriods.length === 1 && !destinationPeriodPlusRight) {
          // A D
          // S -
          control.setValue(true);
        } else if (destinationPeriod.joinedPeriods.length === 2 && destinationPeriod.joinedPeriods[0] === destinationPeriod.periodIndex) {
          // A D
          // D D
          control.setValue(true);
        } else if (destinationPeriod.joinedPeriods.length === 2 && destinationPeriodPlusRight?.joinedPeriods.length === 1) {
          // X A D
          // D D X
          control.setValue(false);
        } else {
          control.setValue(false);
        }
      } else if (sourcePeriod.joinedPeriods.length === 2 && sourcePeriod.joinedPeriods[1] === sourcePeriod.periodIndex) {
        if (destinationPeriod.joinedPeriods.length === 1 && destinationPeriodPlusLeft.joinedPeriods.length === 1) {
          // D A
          // S S
          control.setValue(true);
        } else if (destinationPeriod.joinedPeriods.length === 2 && destinationPeriod.joinedPeriods[1] === destinationPeriod.periodIndex) {
          // D A
          // D D
          control.setValue(true);
        } else {
          control.setValue(false);
        }
      }
    }
    this.setLinkBlocksDisability();
  }

  check(): boolean {
    let isMatching = false;
    for (let i = 0; i < this.linkBlocksInfo.sourceBlock.periods.length; i++) {
      const sourceBlockStaffIds = flatten(this.linkBlocksInfo.sourceBlock.periods[i].subjects
        .map(subject => subject.sessions
          .map(session => [session.mainStaffId, ...session.additionalStaffIds])))
        .filter(id => !!id);
      const sourceBlockRoomIds = flatten(this.linkBlocksInfo.sourceBlock.periods[i].subjects
        .map(subject => subject.sessions
          .map(session => session.roomId)))
        .filter(id => !!id);

      if (this.linkBlocksInfo.destinationBlock.periods[i]) {
        const destinationBlockStaffIds = flatten(this.linkBlocksInfo.destinationBlock.periods[i].subjects
          .map(subject => subject.sessions
            .map(session => [session.mainStaffId, ...session.additionalStaffIds])))
          .filter(id => !!id);
        const destinationBlockRoomIds = flatten(this.linkBlocksInfo.destinationBlock.periods[i].subjects
          .map(subject => subject.sessions
            .map(session => session.roomId)))
          .filter(id => !!id);

        const isStaffMatching = sourceBlockStaffIds.some(id => destinationBlockStaffIds.includes(id));
        const isRoomMatching = sourceBlockRoomIds.some(id => destinationBlockRoomIds.includes(id));
        isMatching = isMatching ? true : ![isStaffMatching, isRoomMatching].every(boolean => !boolean);
      }
    }
    return isMatching;
  }

  onOpenDragLinks(): void {
    if (!this.unscheduleLinkBlockToData) return

    this.curriculumPlanBlocksService.unscheduleSessions(this.unscheduleLinkBlockToData)
      .pipe(switchMap(() => this.curriculumPlanService.getBlock(this.unscheduleLinkBlockToData!.blockId)))
      .subscribe((block) => {
        this.snackbar.success(this.translate.instant('Block was unscheduled successfully.'));
        this.curriculumPlanBlocksService.stateChanged$.next();
        this.curriculumPlanBlocksService.blockSideStateChanged$.next();
        this.curriculumPlanService.currentSelectedBlock$.next(block);

        this.setLinkBlocksDisability();
        this.getLinkBlocksInfo();
        // setTimeout because closing this.linkBlockToWarningModal will close this one also if no time delay added... bcm-component
        setTimeout(() => this.linkBlocksModal.nativeElement.show(), 100)
      });
  }

  backToStepOne(): void {
    this.step = 1;
    this.isOpen = true;
    // setTimeout because closing this.linkBlockToWarningModal will close this one also if no time delay added... bcm-component
    setTimeout(() => this.linkBlocksModal.nativeElement.show(), 100)
  }

  backToStepTwo(): void {
    this.conflictWillBeGeneratedWarningModal.hide();
    this.isOpen = true;
    // setTimeout because closing this.linkBlockToWarningModal will close this one also if no time delay added... bcm-component
    setTimeout(() => this.linkBlocksModal.nativeElement.show(), 100)
  }

  setLinkBlocksDisability(): void {
    this.disableLinkBlocks = this.matchingPeriodCountArray.controls.some(control => control.value === false) ||
      this.linkBlocksInfo?.destinationBlock.periods.length === 0 ||
      this.linkBlocksInfo?.destinationBlock.periods.length > this.linkBlocksInfo?.sourceBlock.periods.length + 1 ||
      (this.linkBlocksInfo?.destinationBlock.periods.length > this.linkBlocksInfo?.sourceBlock.periods.length &&
        this.linkBlocksInfo?.destinationBlock.periods[this.linkBlocksInfo?.destinationBlock.periods.length - 1].joinedPeriods.length === 1)
  }

  private hasMatchingBlock(block: ILinkBlocksListData['bands'][0]['blocks'][0], searchValue: string): boolean {
    return block.blockName.toLowerCase().includes(searchValue.toLowerCase());
  }

  private getMatchingBlocks(band: ILinkBlocksListData['bands'][0], searchValue: string): { blockId: number; blockName: string; isDisabled: boolean }[] {
    return band.blocks.filter(block => this.hasMatchingBlock(block, searchValue));
  }

  private formatEntities(data: ILinkBlocksListData[]): ILinkBlocksTreeNode[] {
    return data.map(year => {
      return {
        name: year.yearGroupName,
        children: year.bands.map(band => {
          return {
            name: band.bandName,
            children: band.blocks.map(block => {
              return {
                ...block,
                name: block.blockName
              }
            })
          }
        })
      }
    })
  }

  isColorDarkHelper(color: string | undefined) {
    return color ? isColorDark(color) : false;
  }

  drop(event: CdkDragDrop<ILinkBlocksInfoPeriods[]>, array: ILinkBlocksInfoPeriods[]) {
    if (event.previousContainer === event.container) {
      const draggedElement = array[event.previousIndex];
      if (draggedElement.joinedPeriods.length === 2) {
        // Double period part
        if (event.previousIndex < event.currentIndex) {
          // Do it twice because of double periods 2 period
          if (this.linkBlocksInfo.destinationBlock.periods[event.currentIndex].joinedPeriods.length === 2) {
            // Change double with double
            moveItemInArray(event.container.data, event.previousIndex + 1, event.currentIndex + 1);
            moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
          } else {
            // Change single with double
            moveItemInArray(event.container.data, event.previousIndex + 1, event.currentIndex);
            moveItemInArray(event.container.data, event.previousIndex, event.currentIndex - 1);
          }
        } else {
          moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
          moveItemInArray(event.container.data, event.previousIndex + 1, event.currentIndex + 1);
        }
      } else {
        // Single period part
        let doubleCount = 0;
        let singleCount = 0;
        if (event.container.id === 'destinationBlocks') {
          // MOVEMENT IN BLOCK 2
          if (event.previousIndex < event.currentIndex) {
            for (let i = event.previousIndex + 1; i < event.currentIndex + 1; i++) {
              if (this.linkBlocksInfo.destinationBlock.periods[i].joinedPeriods.length === 2) {
                doubleCount = doubleCount + 1;
                i += 1;
              } else {
                singleCount = singleCount + 1;
              }
            }
            moveItemInArray(event.container.data,
              event.previousIndex,
              doubleCount ? event.previousIndex + (doubleCount * 2) + singleCount : event.currentIndex);
          } else {
            // for (let i = event.previousIndex - 1; i >= event.currentIndex; i--) {
            for (let i = event.previousIndex - 1; i >= event.currentIndex; i--) {
              if (this.linkBlocksInfo.destinationBlock.periods[i].joinedPeriods.length === 2) {
                doubleCount = doubleCount + 1;
                i -= 1;
              } else {
                singleCount = singleCount + 1;
              }
            }
            moveItemInArray(event.container.data,
              event.previousIndex,
              doubleCount ? event.previousIndex - (doubleCount * 2) - singleCount : event.currentIndex);
          }
        } else {
          // MOVEMENT IN BLOCK 1
          if (event.previousIndex < event.currentIndex) {
            for (let i = event.previousIndex + 1; i < event.currentIndex + 1; i++) {
              if (this.linkBlocksInfo.sourceBlock.periods[i].joinedPeriods.length === 2) {
                doubleCount = doubleCount + 1;
                i += 1;
              } else {
                singleCount = singleCount + 1;
              }
            }
            moveItemInArray(event.container.data,
              event.previousIndex,
              doubleCount ? event.previousIndex + (doubleCount * 2) + singleCount : event.currentIndex);
          } else {
            // for (let i = event.previousIndex - 1; i >= event.currentIndex; i--) {
            for (let i = event.previousIndex - 1; i >= event.currentIndex; i--) {
              if (this.linkBlocksInfo.sourceBlock.periods[i].joinedPeriods.length === 2) {
                doubleCount = doubleCount + 1;
                i -= 1;
              } else {
                singleCount = singleCount + 1;
              }
            }
            moveItemInArray(event.container.data,
              event.previousIndex,
              doubleCount ? event.previousIndex - (doubleCount * 2) - singleCount : event.currentIndex);
          }
        }
      }
    } else {
      const sourcePeriodCount = this.linkBlocksInfo.sourceBlock.periods.length;
      const destinationPeriodCount = this.linkBlocksInfo.destinationBlock.periods.length;

      if (event.container.id === 'destinationBlocks' && destinationPeriodCount + 1 > sourcePeriodCount) {
        this.snackbar.info(this.translate.instant('More period can not be added!'));
        return;
      }

      const draggedElement = event.container.id === 'destinationBlocks'
        ? this.excludedPeriods[event.previousIndex]
        : this.linkBlocksInfo.destinationBlock.periods[event.previousIndex]

      if (draggedElement.joinedPeriods.length === 2) {
        const leftSideArrayIndex = event.container.id === 'destinationBlocks'
          ? this.excludedPeriods.findIndex(period => period.periodIndex === draggedElement.joinedPeriods[0])
          : this.linkBlocksInfo.destinationBlock.periods.findIndex(period => period.periodIndex === draggedElement.joinedPeriods[0]);

        const rightSideArrayIndex = event.container.id === 'destinationBlocks'
          ? this.excludedPeriods.findIndex(period => period.periodIndex === draggedElement.joinedPeriods[1])
          : this.linkBlocksInfo.destinationBlock.periods.findIndex(period => period.periodIndex === draggedElement.joinedPeriods[1]);
        transferArrayItem(event.previousContainer.data, event.container.data, rightSideArrayIndex, event.currentIndex);
        transferArrayItem(event.previousContainer.data, event.container.data, leftSideArrayIndex, event.currentIndex);
      } else {
        transferArrayItem(
          event.previousContainer.data,
          event.container.data,
          event.previousIndex,
          event.currentIndex
        );
      }
    }

    setTimeout(() => {
      this.checkForMatchingPeriods()
    }, 300);
  }

  onCreateLinks(): void {
    const isConflict = this.check();

    if (isConflict) {
      this.conflictWillBeGeneratedWarningModal.show();
      return;
    }

    this.createLinks();
  }

  createLinks(): void {
    const data: ILinkCreateLinkData = {
      sourceBlockId: this.blockId,
      destinationBlockId: this.selectedBlockId!,
      linkedPeriods: [],
      excludedPeriods: this.excludedPeriods.map(period => period.periodIndex)
    }

    for (let i = 0; i < this.linkBlocksInfo.sourceBlock.periods.length; i++) {
      if (this.linkBlocksInfo.destinationBlock.periods[i]) {
        data.linkedPeriods.push({
          sourcePeriodIndex: this.linkBlocksInfo.sourceBlock.periods[i].periodIndex,
          destinationPeriodIndex: this.linkBlocksInfo.destinationBlock.periods[i].periodIndex
        })
      }
    }

    this.linkBlocksService.linkBlocks(data)
      .pipe(switchMap(() => this.curriculumPlanService.getBlock(this.blockId)))
      .subscribe((block) => {
        this.snackbar.success(this.translate.instant('Block was linked successfully.'));
        this.curriculumPlanBlocksService.stateChanged$.next();
        this.curriculumPlanBlocksService.blockSideStateChanged$.next();
        this.curriculumPlanService.currentSelectedBlock$.next(block);
      })
  }
}
