@import "apps/bromcom-timetables/src/assets/styles/variables";
@import "apps/bromcom-timetables/src/assets/styles/style-collector";

.subject-container {
  height: 100%;
  padding: 16px 0;

  .subject-header {
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    background: $color-blue-grey-100;
    border-radius: 8px;

    margin: 8px 0;
    padding: 4px 0;
    height: 40px;

    .drag-icon {
      position: relative;

      .dot {
        @include dot;
      }
    }

    .drag-icon {
      width: 40px;
      text-align: center;
    }

    .code {
      color: $color-blue-grey-700;
      font-weight: 500;
      text-align: center;
      width: 55px;
      cursor: pointer;

      i {
        position: absolute;
        margin-top: 3px;
        margin-left: 3px;
      }
    }

    .name, .short-name, .department {
      color: $color-blue-grey-700;
      font-weight: 500;
      width: 25%;
      padding: 0 0 0 8px;
      cursor: pointer;
    }

    .department {
      padding: 0;
    }

    .short-name {
      display: flex;
      align-items: center;
    }
  }

  .subject-cards-container {
    // 32px - paddings 16+16
    // 32px - search input
    // 32px - edit staff button
    // 50px - correction
    height: calc(100% - 32px - 32px - 32px - 50px);
    overflow: auto;

    .no-data {
      min-height: 378px;
    }
  }

  .subject-cards-container::-webkit-scrollbar {
    display: none;
  }
}
