<div class="expanded-simple-feedback-container">
  <div class="subject-code"
       [ngStyle]="{'background-color' : _block.subjectToYearGroups[0].subjectId | getSubjectColor: subjects}"
       [ngClass]="{'white-color': isColorDarkHelper(_block.subjectToYearGroups[0].subjectId | getSubjectColor: subjects)}">
    {{_block.subjectToYearGroups[0].subjectId | getSubjectCode: subjects}}
  </div>

  <div class="period-container">
    <div class="empty-square"></div>
    <div *ngFor="let period of _block.periods" class="period">P{{period}}</div>
  </div>

  <div class="class-row" *ngFor="let class of _block.classes, let classIndex = index">
    <div *ngFor="let session of groupedSessionByClass[classIndex], let sessionIndex = index">
      <bromcom-expanded-session [session]="session"
                                [sessionIndex]="sessionIndex"
                                [staffs]="staffs"
                                [rooms]="rooms"></bromcom-expanded-session>
    </div>
  </div>

  <div class="footer"></div>
</div>
