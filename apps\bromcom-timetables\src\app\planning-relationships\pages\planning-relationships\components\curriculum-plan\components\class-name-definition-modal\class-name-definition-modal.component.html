<ng-container *ngIf="isOpen">
  <bcm-modal #classNameDefinitionRef size="xxxlarge" backdrop class="class-name-modal"
             (bcm-modal-before-close)="onClose()"
  >
    <bcm-modal-header>{{'Class Name Definition' | translate}}</bcm-modal-header>

    <div class="modal-body">
      <div class="year-group-selector-row">
        <div class="list">
          <bromcom-list listType="select"
                        placeholder="{{'Default - XA/EN01' | translate }}"
                        [formControl]="selectedYearGroupControl"
                        [data]="yearGroupList"
                        [clearable]="false">
          </bromcom-list>
        </div>

        <bromcom-dropdown-menu
          [data]="yearOptionsMenu"
          id="schedule-action-list"
          [label]="'Copy Settings to' | translate"
        >
        </bromcom-dropdown-menu>
      </div>


      <div class="special-settings-row">
        <div class="radio-group-container">
          <span class="radio-group-title">{{'Subject' | translate}}:</span>
          <bcm-radio-group name="radiogroup" full-width #subjectRadioButtonGroup no-caption
                           (bcm-radio-change)="setExampleValue()">
            <bcm-radio
              *ngFor="let classNameItem of (classNameItems | getClassNameDefinitionItemsByType: CLASS_NAME_TYPE.SUBJECT_SETTING:[])"
              [checked]="classNameDefinitionByYearGroup && classNameDefinitionByYearGroup.subjectSetting === classNameItem.id"
              [value]="classNameItem.id">
              {{classNameItem.text}}
            </bcm-radio>
          </bcm-radio-group>
        </div>

        <div class="radio-group-container">
          <span class="radio-group-title">{{'Class Number' | translate}}:</span>

          <bcm-radio-group name="radiogroup" full-width #classNumberRadioButtonGroup no-caption
                           (bcm-radio-change)="setExampleValue()">
            <bcm-radio
              *ngFor="let classNameItem of (classNameItems | getClassNameDefinitionItemsByType: CLASS_NAME_TYPE.CLASS_NUMBER_SETTING:[])"
              [checked]="classNameDefinitionByYearGroup && classNameDefinitionByYearGroup.classNumberSetting === classNameItem.id"
              [value]="classNameItem.id">
              {{classNameItem.text}}
            </bcm-radio>
          </bcm-radio-group>
        </div>
      </div>

      <div class="draggable-row">
        <div class="optional-items">
          <div class="item-container-title">{{'Optional Items' | translate}}:</div>
          <div class="drag-item-container" *ngIf="classNameDefinitionByYearGroup?.classNameIds"
               cdkDropList
               cdkDropListOrientation="horizontal"
               [cdkDropListData]="(classNameItems | getClassNameDefinitionItemsByType: CLASS_NAME_TYPE.OPTIONAL_ITEM:classNameDefinitionByYearGroup.classNameIds)"
               [cdkDropListConnectedTo]="['4']"
               (cdkDropListDropped)="drop($event)"
               [id]="'2'">
            <div
              *ngFor="let classNameItem of (classNameItems | getClassNameDefinitionItemsByType: CLASS_NAME_TYPE.OPTIONAL_ITEM:classNameDefinitionByYearGroup.classNameIds)"
              class="item-card"
              cdkDrag
              [cdkDragData]="classNameItem"
              [style]="{
                        border: '1px solid ' + classNameItem.borderColor,
                        backgroundColor: classNameItem.backgroundColor
                      }">
              {{classNameItem.text}}

              <div *cdkDragPreview
                   [style]="{
                        border: '1px solid ' + classNameItem.borderColor,
                        backgroundColor: classNameItem.backgroundColor
                      }">
                <div class="item-card">{{classNameItem.text}}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="optional-separators">
          <div class="item-container-title">{{'Optional Separators' | translate}}:</div>
          <div class="drag-item-container"
               cdkDropList
               cdkDropListOrientation="horizontal"
               [cdkDropListData]="classNameItemsSeparator"
               [cdkDropListConnectedTo]="['4']"
               (cdkDropListDropped)="drop($event)"
               [id]="'3'">
            <div
              *ngFor="let classNameItem of classNameItemsSeparator"
              class="item-card-small"
              cdkDrag
              [cdkDragData]="classNameItem"
              [style]="{
                        minWidth: '34px',
                        width: '34px',
                        border: '1px solid #60A5FA',
                        backgroundColor: '#DBEAFE'
                      }">
              {{classNameItem.text}}

              <div *cdkDragPreview
                   [style]="{
                        minWidth: '34px',
                        width: '34px',
                        border: '1px solid #60A5FA',
                        backgroundColor: '#DBEAFE'
                      }">
                <div class="item-card-small">{{classNameItem.text}}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="core-items">
          <div class="item-container-title">{{'Core Items' | translate}}:</div>
          <div class="drag-item-container" *ngIf="classNameDefinitionByYearGroup?.classNameIds"
               cdkDropList
               cdkDropListOrientation="horizontal"
               [cdkDropListData]="(classNameItems | getClassNameDefinitionItemsByType: CLASS_NAME_TYPE.CORE_ITEMS:classNameDefinitionByYearGroup.classNameIds)"
               [cdkDropListConnectedTo]="['4']"
               (cdkDropListDropped)="drop($event)"
               [id]="'1'">
            <div
              *ngFor="let classNameItem of (classNameItems | getClassNameDefinitionItemsByType: CLASS_NAME_TYPE.CORE_ITEMS:classNameDefinitionByYearGroup.classNameIds)"
              class="item-card"
              cdkDrag
              [cdkDragData]="classNameItem"
              [style]="{
                        border: '1px solid ' + classNameItem.borderColor,
                        backgroundColor: classNameItem.backgroundColor
                      }">
              {{classNameItem.text }}

              <div *cdkDragPreview
                   [style]="{
                        border: '1px solid ' + classNameItem.borderColor,
                        backgroundColor: classNameItem.backgroundColor
                      }">
                <div class="item-card">{{classNameItem.text}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="droppable-row">
        <div class="drop-item-container-title">{{'Drag Items here to create Class Definition' | translate}}:</div>
        <div class="drop-area" *ngIf="classNameDefinitionByYearGroup?.classNameIds"
             cdkDropList
             cdkDropListOrientation="horizontal"
             [cdkDropListData]="classNameItemsResult"
             [cdkDropListConnectedTo]="['2', '3', '1']"
             (cdkDropListDropped)="drop($event)"
             [id]="'4'">
          <div *ngFor="let classNameItem of classNameItemsResult"
               cdkDrag
               [cdkDragData]="classNameItem"
               [style]="{
                        border: '1px solid ' + (classNameItem.borderColor ? classNameItem.borderColor : '#60A5FA'),
                        backgroundColor: classNameItem.backgroundColor ? classNameItem.backgroundColor : '#DBEAFE',
                        minWidth: classNameItem.type === CLASS_NAME_TYPE.OPTIONAL_SEPARATOR ? '34px' : '110px',
                        width: classNameItem.type === CLASS_NAME_TYPE.OPTIONAL_SEPARATOR ? '34px' : '110px'
                        }"
               [ngClass]="{
                        'item-card': classNameItem.type !== CLASS_NAME_TYPE.OPTIONAL_SEPARATOR,
                        'item-card-small': classNameItem.type === CLASS_NAME_TYPE.OPTIONAL_SEPARATOR
                        }"
          >
            {{classNameItem.text}}
            <span *ngIf="classNameItem.id === 5">{{'&#x1F512;'}}</span>

            <div *cdkDragPreview
                 [style]="{
                        border: '1px solid ' + (classNameItem.borderColor ? classNameItem.borderColor : '#60A5FA'),
                        backgroundColor: classNameItem.backgroundColor ? classNameItem.backgroundColor : '#DBEAFE',
                        minWidth: classNameItem.type === CLASS_NAME_TYPE.OPTIONAL_SEPARATOR ? '34px' : '110px',
                        width: classNameItem.type === CLASS_NAME_TYPE.OPTIONAL_SEPARATOR ? '34px' : '110px'
                      }">
              <div [ngClass]="{
              'item-card': classNameItem.type !== CLASS_NAME_TYPE.OPTIONAL_SEPARATOR,
              'item-card-small': classNameItem.type === CLASS_NAME_TYPE.OPTIONAL_SEPARATOR
              }">{{classNameItem.text}}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="check-row">
        <div class="check-row-example-container">
          <div class="check-row-example-title">{{'Class Name Example' | translate}}</div>
          <bromcom-input-field [formControl]="classNameExampleControl" [readonly]="true"
                               [ngClass]="{'green': isDuplicatesChecked && !isDuplicatesFound, 'red': isDuplicatesChecked && isDuplicatesFound}"></bromcom-input-field>
          <div class="test-your-code-title"
               *ngIf="!isDuplicatesChecked">{{'Test your code for possible duplications.' | translate}}</div>
          <div class="test-your-code-title red"
               *ngIf="isDuplicatesChecked && isDuplicatesFound">{{'This combination causes duplicate class codes.' | translate}}</div>
          <div class="test-your-code-title green"
               *ngIf="isDuplicatesChecked && !isDuplicatesFound">{{'This combination does not create duplicates.' | translate}}</div>
        </div>

        <bcm-button class="check-button" (click)="onCheck()">{{ 'Check' | translate }}</bcm-button>
      </div>
    </div>

    <bcm-modal-footer>
      <bcm-button kind="ghost" data-dismiss
                  (click)="onCloseClassNameDefinition()">{{'Cancel' | translate }}</bcm-button>
      <bcm-button icon="far fa-save" (click)="onSaveClassNameDefinition()">{{'Save' | translate }}</bcm-button>
    </bcm-modal-footer>
  </bcm-modal>
</ng-container>
