import { ICellRendererComp, ICellRendererParams } from 'ag-grid-community';

export class StaffCellRenderer implements ICellRendererComp {
  eGui: HTMLSpanElement;
  value: any;
  staffList!: string[];

  constructor() {
    this.eGui = document.createElement('div');
  }

  init(params: any) {
    this.value = params.value;
    this.staffList = params.staffList;
    this.updateStaffList();
  }

  updateStaffList(): void {
    this.eGui.style.width = '100%';
    let innerHTML = ''
    this.staffList.forEach((staff) => {
      const element = `<bcm-chip color="blue" style="margin-right: 8px">${staff}</bcm-chip>`
      innerHTML += element;
    });
    this.eGui.innerHTML = `<div style="width: 100%; display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">${innerHTML}</div>`;
  }

  getGui() {
    return this.eGui;
  }

  refresh(params: ICellRendererParams) {
    this.value = params.value;

    this.eGui.innerHTML = '';
    this.updateStaffList();

    return true;
  }
}
