import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TtsSubjectOnDaysComponent } from './tts-subject-on-days.component';

describe('TtsSubjectOnDaysComponent', () => {
  let component: TtsSubjectOnDaysComponent;
  let fixture: ComponentFixture<TtsSubjectOnDaysComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ TtsSubjectOnDaysComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(TtsSubjectOnDaysComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
