<div class="room-container">
  <bromcom-input-field [formControl]="searchControl"
                       [icon]="'fal fa-search'"
                       [placeholder]="'Search' | translate"
  ></bromcom-input-field>

  <bcm-button class="action-button" kind="ghost" icon="far fa-pen" (click)="onEditRooms()">
    {{'Edit Rooms' | translate }}
  </bcm-button>

  <div class="room-header">
    <div [matMenuTriggerFor]="menu" class="drag-icon">
      <i class="far fa-filter"></i>
      <div *ngIf="isFilterApplied" class="dot"></div>
    </div>
    <div class="code" (click)="sort('code')">
      {{'Code' | translate }}
      <i bromcomSortIcon [sortIcon]="sorting['code']" class="fa"></i>
    </div>
    <div class="teacher" (click)="sort('teacher')">
      {{'Teacher' | translate }}
      <i bromcomSortIcon [sortIcon]="sorting['teacher']" class="fa"></i>
    </div>
    <div class="type" (click)="sort('type')">
      {{'Type' | translate }}
      <i bromcomSortIcon [sortIcon]="sorting['type']" class="fa"></i>
    </div>
    <div class="site" (click)="sort('site')">
      {{'Site' | translate }}
      <i bromcomSortIcon [sortIcon]="sorting['site']" class="fa"></i>
    </div>
    <div class="contactTime" (click)="sort('assignedContactTime')">
      {{'Total' | translate }}
      <i bromcomSortIcon [sortIcon]="sorting['assignedContactTime']" class="fa"></i>
    </div>
  </div>

  <div class="room-cards-container"
       cdkDropList
       [cdkDropListData]="visibleData"
       [cdkDropListConnectedTo]="droppableRoomPlaceIds">
    <bcm-empty *ngIf="!visibleData.length && roomToSessionActive" class="no-data"
               icon="fad fa-folder-open">{{'No room is available for the selected session.' | translate }}</bcm-empty>

    <bcm-empty *ngIf="!visibleData.length && !searchControl.value && !filterControl.value" class="no-data"
               icon="fad fa-folder-open">{{'No data available! Room not added.' | translate }}</bcm-empty>

    <bcm-empty *ngIf="!visibleData.length && (searchControl.value || filterControl.value)" class="no-data"
               icon="fad fa-folder-open">{{'Sorry no results found. Please amend your search criteria.' | translate }}</bcm-empty>

  <div *ngFor="let room of visibleData" #viewport>
    <bromcom-room-card [room]="room" [id]="room.id.toString()" [showActions]="showActions" (displayTimetable)="onDisplayTimetableClick(room)" (swapRoom)="onSwapRoom(room)"></bromcom-room-card>
  </div>
</div>

<mat-menu
  #menu="matMenu"
  class="filter-popup">
  <ng-template matMenuContent>
    <bromcom-input-field [formControl]="filterControl"
                         [icon]="'fal fa-search'"
                         [placeholder]="'Search' | translate"
                         (click)="$event.stopPropagation()"
    ></bromcom-input-field>

    <div (click)="$event.stopPropagation()"
         class="side-panel-filter-box" [formGroup]="filterGroup">
      <bromcom-checkbox [text]="'Code' | translate"
                        [formControlName]="'code'"></bromcom-checkbox>
      <bromcom-checkbox [text]="'Teacher' | translate"
                        [formControlName]="'teacher'"></bromcom-checkbox>
      <bromcom-checkbox [text]="'Type' | translate"
                        [formControlName]="'type'"></bromcom-checkbox>
      <bromcom-checkbox [text]="'Site' | translate"
                        [formControlName]="'site'"></bromcom-checkbox>
    </div>
  </ng-template>
</mat-menu>
