import { customComparator, IListOption, transformToAGGridConfig } from '@bromcom/core';
import { AgGridNoDataComponent } from '@bromcom/ui';
import {
  CheckboxSelectionCallbackParams,
  EditableCallbackParams,
  IRowNode,
  ITooltipParams,
  RowSelectedEvent
} from 'ag-grid-community';
import { SwapDataModalComponent } from './swap-data-modal.component';

export function roomGridOptions(this: SwapDataModalComponent) {

  const editable = (params: EditableCallbackParams | IRowNode | CheckboxSelectionCallbackParams) => !(params.data && params.data?.hasConflict);

  return transformToAGGridConfig({
    rowSelection: 'single',
    isRowSelectable: (params) => editable(params),
    noRowsOverlayComponent: AgGridNoDataComponent,
    noRowsOverlayComponentParams: {
      style: 'display: flex; justify-content: center; align-items: center; height: 100%; width: 100%; min-height: 162px; padding: 48px 0 0 0',
      noRowsMessageFunc: () => this.translate.instant(`No data available!`)
    },
    onRowSelected: (event: RowSelectedEvent) => {
      if (!event.node.isSelected() && !this.gridApi.getSelectedRows().length) {
        event.node.setSelected(true);
        return
      }
      this.selectedRoomData = this.gridApi.getSelectedRows().length ? this.gridApi.getSelectedRows()[0] : null;
    },
    tooltipShowDelay: 100,
    columnDefs: [
      {
        width: 48,
        headerCheckboxSelection: false,
        showDisabledCheckboxes: true,
        checkboxSelection: (params: CheckboxSelectionCallbackParams<IListOption>) => {
          return !!params.data && editable(params);
        },
        cellClass: (params) => params.node.data.hasConflict ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params)
      },
      {
        field: 'name',
        headerName: this.translate.instant('Name'),
        wrapHeaderText: true,
        minWidth: 130,
        editable: false,
        flex: 1,
        filter: false,
        comparator: customComparator,
        cellClass: (params) => params.node.data.hasConflict ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params),
        menuTabs: []
      },
      {
        field: 'code',
        headerName: this.translate.instant('Code'),
        wrapHeaderText: true,
        minWidth: 110,
        editable: false,
        flex: 1,
        filter: false,
        comparator: customComparator,
        cellClass: (params) => params.node.data.hasConflict ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params),
        sort: 'asc',
        menuTabs: []
      },
      {
        field: 'type',
        headerName: this.translate.instant('Type'),
        wrapHeaderText: true,
        minWidth: 110,
        editable: false,
        flex: 1.3,
        comparator: customComparator,
        cellClass: (params) => params.node.data.hasConflict ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params)
      },
      {
        field: 'site',
        headerName: this.translate.instant('Site'),
        wrapHeaderText: true,
        minWidth: 110,
        editable: false,
        flex: 1.1,
        comparator: customComparator,
        cellClass: (params) => params.node.data.hasConflict ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params),
        sort: 'asc'
      },
      {
        field: 'contactTime',
        headerName: this.translate.instant('Available'),
        minWidth: 110,
        editable: false,
        flex: 1.3,
        filter: false,
        comparator: customComparator,
        cellClass: (params) => params.node.data.hasConflict ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params),
        sort: 'asc',
        menuTabs: []
      },
      {
        field: 'assigned',
        headerName: this.translate.instant('Assigned'),
        minWidth: 110,
        editable: false,
        flex: 1.3,
        filter: false,
        comparator: customComparator,
        cellClass: (params) => params.node.data.hasConflict ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params),
        sort: 'asc',
        menuTabs: []
      },
      {
        field: 'remaining',
        headerName: this.translate.instant('Remaining'),
        minWidth: 140,
        editable: false,
        flex: 1.4,
        filter: false,
        comparator: customComparator,
        cellClass: (params) => params.node.data.hasConflict ? 'disabled' : undefined,
        tooltipValueGetter: (params: ITooltipParams) => tooltipValueGetter.call(this, params),
        sort: 'asc',
        menuTabs: []
      }
    ]
  })
}

function tooltipValueGetter(this: SwapDataModalComponent, params: ITooltipParams) {
  if (params.data?.hasConflict) {
    return this.translate.instant('Swap not available due to the room\'s conflicts.');
  } else if (params.value && params.value.length > 5) {
    return params.value;
  } else {
    return;
  }
}
