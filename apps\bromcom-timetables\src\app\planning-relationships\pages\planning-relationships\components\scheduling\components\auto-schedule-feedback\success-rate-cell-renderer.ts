import { ICellRendererComp, ICellRendererParams } from 'ag-grid-community';

export class SuccessRateCellRenderer implements ICellRendererComp {
  eGui: HTMLSpanElement;
  successRate!: number;

  constructor() {
    this.eGui = document.createElement('div');
  }

  init(params: ICellRendererParams) {
    this.successRate = params.data.sessionsTotal === 0 ? 0 : Math.round(params.data.scheduledPeriodsCount / params.data.sessionsTotal * 100);
    this.updateSubject();
  }

  updateSubject(): void {
    this.eGui.style.display = 'flex';
    this.eGui.style.alignItems = 'center';
    this.eGui.style.width = '100%';
    const progressColor = this.successRate === 100 ? 'emerald' : 'blue';
    this.eGui.innerHTML = `
            <bcm-progress type="line-rounded" value="${this.successRate}" color="${progressColor}"></bcm-progress>
    <div style="margin-left: 16px">${this.successRate}%</div>
            `;
  }

  getGui() {
    return this.eGui;
  }

  refresh(params: ICellRendererParams) {
    this.successRate = params.data.sessionsTotal === 0 ? 0 : Math.round(params.data.scheduledPeriodsCount / params.data.sessionsTotal * 100);
    this.eGui.innerHTML = '';
    this.updateSubject();

    return true;
  }
}
