import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { ISubjectToYearGroup } from "../../../../../../../_shared/models/ISubjectToYearGroup";
import { ISubject } from "../../../../../../../_shared/models/ISubject";
import {
  IRemoveSubjectToYearGroupFromGroupEvent
} from "../../../../../../../_shared/models/IRemoveSubjectToYearGroupFromGroupEvent";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SubjectInformationService } from "../../../../../../services/subject-information.service";
import { IUpdateSubjectToYearGroupEvent } from "../../../../../../../_shared/models/IUpdateSubjectToYearGroupEvent";
import { Subject, debounceTime, takeUntil } from "rxjs";
import { DRAG_AND_DROP_GROUP_TYPE } from "../../../../../../../_shared/enums/DragAndDropGroupType";
import { SnackbarService } from '@bromcom/ui';
import { TranslateService } from '@ngx-translate/core';
import { IListOfBlocksResponse } from '../../../../../../../_shared/models/IListOfBlocksResponse';
import { BLOCK_TYPE } from '../../../../../../../_shared/enums/BlockType';

@Component({
  selector: 'bromcom-group-box-drag',
  templateUrl: './drag-and-drop-dragbox.component.html',
  styleUrls: ['./drag-and-drop-dragbox.component.scss']
})
export class DragAndDropDragboxComponent implements OnInit, OnDestroy {
  @Input() _item!: ISubjectToYearGroup;
  @Input() subjects!: ISubject[];

  @Input() set item(value: ISubjectToYearGroup) {
    this._item = value;
    this.formGroup.controls.classCount.setValue(value.classCount.toString(), { emitEvent: false, onlySelf: true });
    this.formGroup.controls.periodCount.setValue(value.periodCount.toString(), { emitEvent: false, onlySelf: true });
  }

  @Input() updateClassAndPeriodCount$!: Subject<void>;
  @Input() index!: number;
  @Input() type!: DRAG_AND_DROP_GROUP_TYPE;
  @Input() blockIndex!: number;
  @Input() data!: IListOfBlocksResponse;
  @Output() removeEvent = new EventEmitter<IRemoveSubjectToYearGroupFromGroupEvent>();
  @Output() updateSubjectToYearGroupEvent = new EventEmitter<IUpdateSubjectToYearGroupEvent>();
  @Output() updateSubjectInfoEvent = new EventEmitter<void>();

  active = false;
  formGroup = new FormGroup({
    classCount: new FormControl('', Validators.required),
    periodCount: new FormControl('', Validators.required)
  });
  latestPayload!: ISubjectToYearGroup;
  private readonly unsubscribe$: Subject<void> = new Subject();

  constructor(private subjectInformationService: SubjectInformationService,
              private snackbar: SnackbarService,
              private translate: TranslateService) {
  }

  ngOnInit(): void {
    this.latestPayload = this._item;
    this.updateClassAndPeriodCount$.pipe(takeUntil(this.unsubscribe$))
      .subscribe(() => {
        if (this.type === DRAG_AND_DROP_GROUP_TYPE.Linear) {
          this.formGroup.controls.classCount.setValue(this._item.classCount.toString(), {
            emitEvent: false,
            onlySelf: true
          });
        } else {
          this.formGroup.controls.periodCount.setValue(this._item.periodCount.toString(), {
            emitEvent: false,
            onlySelf: true
          });
        }
      })
    if (this.type === DRAG_AND_DROP_GROUP_TYPE.Linear) {
      this.formGroup.controls.classCount.disable({ onlySelf: true });
    }
    this.formGroup.updateValueAndValidity();
    this.formGroup.valueChanges
      .pipe(
        debounceTime(300),
        takeUntil(this.unsubscribe$))
      .subscribe(val => {
        if (this.formGroup.controls.periodCount.value && this.formGroup.controls.classCount.value) {
          const changedId = this.data.subjectToYearGroups[this.index].id;
          const allNotSame = this.data.subjectToYearGroups.filter(el => el.id != changedId).some(item => changedId !== item.id && item.periodCount !== +val.periodCount!);
          const payload = {
            subject: {
              ...this._item,
              periodCount: val.periodCount && +val.periodCount > 0 ? +val.periodCount : 1,
              classCount: val.classCount && +val.classCount > 0 ? +val.classCount : 1,
              convertToComplexBlock: val.periodCount && +val.periodCount !== this.latestPayload.periodCount && allNotSame ? true : false,
            },
            type: this.type,
            index: this.blockIndex
          };
          if (val.classCount && +val.classCount == 0) {
            this.formGroup.controls.classCount.setValue('1', { onlySelf: true });
          }
          if (val.periodCount && +val.periodCount == 0) {
            this.formGroup.controls.periodCount.setValue('1', { onlySelf: true });
          }
          this.formGroup.updateValueAndValidity({ emitEvent: false, onlySelf: true });
          this.subjectInformationService.updateSubjectToYearGroup(payload.subject).subscribe(() => {
            this.updateSubjectInfoEvent.emit();
          });
          this.updateSubjectToYearGroupEvent.emit(payload);

          if (this.data.blockTypeId === BLOCK_TYPE.Complex || this.data.blockTypeId === BLOCK_TYPE.Options) {
            if (allNotSame) {
              this.data.blockTypeId = BLOCK_TYPE.Complex;
            }
            else {
              this.data.blockTypeId = BLOCK_TYPE.Options;
            }
          }
          if ((this.latestPayload.classCount !== payload.subject.classCount) || (this.latestPayload.periodCount !== payload.subject.periodCount)) {
            this.snackbar.success(this.translate.instant('Updated successfully'));
          }
          this.latestPayload = payload.subject;
        }
      })
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  removeSubjectToYearGroup(subject: ISubjectToYearGroup): void {
    this.removeEvent.emit({ subject });
  }

  getColor(color: string): string {
    return `#${color}`;
  }

  getSubject(subjectId: number): ISubject | undefined {
    return this.subjects.find(subject => subject.id === subjectId);
  }

  getBoxText(): string {
    const subjectId = this._item.subjectId;
    const subject = this.getSubject(subjectId);
    return `${subject?.code.toUpperCase()} - ${subject?.name}`;
  }

  onClassCountInputBlur() {
    setTimeout(() => {
      if (!this.formGroup.controls.classCount.value) {
        this.formGroup.controls.classCount.setValue('1', { emitEvent: false, onlySelf: true });
        if (this.formGroup.controls.classCount.value !== this.latestPayload.classCount.toString()) {
          this.snackbar.success(this.translate.instant('Updated successfully'));
        }
        this.latestPayload.classCount = 1;
      } else if (this.formGroup.controls.classCount.value && this.formGroup.controls.classCount.value !== this.latestPayload.classCount.toString()) {
        this.snackbar.success(this.translate.instant('Updated successfully'));
      }
    }, 500);
  }

  onPeriodCountInputBlur() {
    setTimeout(() => {
      if (!this.formGroup.controls.periodCount.value) {
        this.formGroup.controls.periodCount.setValue('1', { emitEvent: false, onlySelf: true });
        if (this.formGroup.controls.periodCount.value !== this.latestPayload.periodCount.toString()) {
          this.snackbar.success(this.translate.instant('Updated successfully'));
        }
        this.latestPayload.periodCount = 1;
      } else if (this.formGroup.controls.periodCount.value && this.formGroup.controls.periodCount.value !== this.latestPayload.periodCount.toString()) {
        this.snackbar.success(this.translate.instant('Updated successfully'));
      }
    }, 500);
  }
}
