@import 'apps/bromcom-timetables/src/assets/styles/variables';

.timetable-specification-modal {
  &.bcm-modal__show {
    z-index: 5;
  }

  .modal-body {
    padding: 24px;

    ::ng-deep .mat-mdc-tab-ripple {
      border-bottom: 2px solid $color-blue-grey-500;
    }

    ::ng-deep .mdc-tab__ripple::before,
    ::ng-deep .mat-mdc-tab .mat-ripple-element {
      background-color: $color-white-0;
    }

    .mat-mdc-tab-header {
      margin-bottom: 16px;
    }

    ::ng-deep .mat-mdc-tab &:not(.mat-mdc-tab--active) .mdc-tab__text-label {
      color: $color-blue-grey-500 !important;
      font-weight: 500;
      font-family: 'Inter', serif;
    }

    ::ng-deep.mat-mdc-tab:not(.mat-mdc-tab-disabled).mdc-tab--active .mdc-tab__text-label,
    ::ng-deep .mat-mdc-tab:not(.mat-mdc-tab-disabled) .mdc-tab-indicator__content--underline {
      color: $color-primary-blue-600 !important;
      border-color: $color-primary-blue-600 !important;
    }
  }
}
