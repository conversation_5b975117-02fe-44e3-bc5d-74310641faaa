@import '../../../../../../../../assets/styles/variables';

.overview {
  z-index: 10;
}

.overview .modal-body {
  padding: 24px;
}

.action-bar_ {
  display: flex;
  flex-wrap: wrap;

  .block {
    width: 100%;
    display: flex;
  }

  bromcom-filter-list-chips {
    position: relative;
    display: inline-block;
    width: 256px;
    margin: 0 10px;
    height: 64px;
  }

  .action-buttons_,
  .action-button-block_ {
    display: inline-block;
    top: 23px;
    position: relative;

    > bcm-button {
      display: inline-block;
      position: relative;
      top: 7px;
    }

    > bcm-button-group {
      display: inline-flex;
    }

    ::ng-deep .mat-mdc-chip {
      padding: 0 8px;
    }
  }

  .action-button-block_ {
    margin-right: 10px;
    top: 17px;
  }

  .export-button {
    margin: 24px 0 0 8px;
  }
}

.overview-container {
  .table-container {
    margin: 10px;
    overflow-x: auto;
    width: 97%;

    table {
      border: 1px solid $color-blue-grey-200;
      border-radius: 4px;
      border-spacing: 0;
      border-collapse: separate;

      tr {
        td {
          min-width: 55px;
          min-height: 24px;
          max-height: 24px;
          border: 1px solid $color-blue-grey-200;
          color: $color-blue-grey-600;
          text-align: center;

          &.yeargroup-header {
            min-width: 25px;
            overflow: hidden;
          }
        }
      }
    }
  }
}

.white-color {
  color: $color-white-0 !important;
}

::ng-deep .bcm-modal__container.xxlarge {
  max-width: unset;
}

#departmentsFilter,
#yearGroupFilter {
  ::ng-deep .mdc-evolution-chip {
    padding: 0 10px;
    max-width: max-content;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.count-span {
  background-color: #475569;
  color: #f3f4f6;
  padding: 10px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  text-align: center;
  font-size: 10px;
}

.count-span-red {
  background-color: $color-red-tertiary-600;
  color: $color-white-0;
  padding: 10px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  text-align: center;
  font-size: 10px;
}

@media screen and (max-width: 1365px) {
  .action-bar_ {
    bromcom-filter-list-chips {
      width: 224px !important;
    }
  }
}

@media screen and (max-width: 1279px) {
  .action-bar_ {
    bromcom-filter-list-chips {
      width: 136px !important;
    }
  }
}
