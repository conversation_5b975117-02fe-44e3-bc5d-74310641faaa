import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { CheckForMissingStaffRoomService } from '../../../../../../services/check-for-missing-staff-room.service';
import { GridOptions, GridReadyEvent, ICellRendererParams } from 'ag-grid-community';
import { IConflictsGridActions } from '../../../../../../../_shared/models/IConflictsGridActions';
import { gridOptions } from './check-for-missing-staff-room-helper';
import { TranslateService } from '@ngx-translate/core';
import { GridApi } from 'ag-grid-community/dist/lib/gridApi';
import { FormControl } from '@angular/forms';
import { RELATIONSHIP_VIEW_TYPES } from '../../../../../../../_shared/enums/RelationshipViewTypes';
import { filterByValue } from '@bromcom/core';
import { Subject, skip, takeUntil } from 'rxjs';
import { CurriculumPlanService } from '../../../../../../services/curriculum-plan.service';
import { saveAs } from 'file-saver';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';
import { IMissingEntity, IMissingStaffRoomList } from '../../../../../../../_shared/models/IMissingStaffRoomList';
import { SchedulingService } from '../../../../../../services/scheduling.service';
import { BaseModalComponent } from '../../../../../../../_shared/components/BaseModalComponent';

@Component({
  selector: 'bromcom-check-for-missing-staff-room',
  templateUrl: './check-for-missing-staff-room.component.html',
  styleUrls: ['./check-for-missing-staff-room.component.scss']
})
export class CheckForMissingStaffRoomComponent extends BaseModalComponent implements OnInit, OnDestroy {
  @ViewChild('checkForMissingModal', { static: false }) checkForMissingModal!: ElementRef;
  @Input() timeTableId!: number;
  @Input() projectName!: string;
  @Input() timetableName!: string;
  @Output() changeToCurriculumTab = new EventEmitter<any>();
  @Output() changeToSchedulingTab = new EventEmitter<any>();
  gridApi!: GridApi;
  gridOptions!: GridOptions;
  originalData!: IMissingStaffRoomList;
  searchControl: FormControl = new FormControl<string>('');
  showAllYearGroupsControl: FormControl = new FormControl<boolean>(false);
  viewType = RELATIONSHIP_VIEW_TYPES.Staff;
  viewTypes = RELATIONSHIP_VIEW_TYPES;
  readonly unsubscribe$: Subject<void> = new Subject();

  constructor(
    private curriculumPlanService: CurriculumPlanService,
    private curriculumPlanBlockService: CurriculumPlanBlocksService,
    private checkForMissingStaffRoomService: CheckForMissingStaffRoomService,
    private schedulingService: SchedulingService,
    protected translate: TranslateService
  ) {
    super();
  }

  ngOnInit(): void {
    this.gridOptions = gridOptions.call(this, {
      onViewRow: (params: ICellRendererParams) => this.onViewRow(params)
    } as IConflictsGridActions) as GridOptions;

    this.searchControl.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(searchValue => {
        this.setRowData(searchValue || '');
      });

    this.showAllYearGroupsControl.valueChanges
      .pipe(skip(1), takeUntil(this.unsubscribe$))
      .subscribe(showAllYearGroups => {
        this.getMissingList(showAllYearGroups);
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  onGridReady(params: GridReadyEvent): void {
    this.gridApi = params.api;
    this.gridApi.setDomLayout('normal');
  }

  show(): void {
    this.getMissingList(this.showAllYearGroupsControl.getRawValue());
    this.isOpen = true;
    setTimeout(() => {
      this.checkForMissingModal.nativeElement.show();
    }, 100);
  }

  getMissingList(showAllYearGroups: boolean): void {
    if (!this.timeTableId) {
      return;
    }

    showAllYearGroups
      ? this.checkForMissingStaffRoomService.getMissingStaffRoomList(this.timeTableId)
        .subscribe(res => {
          this.setupPageData(res);
        })
      : this.checkForMissingStaffRoomService.getMissingStaffRoomListForYearGroup(this.timeTableId, this.curriculumPlanService.selectedYearGroup$.getValue()!)
        .subscribe(res => {
          this.setupPageData(res);
        })
  }

  setupPageData(res: IMissingStaffRoomList): void {
    this.originalData = res;
    this.setRowData(this.searchControl.getRawValue());
    this.gridApi.sizeColumnsToFit();
  }

  onViewRow(params: ICellRendererParams): void {
    if (params.data.scheduledPeriodName) {
      this.isOpen = false;
      this.schedulingService.resetShowAllYearGroupControl$.next(true);
      this.checkForMissingModal.nativeElement.hide();
      this.schedulingService.pageInitProps$.next({
        showSession: ({
          ...params.data,
          missingAssignment: this.viewType
        })
      })

      this.changeToSchedulingTab.emit(true);

      return;
    }

    this.changeToCurriculumTab.emit(true);
    this.curriculumPlanService.getBlock(params.data.blockId).subscribe(block => {
      const index = block.subjectToYearGroups.findIndex(subjectToYearGroup => subjectToYearGroup.code === params.data.subjectCode);
      this.isOpen = false;
      this.checkForMissingModal.nativeElement.hide();

      // Set selected year group
      const actualYG = this.curriculumPlanService.selectedYearGroup$.getValue();
      if (actualYG !== params.data.yearGroupId) {
        this.curriculumPlanService.selectedYearGroup$.next(params.data.yearGroupId)
      }
      // Set selected band
      setTimeout(() => {
        const actualBand = this.curriculumPlanService.selectedBand$.getValue();
        if (actualBand !== params.data.bandId) {
          this.curriculumPlanService.selectedBand$.next(params.data.bandId);
        }
      }, 1000)
      
      // Set selected subject for Linear block
      this.curriculumPlanBlockService.expandedSelectedSubjectIndex$.next(index);
      // Set selected block to update expanded view block
      this.curriculumPlanService.currentSelectedBlock$.next(block)
      // Open expanded view
      this.curriculumPlanService.isExpanded$.next({
        isExpanded: true,
        sessionId: params.data.sessionId
      })
      // Set missingStaffRoomSessionId$ to show border on expanded view
      this.curriculumPlanBlockService.missingStaffRoomSessionId$.next(params.data.sessionId);
    })

  }

  viewTypeChange(event: Event): void {
    this.viewType = (event as CustomEvent).detail.value === RELATIONSHIP_VIEW_TYPES.Staff ? RELATIONSHIP_VIEW_TYPES.Staff : RELATIONSHIP_VIEW_TYPES.Room;
    this.setRowData(this.searchControl.getRawValue());
    this.gridApi.refreshCells({ force: true })
    this.gridApi.deselectAll();
  }

  exportMissingStaffRoom(): void {
    const subscription = this.viewType === this.viewTypes.Staff
      ? this.checkForMissingStaffRoomService.exportMissingStaff(this.timeTableId)
      : this.checkForMissingStaffRoomService.exportMissingRoom(this.timeTableId)

    subscription.subscribe(response => {
      const viewTypeName = this.viewType === this.viewTypes.Staff ? this.translate.instant('Missing staff') : this.translate.instant('Missing rooms')
      const fileName = `${this.projectName} ${this.timetableName} ${viewTypeName}.csv`
      saveAs(response, fileName);
    })
  }

  setRowData(searchValue: string): void {
    const data = filterByValue<IMissingEntity>(
      searchValue || '',
      this.viewType === this.viewTypes.Staff ? this.originalData.missingStaff : this.originalData.missingRooms,
      ['yearGroupName', 'bandName', 'blockCode', 'blockName', 'subjectCode', 'className', 'periodNumber', 'scheduledPeriodName']);

    this.gridApi.setRowData(data)
  }
}
