import { Component, ElementRef, ViewChild } from '@angular/core';

@Component({
  selector: 'bromcom-ag-grid-select-dropdown',
  templateUrl: './ag-grid-select-dropdown.component.html',
  styleUrls: ['./ag-grid-select-dropdown.component.scss']
})
export class AgGridSelectDropdownComponent {
  @ViewChild('agGridSelectDropdown') agGridSelectDropdown: ElementRef = {} as ElementRef;
  params!: any;
  gridApi!: any;
  data!: any[];
  value!: any;
  placeholder = '';
  searchable = false;
  template = `<div style="display: flex; align-items: center; justify-content: space-between; width: 100%; pointer-events: none;">
              <span style="display: flex; align-items: center; justify-content: flex-start; width: 100%">
                <span class="{{class}}" style="width:16px; display: none; margin-right: 10px;">
                    <bcm-icon icon="{{icon}}" slot="suffix"></bcm-icon>
                </span>
                <span class="{{class}}">{{text}}</span>
              </span>
             </div>`;

  agInit(params: any): void {
    this.params = params;
    this.gridApi = params.gridApi;

    this.data = params.values.map((value: { id: number, name: string }) => ({ ...value, text: value.name }));
    this.value = params.value;
    this.placeholder = params.placeholder;
    this.searchable = (!params?.searchable)? false : params.searchable;
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.agGridSelectDropdown.nativeElement.set(this.value)
    }, 0)
  }

  getValue(): number[] {
    return this.value;
  }

  updateValue(): void {
    this.agGridSelectDropdown.nativeElement.get().then((data: number) => {
      this.value = data;

      if (this.params.node.rowPinned === 'bottom') {
        this.gridApi.getPinnedBottomRow(0).setDataValue([this.params.colDef.field], data);
      } else {
        this.gridApi.getRowNode(this.params.data.id).setDataValue([this.params.colDef.field], data);
      }
    })
  }
}
