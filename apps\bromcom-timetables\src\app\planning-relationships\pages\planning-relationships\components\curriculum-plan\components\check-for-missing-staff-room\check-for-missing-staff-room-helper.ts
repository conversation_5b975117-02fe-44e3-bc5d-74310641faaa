import { customComparator, NOOP, transformToAGGridConfig } from '@bromcom/core';
import { AgGridNoDataComponent } from '@bromcom/ui';
import { CheckForMissingStaffRoomComponent } from './check-for-missing-staff-room.component';
import { ICellRendererParams } from 'ag-grid-community';
import {
  CheckForMissingStaffRoomActionsComponent
} from '../check-for-missing-staff-room-actions/check-for-missing-staff-room-actions.component';

export function gridOptions(this: CheckForMissingStaffRoomComponent, config: { onViewRow: (params: ICellRendererParams) => void; }) {
  const {
    onViewRow = NOOP
  } = config

  return transformToAGGridConfig({
    noRowsOverlayComponent: AgGridNoDataComponent,
    noRowsOverlayComponentParams: {
      style: 'display: flex; justify-content: center; align-items: center; height: 100%; width: 100%; min-height: 162px; padding: 48px 0 0 0',
      noRowsMessageFunc: () => this.translate.instant(`No data available!`)
    },
    columnDefs: [
      {
        field: 'yearGroupName',
        headerName: this.translate.instant('Year Group'),
        wrapHeaderText: true,
        minWidth: 130,
        editable: false,
        flex: 1,
        comparator: customComparator
      },
      {
        field: 'bandName',
        headerName: this.translate.instant('Band'),
        minWidth: 110,
        editable: false,
        flex: 1,
        comparator: customComparator
      },
      {
        field: 'blockCode',
        headerName: this.translate.instant('Block Code'),
        wrapHeaderText: true,
        minWidth: 100,
        editable: false,
        flex: 1,
        comparator: customComparator,
        filter: false,
        menuTabs: []
      },
      {
        field: 'blockName',
        headerName: this.translate.instant('Block Name'),
        wrapHeaderText: true,
        minWidth: 130,
        editable: false,
        flex: 1,
        comparator: customComparator,
        tooltipValueGetter: (params) => {
          return params.value && params.value.length > 8 ? params.value : null;
        },
        filter: false,
        menuTabs: []
      },
      {
        field: 'subjectCode',
        headerName: this.translate.instant('Subject'),
        minWidth: 120,
        editable: false,
        flex: 1,
        comparator: customComparator,
        tooltipValueGetter: (params) => {
          return params.value && params.value.length > 8 ? params.value : null;
        },
      },
      {
        field: 'className',
        headerName: this.translate.instant('Class'),
        minWidth: 110,
        editable: false,
        flex: 1,
        comparator: customComparator
      },
      {
        field: 'periodNumber',
        headerName: this.translate.instant('Period Number'),
        wrapHeaderText: true,
        minWidth: 130,
        editable: false,
        flex: 1.2,
        comparator: customComparator
      },
      {
        field: 'scheduledPeriodName',
        headerName: this.translate.instant('Scheduled Period'),
        wrapHeaderText: true,
        minWidth: 140,
        editable: false,
        flex: 1.2,
        comparator: customComparator,
        filter: false,
        menuTabs: []
      },
      {
        field: 'actions',
        headerName: this.translate.instant('Actions'),
        minWidth: 80,
        filter: false,
        sortable: false,
        menuTabs: [],
        flex: 0.8,
        cellRenderer: CheckForMissingStaffRoomActionsComponent,
        headerClass: 'text-center',
        cellStyle: { display: 'flex', justifyContent: 'center' },
        cellRendererParams: {
          onViewRow
        },
        editable: false
      }
    ]
  })
}
