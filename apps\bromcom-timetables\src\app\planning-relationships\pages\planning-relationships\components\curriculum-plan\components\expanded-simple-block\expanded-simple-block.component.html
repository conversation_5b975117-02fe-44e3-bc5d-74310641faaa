<div class="expanded-simple-block-container">
  <bromcom-expanded-header [selectedBlock]="selectedBlock"
                           [selectedYearGroupId]="selectedYearGroupId"
                           [timetableId]="timetableId"
                           (openEditClassCodesModalEvent)="openEditClassCodesModal($event)"
                           (openLinkBlocksModalComponentEvent)="openLinkBlocksModal($event)">
  </bromcom-expanded-header>

  <div class="actions" [formGroup]="form">
    <div>
      <label for="periodCount">
        {{'Total Period Count' | translate}}*
      </label>
      <input id="periodCount"
             class="input readonly"
             formControlName="periodCount"
             readonly/>
    </div>

    <div>
      <label>
        {{'Number of Classes' | translate}}*
      </label>
      <input class="input"
             formControlName="classCount"
             appAcceptNumbers/>
    </div>

    <div>
      <label>
        {{'Single Periods' | translate}}*
      </label>
      <input class="input"
             formControlName="singlePeriodCount"
             appAcceptNumbers/>
    </div>

    <div>
      <label>
        {{'Double Periods' | translate}}*
      </label>
      <input class="input"
             formControlName="doublePeriodCount"
             appAcceptNumbers/>
    </div>

    <div class="apply">
      <bcm-button (click)="updateAnExistingSubjectToYearGroup()"
                  [disabled]="form.invalid || !selectedBlock.subjectToYearGroups[0].subjectId">
        {{'Apply' | translate }}
      </bcm-button>
    </div>
  </div>

  <div class="error" *ngIf="(form.controls.classCount?.errors?.['required'] ||
     form.controls.periodCount?.errors?.['required'] || form.controls.singlePeriodCount?.errors?.['required'] ||
      form.controls.doublePeriodCount?.errors?.['required'])">
    <span>
        {{'All fields are required' | translate}}
    </span>
  </div>

  <div class="error" *ngIf="!(form.controls.classCount?.errors?.['required'] ||
     form.controls.periodCount?.errors?.['required'] || form.controls.singlePeriodCount?.errors?.['required'] ||
      form.controls.doublePeriodCount?.errors?.['required']) && form.controls.periodCount.errors?.['max']">
      <span>
        {{'Maximum total period count is 99.' | translate}}
      </span>
  </div>

  <div class="error" *ngIf="!(form.controls.classCount?.errors?.['required'] ||
     form.controls.periodCount?.errors?.['required'] || form.controls.singlePeriodCount?.errors?.['required'] ||
      form.controls.doublePeriodCount?.errors?.['required']) &&form.controls.periodCount.errors?.['min']">
      <span>
        {{'Minimum total period count is 1.' | translate}}
      </span>
  </div>

  <div class="panel">
    <bromcom-detailed-block [staffs]="staffs"
                            [rooms]="rooms"
                            [projectId]="projectId"
                            [missingStaffRoomSessionId]="missingStaffRoomSessionId"
                            [staffToSessionActiveId]="staffToSessionActiveId"
                            [roomToSessionActiveId]="roomToSessionActiveId"
                            [periodMenuRef]="periodMenuRef"
                            [sessionMenuRef]="sessionMenuRef"
                            [classMenuRef]="classMenuRef"
                            [resetSelectedPeriodIds$]="resetSelectedPeriodIds$"
                            [resetSelectedClassIds$]="resetSelectedClassIds$"
                            [setScrollToPeriodIndex$]="setScrollToPeriodIndex$"
                            (sessionMenuOpenedEvent)="sessionMenuOpened($event)"
                            (updateAnExistingSubjectToYearGroup)="updateAnExistingSubjectToYearGroup()"
                            [subjectMenuRef]="subjectMenuRef"
                            (periodMenuOpenedEvent)="periodMenuOpened($event)"
                            (subjectMenuOpenedEvent)="subjectMenuOpened($event)"
                            (classMenuOpenedEvent)="classMenuOpened($event)"
    ></bromcom-detailed-block>
  </div>
</div>
