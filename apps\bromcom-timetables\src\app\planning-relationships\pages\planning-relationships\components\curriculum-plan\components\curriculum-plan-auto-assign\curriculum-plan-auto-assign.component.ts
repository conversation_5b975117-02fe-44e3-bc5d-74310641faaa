import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormArray, FormControl, FormGroup, Validators } from "@angular/forms";
import { NestedTreeControl } from "@angular/cdk/tree";
import { MatTreeNestedDataSource } from "@angular/material/tree";
import { ISubject } from "../../../../../../../_shared/models/ISubject";
import { IAutoAssign } from "../../../../../../../_shared/models/IAutoAssign";
import { CurriculumPlanService } from "../../../../../../services/curriculum-plan.service";
import { customComparator, IListOption } from "@bromcom/core";
import { GeneralModalComponent } from "../../../../../../../_shared/components/general-modal/general-modal.component";
import { fork<PERSON>oin, of, Subject, switchMap, takeUntil } from "rxjs";
import { CurriculumPlanBlocksService } from "../../../../../../services/curriculum-plan-blocks";
import { NOT_AVAILABLE } from "../../../../../../../_shared/enums/NotAvailable";
import { ICurriculumPlanBlock } from "../../../../../../../_shared/models/ICurriculumPlanBlock";
import { BaseModalComponent } from '../../../../../../../_shared/components/BaseModalComponent';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@bromcom/ui';
import { StaffingService } from '../../../../../../services/staffing.service';
import { IStaffingYearGroup } from '../../../../../../../_shared/models/IStaffingYearGroup';
import { IStaffingListElement } from '../../../../../../../_shared/models/IStaffingList';
import { TitleHelperService } from '../../../../../../../_shared/services/title-helper.service';
import {
  ConflictsModalComponent
} from '../../../../components/planning-relationship-actions/conflicts-modal/conflicts-modal.component';


interface Node {
  name: string;
  children?: Node[];
  code?: string;
  color?: string;
  index?: number;
}

@Component({
  selector: 'bromcom-curriculum-plan-auto-assign',
  templateUrl: './curriculum-plan-auto-assign.component.html',
  styleUrls: ['./curriculum-plan-auto-assign.component.scss']
})
export class CurriculumPlanAutoAssignComponent extends BaseModalComponent implements OnInit, OnDestroy {
  @ViewChild('removeStaffAndRoom') removeStaffAndRoomRef!: GeneralModalComponent;
  @ViewChild('warningModal') warningModalRef!: GeneralModalComponent;
  @ViewChild('autoAssignModal') autoAssignModalRef!: ElementRef;

  @Input() timetableId!: number;
  @Input() currentSelectedBlock!: ICurriculumPlanBlock | undefined | null;
  @Input() projectId!: number;
  @Input() selectYearGroups: IListOption[] = [];

  selectAllSubjects = new FormControl(false);
  searchControl = new FormControl('');
  selectYearGroupsForm = new FormControl<number | null>(null, Validators.required);
  staffRoomForm = new FormControl('', Validators.required);
  listByForm = new FormControl(1);
  useAnyStaffWithASubjectRelationshipForm = new FormControl({ value: false, disabled: true });
  subjectsForm = new FormGroup({
    subjectList: new FormArray<FormControl<boolean>>([])
  });
  removeStaffAndRoomDescription = '';
  subjectList: ISubject[] = [];
  staffAndRoom = [
    {
      id: 1,
      text: 'Staff only'
    },
    {
      id: 2,
      text: 'Room only'
    },
    {
      id: 3,
      text: 'Staff and Room'
    }
  ];

  listBy = [
    {
      id: 1,
      text: 'Subjects'
    }, {
      id: 2,
      text: 'Departments'
    }
  ];

  treeControl = new NestedTreeControl<Node>(node => node.children);
  dataSource = new MatTreeNestedDataSource<Node>();
  TREE_DATA: Node[] = [];
  readonly unsubscribe$: Subject<void> = new Subject();
  projectName = '';
  timetableName = '';
  @ViewChild('conflictsAutoAssignModalComponent') conflictsAutoAssignModalComponent! : GeneralModalComponent;
  @ViewChild('conflictsModalComponent') conflictsModalComponent!: ConflictsModalComponent;

  constructor(private curriculumPlanService: CurriculumPlanService,
              private curriculumPlanBlocksService: CurriculumPlanBlocksService,
              private staffingService: StaffingService,
              private translate: TranslateService,
              private snackbar: SnackbarService,
              private titleHelperService: TitleHelperService) {
    super();
  }

  ngOnInit(): void {
    this.getSubjectList();

    this.listByForm.valueChanges.pipe(takeUntil(this.unsubscribe$))
      .subscribe(() => {
        this.treeControl.expandAll();
        this.clearAll();
      })

    this.staffRoomForm.valueChanges.pipe(takeUntil(this.unsubscribe$)).subscribe(val => {
      if (this.staffRoomForm.value?.toString() === '2') {
        this.useAnyStaffWithASubjectRelationshipForm.setValue(false);
        this.useAnyStaffWithASubjectRelationshipForm.disable();
      } else {
        this.useAnyStaffWithASubjectRelationshipForm.enable();
      }
    });

    this.selectAllSubjects.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(val => {
        this.subjectsForm.controls.subjectList.value.forEach((_, index) => {
          this.subjectsForm.controls.subjectList.controls[index].setValue(val || false);
        });
      });

      this.titleHelperService.getProjectById(this.projectId)?.subscribe(response => {
        this.projectName = response?.projectName;
      });
      this.titleHelperService.getTimetableById(this.timetableId)?.subscribe(response => {
        this.timetableName = response?.timeTableName;
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
  
  onChange() {
    let indices: number[] = [];
    if (this.listByForm.value === 2) {
      indices = <number[]>this.dataSource.data.map(d => d.children).flat().map(c => c?.index) ?? [];
    }
    const filteredValues = indices && indices.length ? indices!
      .filter((index: number): index is number => index !== undefined) // Filter out undefined indices
      .map((index: number) => this.subjectsForm.controls.subjectList.value[index]) : [];
    setTimeout(() => {
      // This part is to check if all the elements were selected and if yes, the 'select all' should be selected too
      const all = this.listByForm.value === 2 ? filteredValues.every((item: any) => item) : this.subjectsForm.controls.subjectList.value.every(item => item);
      this.selectAllSubjects.setValue(all, { emitEvent: false, onlySelf: true });
    }, 200)
  }

  getSubjectList() {
    this.curriculumPlanService.getSubjectsList(this.projectId).subscribe(res => {
      const sortedByDepAndSubjectCode = res.filter(subject => subject.departmentName)
        .sort((a: ISubject, b: ISubject) => customComparator(a.departmentName || '', b.departmentName || ''))
        .sort((a: ISubject, b: ISubject) => {
          if (a.departmentName === b.departmentName) {
            if (a.code < b.code) return -1;
            if (a.code > b.code) return 1;
          }
          return 0
        })

      this.subjectList = res.filter(subject => subject.shortName.toLowerCase() !== NOT_AVAILABLE.FREE_SESSION)
        .sort((a: ISubject, b: ISubject) => customComparator(a.code, b.code))
      this.TREE_DATA = this.transformArray(sortedByDepAndSubjectCode);
      this.dataSource.data = this.TREE_DATA;
      this.treeControl.dataNodes = this.TREE_DATA;

      this.subjectsForm.controls.subjectList.clear();
      this.subjectList.map(() => {
        this.subjectsForm.controls.subjectList.push(new FormControl(false as any), { emitEvent: false });
      });
      this.subjectsForm.controls.subjectList.updateValueAndValidity();
    });
  }

  hasChild = (_: number, node: Node) => !!node.children && node.children.length > 0;

  transformArray(inputArray: ISubject[]): Node[] {
    const outputArray: Node[] = [];
    inputArray.forEach((inputObject: ISubject): void => {
      const { departmentName } = inputObject;
      if (departmentName) {
        const departmentIndex: number = outputArray.findIndex(item => item.name === departmentName);
        if (departmentIndex !== -1) {
          outputArray[departmentIndex]?.children?.push({
            name: inputObject.name,
            code: inputObject.code,
            color: inputObject.color,
            index: this.subjectList.findIndex(s => s.id === inputObject.id)
          });
        } else {
          outputArray.push({
            name: departmentName,
            children: [{ name: inputObject.name, code: inputObject.code, color: inputObject.color, index: this.subjectList.findIndex(s => s.id === inputObject.id) }]
          });
        }
      }
    });
    return outputArray;
  }

  autoAssignment(): void {
    const subjectIds: number[] = []
    this.subjectsForm.controls.subjectList.value.forEach((subj, index) => {
      if (subj) {
        subjectIds.push(this.subjectList[index].id);
      }
    });
    forkJoin(
      [
        this.curriculumPlanService.getStaffToSubjectRelationsBySubjectIds(this.timetableId, subjectIds),
        this.curriculumPlanService.getRoomToSubjectRelationsBySubjectIds(this.timetableId, subjectIds),
        this.staffingService.getStaffingList(this.projectId, this.timetableId)
      ]).subscribe(
      ([staffRelations, roomRelations, staffingList]) => {
        const checkForStaffingPageAssignment = this.checkForStaffingPageAssignment(staffingList, subjectIds);

        if ((!checkForStaffingPageAssignment && this.staffRoomForm.value && [1, 3].includes(+this.staffRoomForm.value)) ||
          ((this.staffRoomForm.value && +this.staffRoomForm.value === 1 && staffRelations.length > 0) ||
            (this.staffRoomForm.value && +this.staffRoomForm.value === 2 && roomRelations?.length > 0) ||
            (this.staffRoomForm.value && +this.staffRoomForm.value === 3 && staffRelations.length > 0 && roomRelations?.length > 0))) {
          this.autoAssignmentHelper();
        } else {
          this.warningModalRef.show();
        }
      }
    );
  }

  checkForStaffingPageAssignment(staffingList: IStaffingListElement[], subjectIds: number[]): boolean {
    // in case of staff assignment, the contact time is added for the selected subjects for the selected year groups on the staffing page
    const yearGroupIds = this.selectYearGroupsForm.value as unknown as number[];
    const staffingListFiltered = staffingList.filter(element => {
      element.subjects = element.subjects.filter(subject => subjectIds.includes(subject.subjectId))
      return !!element.subjects.length;
    })

    const obj: {
      [key: number]: {
        [key: number]: number[]
      }
    } = {};

    subjectIds.forEach((subjectId: number) => {
      obj[subjectId] = {}
      yearGroupIds.forEach((ygId: number) => {
        obj[subjectId][ygId] = []
      })
    })

    staffingListFiltered.forEach(element => element.subjects.forEach(subject => subject.yearGroups.forEach((yg: IStaffingYearGroup) => {
      if (obj[subject.subjectId][yg.yearGroupId]) {
        obj[subject.subjectId][yg.yearGroupId].push(yg.contactTime ?? 0)
      }
    })))

    return Object.values(obj).some(subject => Object.values(subject).some(yearGroups => yearGroups.every(contactTime => !contactTime)))
  }

  autoAssignmentHelper() {
    if (this.staffRoomForm.invalid || this.selectYearGroupsForm.invalid) {
      return;
    }

    const payload: IAutoAssign = {
      yearGroupIds: [],
      subjectIds: [],
      staffAssignment: false,
      roomAssignment: false,
      useAnyStaffWithASubjectRelationship: this.useAnyStaffWithASubjectRelationshipForm.value as boolean
    };

    if (this.staffRoomForm.value && +this.staffRoomForm.value === 1) {
      payload.staffAssignment = true;
      payload.roomAssignment = false;
    } else if (this.staffRoomForm.value && +this.staffRoomForm.value === 2) {
      payload.staffAssignment = false;
      payload.roomAssignment = true;
    } else if (this.staffRoomForm.value && +this.staffRoomForm.value === 3) {
      payload.staffAssignment = true;
      payload.roomAssignment = true;
    }
    payload.yearGroupIds = this.selectYearGroupsForm.value as any;
    this.subjectsForm.controls.subjectList.value.forEach((subj, index) => {
      if (subj) {
        payload.subjectIds.push(this.subjectList[index].id);
      }
    });

    this.curriculumPlanService
      .autoAssign(this.timetableId, payload)
      .pipe(
        switchMap(() => {
          if (!this.currentSelectedBlock) {
            return of(null);
          }
          return this.curriculumPlanService.getBlock(this.currentSelectedBlock?.id);
        })
      )
      .subscribe({
        next: block => {
          if (block) {
            this.curriculumPlanService.currentSelectedBlock$.next(block);
          }
          this.curriculumPlanBlocksService.stateChanged$.next();
          this.snackbar.success(this.translate.instant('Auto Assignment has been successfully completed.'))
        },
        error: ({ error }) => {
          if (error?.validationErrors?.[0]) {
            if (this.currentSelectedBlock) {
              this.curriculumPlanService.getBlock(this.currentSelectedBlock?.id).subscribe(response => {
                this.curriculumPlanService.currentSelectedBlock$.next(response);
              });
            }
            this.curriculumPlanBlocksService.stateChanged$.next();
            this.conflictsAutoAssignModalComponent.show();
          } else {
            this.snackbar.error();
          }
        }
      });

    this.autoAssignModalRef.nativeElement.hide();
    this.isOpen = false;
  }


  onRemoveStaffAndRoom(): void {
    if (this.staffRoomForm.invalid || this.selectYearGroupsForm.invalid) {
      return;
    }

    const payload: IAutoAssign = {
      yearGroupIds: [],
      subjectIds: [],
      staffAssignment: false,
      roomAssignment: false,
      useAnyStaffWithASubjectRelationship: this.useAnyStaffWithASubjectRelationshipForm.value as boolean
    };

    if (this.staffRoomForm.value && +this.staffRoomForm.value === 1) {
      payload.staffAssignment = true;
      payload.roomAssignment = false;
    } else if (this.staffRoomForm.value && +this.staffRoomForm.value === 2) {
      payload.staffAssignment = false;
      payload.roomAssignment = true;
    } else if (this.staffRoomForm.value && +this.staffRoomForm.value === 3) {
      payload.staffAssignment = true;
      payload.roomAssignment = true;
    }
    payload.yearGroupIds = this.selectYearGroupsForm.value as any;
    this.subjectsForm.controls.subjectList.value.forEach((subj, index) => {
      if (subj) {
        payload.subjectIds.push(this.subjectList[index].id);
      }
    });

    this.curriculumPlanService
      .unAssign(this.timetableId, payload)
      .pipe(
        switchMap(() => {
          if (!this.currentSelectedBlock) {
            return of(null);
          }
          return this.curriculumPlanService.getBlock(this.currentSelectedBlock?.id);
        })
      )
      .subscribe(block => {
        if (block) {
          this.curriculumPlanService.currentSelectedBlock$.next(block);
        }
        this.curriculumPlanBlocksService.stateChanged$.next();
        this.snackbar.success(this.translate.instant('Assignments have been successfully removed.'))
      });
    this.autoAssignModalRef.nativeElement.hide();
    this.isOpen = false;
  }

  removeAssignment(): void {
    if (this.staffRoomForm.value && +this.staffRoomForm.value === 1) {
      this.removeStaffAndRoomDescription = this.translate.instant('All staff will be removed from the selected year groups and subjects.');
    } else if (this.staffRoomForm.value && +this.staffRoomForm.value === 2) {
      this.removeStaffAndRoomDescription = this.translate.instant('All rooms will be removed from the selected year groups and subjects.');
    } else if (this.staffRoomForm.value && +this.staffRoomForm.value === 3) {
      this.removeStaffAndRoomDescription = this.translate.instant('All staff and rooms will be removed from the selected year groups and subjects.');
    }
    this.removeStaffAndRoomRef.show();
  }

  clearAll(): void {
    this.subjectsForm.controls.subjectList.value.forEach((_, index) => {
      this.subjectsForm.controls.subjectList.controls[index].setValue(false);
      this.selectAllSubjects.setValue(false, { emitEvent: false, onlySelf: true });
    });
  }

  show(): void {
    this.selectAllSubjects.setValue(false);
    this.searchControl.setValue('');
    this.selectYearGroupsForm.setValue(null);
    this.staffRoomForm.setValue('');
    this.listByForm.setValue(1);
    this.useAnyStaffWithASubjectRelationshipForm.setValue(false);
    this.getSubjectList();

    this.isOpen = true;
    setTimeout(() => {
      this.autoAssignModalRef.nativeElement.show();
    }, 100)
  }

  onShowConflicts(): void{
    this.conflictsAutoAssignModalComponent.hide();
    this.conflictsModalComponent.show();
  }

}
