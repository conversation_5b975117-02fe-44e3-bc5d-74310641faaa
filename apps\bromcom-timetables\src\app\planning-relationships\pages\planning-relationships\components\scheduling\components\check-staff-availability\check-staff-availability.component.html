<ng-container *ngIf="isOpen">
    <bcm-modal size="xxxlarge" class="check-availability-modal" (bcm-modal-before-close)="onClose()"
        #checkAvailabilityModal>
        <bcm-modal-header>{{ 'Check Availability' | translate }}</bcm-modal-header>

        <div class="modal-body">
            <div class="check-availability-container">
                <mat-tab-group>
                    <mat-tab *ngFor="let week of weeks" [label]="'Week ' + week.weekDisplayName">
                        <div class="tab-content">
                            <div class="table-container">
                                <table class="availability-table">
                                    <thead>
                                        <tr>
                                            <th class="top-left-cell" rowspan="2"></th>
                                            <th class="day-header" *ngFor="let day of getDays(week); let i = index"
                                                [attr.colspan]="getPeriods(day).length" [ngClass]="{ 'day-separator': i !== getDays(week).length - 1 }">
                                                {{ day.text }}
                                            </th>
                                        </tr>
                                        <tr>
                                            <ng-container *ngFor="let day of getDays(week); let i = index">
                                                <th class="period-header" *ngFor="let period of getPeriods(day); let j = index"
                                                    [ngClass]="{ 'day-separator': j === getPeriods(day).length - 1 && i !== getDays(week).length - 1 }"
                                                    [matTooltip]="period.text || '--'"
                                                    [matTooltipDisabled]="period.text && period.text.length! < 3" [matTooltipPosition]="'above'">
                                                    {{ period.text  }}
                                                    <bcm-tooltip [message]="'Lunch Period'" trigger="hover" *ngIf="period.isLunch">
                                                        <div class="circle">L</div>
                                                    </bcm-tooltip>
                                                </th>
                                            </ng-container>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let staff of selectedStaffs; trackBy: identify">
                                            <td class="staff-holder">{{ staff.name }}</td>
                                            <ng-container *ngFor="let day of getDays(week); let i = index; trackBy: identify">
                                                <td class="period-cell"
                                                    *ngFor="let period of getPeriods(day); let j = index; trackBy: identify" [ngClass]="{ 'day-separator': j === getPeriods(day).length - 1 && i !== getDays(week).length - 1 }">
                                                    <div class="flex-column period-holder">
                                                        <ng-container
                                                            *ngFor="let session of getSessions(period, staff); trackBy: identify">
                                                            <div class="session-class">
                                                                <span class="session"
                                                                    [ngStyle]="{ 'background-color': getColor(session), 'color': textColor }">
                                                                    <div class="session-name"
                                                                        [matTooltip]="session.sessionName || '--'"
                                                                        [matTooltipDisabled]="session.sessionName && session.sessionName.length! < 3"
                                                                        [matTooltipPosition]="'above'">{{
                                                                        session.sessionName }}</div>
                                                                </span>
                                                            </div>
                                                        </ng-container>
                                                        <ng-container
                                                            *ngFor="let nonContactCode of getNonContactCodes(period, staff); trackBy: identify">
                                                            <div class="non-contact-class">
                                                                <span class="indicator" *ngIf="nonContactCode.isMultiStaffAssociated">G</span>
                                                                <span class="non-contact-holder">
                                                                    <div class="non-contact-name"
                                                                        [matTooltip]="nonContactCode.codeName ?? ''"
                                                                        [matTooltipDisabled]="nonContactCode.codeName?.length! < 5"
                                                                        [matTooltipPosition]="'above'">{{
                                                                        nonContactCode.codeName }}</div>
                                                                </span>
                                                            </div>
                                                        </ng-container>
                                                    </div>
                                                </td>
                                            </ng-container>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </mat-tab>
                </mat-tab-group>
            </div>
        </div>

        <bcm-modal-footer>
            <bcm-button kind="ghost" (click)="onClose()">{{'Close' | translate}}</bcm-button>
        </bcm-modal-footer>
    </bcm-modal>
</ng-container>