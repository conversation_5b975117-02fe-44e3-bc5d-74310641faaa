const generator = require('./data/generate_block');
module.exports = (server, db) => {
  //
  // TEST
  //
  server.get('/api/test', (req, res) => {
    res.json(db.get('test').value());
  });

  // GET PREVIEW TO REVIEW PAGE
  //
  server.get('/api/v1/TimeTable/:timeTableId/Blocks/:yearGroupId/Review', (req, res) => {
    res.json(db.get('get_review_preview').value());
  });

  //
  // PROJECTS for project overview page
  //
  server.get('/api/v1/Timetable/Projects', (req, res) => {
    res.json(db.get('get_projects').value());
  });

  //
  // PROJECT NAME UNIQUE
  //
  server.head('/api/v1/Timetable/Projects/:projectName', (req, res) => {
    if (!['Project', 'Project name', 'a'].includes(req.params.projectName)) {
      res.status(422).end();
    } else {
      res.status(204).end();
    }
  });

  //
  // PROJECT NAME UNIQUE EXISTING PROJECT
  //
  server.head('/api/v1/TimeTable/Projects/:projectName/:excludedProjectId', (req, res) => {
    if (!['existing'].includes(req.params.projectName)) {
      res.status(422).end();
    } else {
      res.status(204).end();
    }
  });

  //
  // GET PROJECT
  // GET CURRENT ACADEMIC YEAR
  //
  server.get('/api/v1/TimeTable/Projects/:projectId', (req, res) => {
    switch (req.params.projectId) {
      case 'CurrentAcademicYear':
        res.json({
          startDate: '2022-08-28T00:00:00',
          endDate: '2023-08-26T23:59:59'
        });
        break;
      default:
        res.json(db
          .get('get_projects')
          .value()
          .find((project) => project.id.toString() === req.params.projectId.toString()));
    }
  });

  //
  // GET PROJECT CONFLICTS
  //
  server.get('/api/v1/Timetable/Projects/:projectId/Import/Conflicts', (req, res) => {
    res.json([
      {
        id: 94,
        entityType: "YearGroup",
        fieldName: "Name",
        conflictingValue: "7",
        instant: "2023-07-02T05:30:26.08"
      }
    ]);
  });

  //
  // GET TIMETABLE NAME
  //
  server.get('/api/v1/TimeTable/TimeTables/:timetableId', (req, res) => {
    res.json({ timeTableName: 'timeTableName' });
  });

  //
  // PUT SPECIFIC WIZARD STATE
  //
  server.put('/api/v1/TimeTable/Projects/:projectId/WizardState', (req, res) => {
    res.status(200).end();
  });

  //
  // PROJECT DETAILS STEP
  //
  server.post('/api/v1/Timetable/Projects', (req, res) => {
    res.json({ id: 64 });
  });

  //
  // PROJECT DETAILS STEP
  //
  server.post('/api/v1/Timetable/Projects/:projectId/import', (req, res) => {
    res.status(200).end();
  });

  //
  // PROJECT DETAILS STEP
  //
  server.get('/api/v1/Timetable/Projects/:projectId/ImportProgress/:previousProgress', (req, res) => {
    switch (req.params.previousProgress) {
      case '0':
        res.json(50);
        break;
      default:
        res.json(100);
    }
  });

  //
  // PROJECT DETAILS STEP
  //
  server.put('/api/v1/Timetable/Projects/:id', (req, res) => {
    res.status(200).end();
  });

  //
  // YEAR GROUPS LIST for new project
  //

  server.get('/api/v1/Timetable/:projectId/yeargroups', (req, res) => {
    const originalMockData = db.get('get_year_groups').value();

    res.json([...generator.generatedYeargroupsMockData(req), ...originalMockData]);
  });

  //
  // GET YEAR GROUP
  // Get list of NC Year Groups - for dropdown
  // Get list of Next Year Groups - for dropdown
  //
  server.get('/api/v1/timetable/yeargroups/:id', (req, res) => {
    switch (req.params.id) {
      case 'ncyeargroups':
        res.json(db.get('get_nc_year_groups').value());
        break;
      case 'nextyeargroups':
        res.json(db.get('get_next_year_groups').value());
        break;
      default:
        res.json({
          id: Date.now(),
          originalId: null,
          name: '12',
          description: 'Year 12',
          ncYearGroupId: 19,
          nextYearGroupId: null,
          isExcluded: false
        });
    }
  });

  //
  // POST YEAR GROUP
  //
  server.post('/api/v1/timetable/:projectId/yeargroups', (req, res) => {
    res.json({ id: Date.now() });
    // res.status(409).end();
  });

  //
  // PUT YEAR GROUP
  //
  server.put('/api/v1/timetable/yeargroups/:id', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // DELETE YEAR GROUP
  //
  server.delete('/api/v1/timetable/yeargroups/:id', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // INCLUDE/EXCLUDE YEAR GROUP
  //
  server.put('/api/v1/timetable/yeargroups/:id/isExcluded', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // INCLUDE/EXCLUDE BULK YEAR GROUP LIST
  //
  server.put('/api/v1/timetable/yeargroups/bulk/isExcluded', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // SUBJECTS LIST for new project
  //
  server.get('/api/v1/Timetable/:projectId/subjects', (req, res) => {
    res.json(db.get('get_subjects').value());
  });

  //
  // GET SUBJECT
  //
  server.get('/api/v1/timetable/subjects/:id', (req, res) => {
    res.json({
      id: Date.now(),
      originalId: 153,
      name: 'Art',
      isExcluded: false,
      shortName: 'Art',
      code: 'Ar',
      color: '#FFA07A',
      department: 'Art',
      departmentId: 2
    });
    // res.status(409).end();
  });

  //
  // POST SUBJECT
  //
  server.post('/api/v1/timetable/:projectId/subjects', (req, res) => {
    res.json({ id: Date.now() });
    // res.status(409).end();
  });

  //
  // PUT SUBJECT
  //
  server.put('/api/v1/timetable/subjects/:id', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // DELETE SUBJECT
  //
  server.delete('/api/v1/timetable/subjects/:id', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // INCLUDE/EXCLUDE SUBJECT
  //
  server.put('/api/v1/timetable/subjects/:id/isExcluded', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // INCLUDE/EXCLUDE BULK SUBJECT LIST
  //
  server.put('/api/v1/timetable/subjects/bulk/isExcluded', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // Get list of Departments - for dropdown
  //
  server.get('/api/v1/Timetable/:projectId/departments', (req, res) => {
    res.json(db.get('get_subject_departments').value());
  });

  //
  // GET DEPARTMENT
  //
  server.get('/api/v1/timetable/departments/:id', (req, res) => {
    res.json({
      id: Date.now(),
      originalId: 3,
      name: 'Natural scienceN',
      description: 'Natural scienceD'
    });
    // res.status(409).end();
  });

  //
  // POST DEPARTMENT
  //
  server.post('/api/v1/timetable/:projectId/departments', (req, res) => {
    res.json({ id: Date.now() });
    // res.status(409).end();
  });

  //
  // PUT DEPARTMENT
  //
  server.put('/api/v1/timetable/departments/:id', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // DELETE DEPARTMENT
  //
  server.delete('/api/v1/timetable/departments/:id', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // ROOMS LIST for new project
  //
  server.get('/api/v1/Timetable/:projectId/rooms', (req, res) => {
    res.json(db.get('get_rooms').value());
  });

  //
  // GET ROOM
  //
  server.get('/api/v1/timetable/rooms/:id', (req, res) => {
    res.json({
      id: Date.now(),
      originalId: 1,
      name: 'EAST 01',
      isExcluded: false,
      code: 'E01',
      capacity: 30,
      typeId: 1,
      siteId: 1
    });
    // res.status(409).end();
  });

  //
  // POST ROOM
  //
  server.post('/api/v1/timetable/:projectId/rooms', (req, res) => {
    res.json({ id: Date.now() });
    // res.status(409).end();
  });

  //
  // PUT ROOM
  //
  server.put('/api/v1/timetable/rooms/:id', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // DELETE ROOM
  //
  server.delete('/api/v1/timetable/rooms/:id', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // INCLUDE/EXCLUDE ROOM
  //
  server.put('/api/v1/timetable/rooms/:id/isExcluded', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // INCLUDE/EXCLUDE BULK ROOM LIST
  //
  server.put('/api/v1/timetable/rooms/bulk/isExcluded', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // Get list of Sites - for dropdown
  //
  server.get('/api/v1/Timetable/:projectId/rooms/sites', (req, res) => {
    res.json(db.get('get_sites').value());
  });

  //
  // Get list of Types - for dropdown
  //
  server.get('/api/v1/Timetable/:projectId/rooms/types', (req, res) => {
    res.json(db.get('get_types').value());
  });

  //
  // STAFF LIST for new project
  //
  server.get('/api/v1/Timetable/:projectId/staff', (req, res) => {
    res.json(db.get('get_staff').value());
  });

  //
  // GET STAFF
  // Get list of StaffGender - for dropdown
  // Get list of StaffTitle - for dropdown
  // Get list of StaffType - for dropdown
  //
  server.get('/api/v1/timetable/staff/:id', (req, res) => {
    switch (req.params.id) {
      case 'genders':
        res.json(db.get('get_staffGender').value());
        break;
      case 'titles':
        res.json(db.get('get_staffTitle').value());
        break;
      case 'types':
        res.json(db.get('get_staffType').value());
        break;
      default:
        res.json({
          id: Date.now(),
          originalId: 1,
          firstName: 'Denver',
          middleName: null,
          lastName: 'Adams',
          code: 'DAD',
          genderId: 77,
          titleId: null,
          totalContactTime: 24,
          typeId: 1,
          isExcluded: false
        });
    }
  });

  //
  // POST STAFF
  //
  server.post('/api/v1/timetable/:projectId/staff', (req, res) => {
    res.json({ id: new Date().getTime() });
    // res.status(409).end();
  });

  //
  // PUT STAFF
  //
  server.put('/api/v1/timetable/staff/:id', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // DELETE STAFF
  //
  server.delete('/api/v1/timetable/staff/:id', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // INCLUDE/EXCLUDE STAFF
  //
  server.put('/api/v1/timetable/staff/:id/isExcluded', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // INCLUDE/EXCLUDE BULK STAFF LIST
  //
  server.put('/api/v1/timetable/staff/bulk/isExcluded', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // NON CONTACT CODES LIST for new project
  //
  server.get('/api/v1/Timetable/:projectId/noncontactcodes', (req, res) => {
    res.json(db.get('get_non_contact_codes').value());
  });

  //
  // GET NON CONTACT CODE
  //
  server.post('/api/v1/timetable/noncontactcodes/:id', (req, res) => {
    res.json({
      id: 1,
      originalId: 1,
      codeName: 'Lunch',
      shortName: 'Lnch',
      isTeacher: true,
      isRoom: true,
      isExcluded: false
    });
  });

  //
  // POST NON CONTACT CODE
  //
  server.post('/api/v1/timetable/:projectId/noncontactcodes', (req, res) => {
    res.json({ id: new Date().getTime() });
    // res.status(409).end();
  });

  //
  // PUT NON CONTACT CODE
  //
  server.put('/api/v1/timetable/noncontactcodes/:id', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // DELETE NON CONTACT CODE
  //
  server.delete('/api/v1/timetable/noncontactcodes/:id', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // INCLUDE/EXCLUDE NON CONTACT CODE
  //
  server.put('/api/v1/timetable/noncontactcodes/:id/isExcluded', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // INCLUDE/EXCLUDE BULK NON CONTACT CODE LIST
  //
  server.put('/api/v1/timetable/noncontactcodes/bulk/isExcluded', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // GET NON CONTACT CODE ASSOCIATIONS
  //
  server.get('/api/v1/TimeTable/:timetableId/NonContactCodes/Association', (req, res) => {
    res.status(200).end();
  });

  //
  // POST NEW NON CONTACT CODE ASSOCIATIONS
  //
  server.post('/api/v1/TimeTable/:timetableId/NonContactCodes/Association', (req, res) => {
    res.status(200).end();
  });

  //
  // DELETE NON CONTACT CODE ASSOCIATIONS
  //
  server.delete('/api/v1/TimeTable/:timetableId/NonContactCodes/Association', (req, res) => {
    res.status(204).end();
  });

  //
  // TIMETABLE NAME UNIQUE
  //
  server.head('/api/v1/Timetable/Timetables/:timetableName', (req, res) => {
    if (['2022', '2023'].includes(req.params.timetableName)) {
      res.status(204).end();
    } else {
      res.status(422).end();
    }
  });

  //
  // TIMETABLES for timetables overview page
  //
  server.get('/api/v1/Timetable/:id/Timetables', (req, res) => {
    res.json(db.get('get_timetables').value());
  });

  //
  // Add new timetable
  //
  server.post('/api/v1/TimeTable/:projectId/TimeTables/', (req, res) => {
    res.json(db.get('get_with_period_structure').value());
  });

  //
  // Edit timetable
  //
  server.put('/api/v1/TimeTable/:projectId/TimeTables/:timetableId', (req, res) => {
    res.json(db.get('get_with_period_structure').value());
  });

  //
  // COPY TIMETABLE
  //
  server.post('/api/v1/timetable/Timetables/:id/Copy', (req, res) => {
    res.json({ id: Date.now() });
    // res.status(409).end();
  });

  //
  // DELETE TIMETABLE
  //
  server.delete('/api/v1/timetable/Timetables/:id', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // Get list of Period structure
  //
  server.get('/api/v1/Timetable/Timetables/periodStructure', (req, res) => {
    res.json(db.get('get_period_structure').value());
  });

  //
  // Get list of Period structure list - for dropdown
  //
  server.get('/api/v1/Timetable/Timetables/periodStructureList', (req, res) => {
    res.json(db.get('get_period_structure_list').value());
  });

  server.get('/api/v1/Timetable/Timetables/:id/WithPeriodStructure', (req, res) => {
    res.json(db.get('get_with_period_structure').value());
  });

  //
  // COPY PROJECT
  //
  server.post('/api/v1/timetable/projects/:id/Copy', (req, res) => {
    res.json({ id: Date.now() });
    // res.status(409).end();
  });

  //
  // DELETE PROJECT
  //
  server.delete('/api/v1/timetable/projects/:id', (req, res) => {
    res.status(204).end();
    // res.status(409).end();
  });

  //
  // Get list of Period structure
  //
  server.get('/api/v1/Timetable/Timetables/periodStructure', (req, res) => {
    res.json(db.get('get_period_structure').value());
  });

  //
  // Get list of Period structure list - for dropdown
  //
  server.get('/api/v1/Timetable/Timetables/PeriodStructures', (req, res) => {
    res.json(db.get('get_period_structure_list').value());
  });

  //
  // GET RELATIONSHIPS SUBJECTS
  //
  server.get('/api/v1/timetable/:projectId/subjects/Included', (req, res) => {
    res.json(db.get('get_relationships_subjects').value());
  });

  //
  // GET RELATIONSHIPS STAFFS
  //
  server.get('/api/v1/timetable/:projectId/staff/Included', (req, res) => {
    res.json(db.get('get_relationships_staffs').value());
  });

  //
  // GET RELATIONSHIPS ROOMS
  //
  server.get('/api/v1/timetable/:projectId/rooms/Included', (req, res) => {
    res.json(db.get('get_relationships_rooms').value());
  });

  //
  // GET STAFFS TO SUBJECT RELATIONS
  //
  server.get('/api/v1/timetable/:timetableId/relations/:subjectId/staffToSubjectRelations', (req, res) => {
    res.json(db.get('get_relationships_staff_to_subject').value());
  });

  //
  // POST STAFFS TO SUBJECT RELATIONS
  //
  server.post('/api/v1/timetable/:timetableId/relations/:subjectId/staffToSubjectRelations', (req, res) => {
    res.status(204).end();
  });

  //
  // DELETE STAFF TO SUBJECT RELATIONS
  //
  server.delete('/api/v1/timetable/:timetableId/relations/:subjectId/staffToSubjectRelations/:staffId', (req, res) => {
    res.status(204).end();
  });

  //
  // DELETE STAFFS TO SUBJECT RELATIONS
  //
  server.post('/api/v1/timetable/:timetableId/relations/:subjectId/staffToSubjectRelations/BulkDelete', (req, res) => {
    res.status(204).end();
  });

  //
  // GET ROOM TO SUBJECT
  //
  server.get('/api/v1/timetable/:timetableId/relations/:subjectId/roomToSubjectRelations', (req, res) => {
    res.json(db.get('get_relationships_room_to_subject').value());
  });

  //
  // POST ROOM TO SUBJECT
  //
  server.post('/api/v1/timetable/:timetableId/relations/:subjectId/roomToSubjectRelations', (req, res) => {
    res.status(204).end();
  });

  //
  // DELETE ROOM TO SUBJECT RELATIONS
  //
  server.delete('/api/v1/timetable/:timetableId/relations/:subjectId/roomToSubjectRelations/:roomId', (req, res) => {
    res.status(204).end();
  });

  //
  // DELETE ROOMS TO SUBJECT RELATIONS
  //
  server.post('/api/v1/timetable/:timeTableId/relations/:subjectId/roomToSubjectRelations/BulkDelete', (req, res) => {
    res.status(204).end();
  });

  //
  // GET RELATIONSHIPS ROOMS TO SUBJECT
  //
  server.get('/api/v1/timetable/:timetableId/relations/:subjectId/roomStaffRelations', (req, res) => {
    res.json(db.get('get_relationships_staffAndRoom_to_subject').value());
  });

  //
  // GET ALL RELATIONSHIPS ROOMS TO SUBJECT
  //
  server.get('/api/v1/timetable/:timetableId/relations/roomStaffRelations', (req, res) => {
    res.json(db.get('get_relationships_staffAndRoom_to_subject').value());
  });

  //
  // POST RELATIONSHIPS ROOMS TO SUBJECT
  //
  server.post('/api/v1/timetable/:timetableId/relations/:subjectId/roomStaffRelations', (req, res) => {
    res.status(204).end();
  });

  //
  // GET CURRICULUM PLAN SUBJECTS BY YEARGROUP
  //
  server.get('/api/v1/timetable/:timeTableId/subjects/Included/:yearGroupId', (req, res) => {
    res.json(db.get('get_relationships_subjects').value().slice(0, 1));
  });

  //
  // GET STAFFING TOTAL CONTACT TIMES
  //
  server.get('/api/v1/timeTable/:projectId/staff/:timetableId/totalContactTimes', (req, res) => {
    res.json(db.get('get_staffing_total_contact_times').value().slice(0, 1));
  });

  //
  // UPDATE STAFF TOTAL CONTACT TIME
  //
  server.put('/api/v1/timeTable/staff/:staffId/totalContactTimes', (req, res) => {
    res.status(204).end();
  });

  //
  // UPDATE STAFFING CONTACT TIMES PER YEAR GROUP
  //
  server.post('/api/v1/timeTable/:timetableId/staff/:staffId/totalContactTimes', (req, res) => {
    res.status(204).end();
  });

  //
  // GET STAFFING SUBJECT INFO TOTAL
  //
  server.get('/api/v1/TimeTable/:timetableId/YearGroups/TotalContactTimes', (req, res) => {
    res.json(db.get('get_staffing_subject_info_total').value());
  });

  //
  // GET STAFFING SUBJECT INFO TOTAL FILTERED
  //
  server.post('/api/v1/TimeTable/:timetableId/YearGroups/TotalContactTimes/Filter', (req, res) => {
    res.json(db.get('get_staffing_subject_info_total').value());
  });

  //
  // GET CURRICULUM PLAN ASSIGNED CONTACT TIMES STAFF
  //
  server.get('/api/v1/timetable/:timeTableId/staff/assignedContactTimes', (req, res) => {
    res.json(db.get('get_curriculum_plan_staffs').value());
  });

  //
  // GET CURRICULUM PLAN ASSIGNED CONTACT TIMES STAFF
  //
  server.get('/api/v1/timetable/:timeTableId/staff/assignedContactTimes/:yearGroupId', (req, res) => {
    res.json(db.get('get_curriculum_plan_staffs').value());
  });

  //
  // GET CURRICULUM PLAN ASSIGNED CONTACT TIMES STAFF
  //
  server.get('/api/v1/timetable/:timeTableId/staff/assignedContactTimes', (req, res) => {
    res.json(db.get('get_curriculum_plan_staffs').value());
  });

  //
  // GET CURRICULUM PLAN ASSIGNED CONTACT TIMES STAFF
  //
  server.get('/api/v1/timetable/:timeTableId/staff/assignedContactTimes/:yearGroupId', (req, res) => {
    res.json(db.get('get_curriculum_plan_staffs').value());
  });

  //
  // GET CURRICULUM PLAN ASSIGNED CONTACT TIMES ROOM
  //
  server.get('/api/v1/timetable/:timeTableId/rooms/assignedContactTimes', (req, res) => {
    res.json(db.get('get_curriculum_plan_rooms').value());
  });

  //
  // GET YEAR GROUPS TO SUBJECT INFORMATION
  //
  server.get('/api/v1/TimeTable/:projectId/YearGroups', (req, res) => {
    res.json(db.get('get_year_groups').value());
  });

  //
  // GET COUNT INFORMATION TO SUBJECT INFORMATION
  //
  server.get('/api/v1/TimeTable/:timeTableId/YearGroups/:yearGroupId/CountInformation', (req, res) => {
    res.json(db.get('get_count_information').value());
  });

  //
  // UPDATE COUNT INFORMATION TO SUBJECT INFORMATION
  //
  server.put('/api/v1/TimeTable/:timeTableId/YearGroups/:yearGroupId/CountInformation', (req, res) => {
    res.status(204).end();
  });

  //
  // GET list of blocks INFORMATION TO SUBJECT INFORMATION
  //
  server.get('/api/v1/TimeTable/:timeTableId/Blocks/:isPreview', (req, res) => {
    const response = [...Array(5).keys()].map((index) => generator.generateBlocks(db, index + 2)).flat();
    res.json([...db.get('get_curriculum_plan_list_of_blocks').value(), ...response]);
  });

  //
  // GET PREVIEW FOR CURRICULUM PLAN
  //
  server.get('/api/v1/TimeTable/:timeTableId/Blocks/:yearGroupId/:isPreview', (req, res) => {
    if (JSON.parse(req.params.isPreview)) {
      res.json(db.get('get_review_blocks_preview').value());
    } else {
      switch (req.params.yearGroupId) {
        case '23':
          res.json(db.get('get_curriculum_plan_list_of_blocks').value());
          break;
        default:
          res.json(generator.generateBlocks(db, req.params.yearGroupId));
      }
    }
  });

  //
  // CREATE block INFORMATION TO SUBJECT INFORMATION
  //
  server.post('/api/v1/TimeTable/:timetableId/Blocks/:yearGroupId', (req, res) => {
    const resp = {
      id: 3,
      blockTypeId: 1,
      subjectToYearGroups: [
        {
          subjectToYearGroupId: 1,
          yearGroupId: 25,
          subjectId: 25,
          classCount: 6,
          periodCount: 1
        }
      ]
    };
    res.json(db.get('create_block').value());
  });

  // GET preview TO SUBJECT INFORMATION
  //
  server.get('/api/v1/TimeTable/:timeTableId/Blocks/:yearGroupId/preview', (req, res) => {
    res.json(db.get('get_subject_information_preview').value());
  });

  //
  // GET REVIEW FEEDBACK
  //
  server.get('/api/v1/TimeTable/TimeTables/:timeTableId/Feedback', (req, res) => {
    res.json({
      "feedback": [
        {
          "message": "To make the most of this Block Planning page, you should complete Relationships, Subject Information and Staffing. Use the Block Planning page to create bands and generate blocks or proceed to Curriculum plan to build your blocks directly.",
          "isError": true
        }
      ]
    });
  });

  //
  // GET REVIEW FEEDBACK
  //
  server.get('/api/v1/TimeTable/TimeTables/:timeTableId/YearGroups', (req, res) => {
    res.json([
      {
        yearGroupId: 23,
        hasFeedback: true
      },
      {
        yearGroupId: 24,
        hasFeedback: false
      }
    ]);
  });

  //
  // GET REVIEW FEEDBACK BY YEAR GROUP
  //
  server.get('/api/v1/TimeTable/TimeTables/:timeTableId/Feedback/:yearGroupId', (req, res) => {
    // res.json({
    //   "feedback": [
    //     {
    //       "message": "English Language & Literature",
    //       "isError": true
    //     },
    //     {
    //       "message": "English Language & Literature (AS)",
    //       "isError": true
    //     },
    //     {
    //       "message": "English Literature (AS)",
    //       "isError": true
    //     },
    //     {
    //       "message": "BTEC Sport (Nat Dip)",
    //       "isError": true
    //     },
    //     {
    //       "message": "BTEC Sport (Fnd Dip)",
    //       "isError": true
    //     },
    //     {
    //       "message": "BTEC Sport (Ext Cert)",
    //       "isError": true
    //     },
    //     {
    //       "message": "Chemistry",
    //       "isError": true
    //     }
    //   ]
    // });

    res.json({
      "feedback": [
        {
          "message": "Staffing - Subject Relationships are fine for 0711",
          "isError": false
        }
      ]
    });

    // res.json({
    //   "feedback": []
    // });
  });

  //
  // GENERATE BLOCKS BY YEAR GROUP
  //
  server.put('/api/v1/TimeTable/:timetableId/Blocks/:yearGroupId/Generate', (req, res) => {
    res.status(204).end();
  });

  //
  // UPDATE BAND NAME
  //
  server.put('/api/v1/TimeTable/Bands/:bandId', (req, res) => {
    res.status(204).end();
  });

  //
  // BAND NAME UNIQUE
  //
  server.head('/api/v1/TimeTable/:timeTableId/Bands/:yearGroupId/:bandName/:excludedBandId/:isPreview', (req, res) => {
    if (['10A', '10B', '10C'].includes(req.params.bandName)) {
      res.status(422).end();
    } else {
      res.status(204).end();
    }
  });

  //
  // GET BANDS BY YEAR GROUP
  //
  server.get('/api/v1/TimeTable/:timeTableId/Bands/:yearGroupId/:isPreview', (req, res) => {
    const indexId = (req.params.yearGroupId - 1) * 4;
    if (req.params.yearGroupId != 23) {
      res.json([
          {
            id: indexId + 0,
            bandName: `${req.params.yearGroupId}A`,
            timeTableId: `${req.params.timeTableId}`,
            yearGroupId: `${req.params.yearGroupId}`
          },
          {
            id: indexId + 1,
            bandName: `${req.params.yearGroupId}B`,
            timeTableId: `${req.params.timeTableId}`,
            yearGroupId: `${req.params.yearGroupId}`
          },
          {
            id: indexId + 2,
            bandName: `${req.params.yearGroupId}C`,
            timeTableId: `${req.params.timeTableId}`,
            yearGroupId: `${req.params.yearGroupId}`
          },
          {
            id: indexId + 3,
            bandName: `${req.params.yearGroupId}D`,
            timeTableId: `${req.params.timeTableId}`,
            yearGroupId: `${req.params.yearGroupId}`
          }
        ]
      )
    } else {
      res.json([{
        id: 1000,
        bandName: `10B`,
        timeTableId: `${req.params.timeTableId}`,
        yearGroupId: `23`
      },
        {
          id: 1001,
          bandName: `10C`,
          timeTableId: `${req.params.timeTableId}`,
          yearGroupId: `23`
        }
      ]);
    }
  });

  //
  // GET ALL BANDS
  //
  server.get('/api/v1/TimeTable/:timeTableId/Bands/:isPreview', (req, res) => {
    const response = [...Array(12).keys()].map((index) => {
      const yearGroupId = index;
      return [
        {
          id: (yearGroupId * 4) + 0,
          bandName: `${yearGroupId + 1}A`,
          timeTableId: `${req.params.timeTableId}`,
          yearGroupId: `${yearGroupId + 1}`
        },
        {
          id: (yearGroupId * 4) + 1,
          bandName: `${yearGroupId + 1}B`,
          timeTableId: `${req.params.timeTableId}`,
          yearGroupId: `${yearGroupId + 1}`
        },
        {
          id: (yearGroupId * 4) + 2,
          bandName: `${yearGroupId + 1}C`,
          timeTableId: `${req.params.timeTableId}`,
          yearGroupId: `${yearGroupId + 1}`
        },
        {
          id: (yearGroupId * 4) + 3,
          bandName: `${yearGroupId + 1}D`,
          timeTableId: `${req.params.timeTableId}`,
          yearGroupId: `${yearGroupId + 1}`
        }
      ]
    }).flat();

    const yg23 = [{
      id: 1000,
      bandName: `10B`,
      timeTableId: `${req.params.timeTableId}`,
      yearGroupId: `23`
    },
      {
        id: 1001,
        bandName: `10C`,
        timeTableId: `${req.params.timeTableId}`,
        yearGroupId: `23`
      }
    ]


    res.json([...yg23, ...response]);
  });

  //
  // ADD BAND BY YEAR GROUP
  //
  server.post('/api/v1/TimeTable/:timeTableId/Bands/:yearGroupId/', (req, res) => {
    res.json({
      id: 4,
      bandName: `${req.params.yearGroupId}D`,
      timeTableId: `${req.params.timeTableId}`,
      yearGroupId: `${req.params.yearGroupId}`
    });
  });

  //
  // UPDATE BAND NAME
  //
  server.put('/api/v1/TimeTable/Bands/:bandId', (req, res) => {
    res.status(204).end();
  });

  //
  // DELETE BAND
  //
  server.delete('/api/v1/TimeTable/Bands/:bandId', (req, res) => {
    res.status(204).end();
  });

  //
  // COPY BAND STRUCTURE
  //
  server.post('/api/v1/TimeTable/Blocks/BulkCopy', (req, res) => {
    res.status(204).end();
  });


  //
  // REMOVE ALL BLOCKS FROM BAND
  //
  server.post('/api/v1/TimeTable/Blocks/BulkDelete', (req, res) => {
    res.status(204).end();
  });

  //
  // POST CURRICULUM PLAN NEW BLOCK
  //
  server.post('/api/v1/TimeTable/:timeTableId/blocks', (req, res) => {
    res.json({
      blockId: Date.now(),
      blockName: `Name${Math.round(Math.random() * 100)}`,
      blockCode: `Code${Math.round(Math.random() * 100)}`
    });
  });

  //
  // GET CURRICULUM PLAN BLOCK
  //
  server.get('/api/v1/TimeTable/Blocks/:blockId', (req, res) => {
    res.json(db.get('get_curriculum_plan_list_of_blocks').value()[0]);
  });

  //
  // ADD SUBJECT TO CURRICULUM PLAN BLOCK
  //
  server.put('/api/v1/TimeTable/Blocks/Subject', (req, res) => {
    res.status(204).end();
  });

  //
  // SWAP SUBJECT ON CURRICULUM PLAN BLOCK
  //
  server.post('/api/v1/TimeTable/Blocks/SwapSubject', (req, res) => {
    res.status(204).end();
  });

  //
  // DELETE CURRICULUM PLAN BLOCK
  //
  server.delete('/api/v1/TimeTable/Blocks/:blockId/:permanentlyDelete', (req, res) => {
    res.status(204).end();
  });

  //
  // ASSIGN MAIN STAFF TO BLOCK
  //
  server.put('/api/v1/TimeTable/Sessions/Staff/Main', (req, res) => {
    const response = req.body.sessionIds.map((sessionId, index) => ({
      sessionId: sessionId,
      mainStaffId: req.body.staffId,
      roomId: index + 1
    }));
    res.json(response).end();
  });

  //
  // ASSIGN ADDITIONAL STAFF TO BLOCK
  //
  server.put('/api/v1/TimeTable/Sessions/Staff/Additional', (req, res) => {
    res.status(204).end();
  });

  //
  // ASSIGN ROOM TO BLOCK
  //
  server.put('/api/v1/TimeTable/Sessions/Room', (req, res) => {
    res.status(204).end();
  });

  //
  // ASSIGN PERIOD TO SESSIONS
  //
  server.post('/api/v1/TimeTable/Sessions/Schedule', (req, res) => {
    res.status(204).end();
  });

  //
  // REMOVE STAFF FROM SESSIONS
  //
  server.post('/api/v1/TimeTable/Sessions/Staff/BulkDelete', (req, res) => {
    res.status(204).end();
  });

  //
  // REMOVE ROOM FROM SESSIONS
  //
  server.post('/api/v1/TimeTable/Sessions/Room/BulkDelete', (req, res) => {
    res.status(204).end();
  });

  //
  // CLEAR SESSIONS
  //
  server.post('/api/v1/TimeTable/Sessions/Clear', (req, res) => {
    res.status(204).end();
  });

  //
  // COPY BLOCK TO BAND
  //
  server.post('/api/v1/TimeTable/Blocks/:blockId/CopyToBand', (req, res) => {
    res.json({ id: Date.now() })
  });

  //
  // UNSCHEDULE SESSIONS
  //
  server.post('/api/v1/TimeTable/Sessions/Unschedule', (req, res) => {
    res.status(204).end();
  });

  //
  // REMOVE SUBJECT FROM BLOCK
  //
  server.post('/api/v1/TimeTable/Blocks/Subject/Delete', (req, res) => {
    res.status(204).end();
  });

  //
  // REMOVE CLASS FROM BLOCK
  //
  server.post('/api/v1/TimeTable/Sessions/BulkDelete/Class', (req, res) => {
    res.status(204).end();
  });

  //
  // COPY BLOCK TO BAND
  //
  server.post('/api/v1/TimeTable/Blocks/:blockId/CopyToBand', (req, res) => {
    res.json({ id: Date.now() })
  });

  //
  // SPREAD BLOCK TO BAND
  //
  server.post('/api/v1/TimeTable/Blocks/:blockId/SpreadToBands', (req, res) => {
    res.status(204).end();
  });

  //
  // SPLIT BLOCK TO BAND
  //
  server.post('/api/v1/TimeTable/Blocks/:blockId/SplitToBands', (req, res) => {
    res.status(204).end();
  });

  //
  // ADD CLASS TO SESSION
  //
  server.post('/api/v1/TimeTable/Sessions/Class', (req, res) => {
    res.status(204).end();
  });

  //
  // REMOVE CLASS FROM BLOCK
  //
  server.post('/api/v1/TimeTable/Sessions/BulkDelete/Class', (req, res) => {
    res.status(204).end();
  });

  // GET BLOCKS TO REVIEW PAGE
  //
  server.get('/api/v1/TimeTable/:timeTableId/Blocks/:isPreview', (req, res) => {
    res.json(db.get('get_blocks_review').value());
  });


  //
  // GET SPLIT BLOCKS TO REVIEW PAGE
  //
  server.get('/api/v1/TimeTable/:timetableId/Blocks/:yearGroupId/SplitBlocks', (req, res) => {
    res.json([16402, 16403, 16404, 16405, 16406, 16407]);
  });

  //
  // RESET SPLIT TO REVIEW PAGE
  //
  server.post('/api/v1/TimeTable/:timeTableId/Blocks/:yearGroupId/ResetSplit', (req, res) => {
    res.status(204).end();
  });

  //
  // ADD CLASS TO BLOCK
  //
  server.post('/api/v1/TimeTable/Sessions/Class', (req, res) => {
    res.status(204).end();
  });

  //
  // ADD PERIOD
  //
  server.post('/api/v1/TimeTable/Sessions/Period', (req, res) => {
    res.status(204).end();
  });

  //
  // REMOVE PERIOD
  //
  server.post('/api/v1/TimeTable/Sessions/BulkDelete/Period', (req, res) => {
    res.status(204).end();
  });

  //
  // LOCK AND UNLOCK SESSIONS
  //
  server.put('/api/v1/TimeTable/Sessions/Lock', (req, res) => {
    res.status(204).end();
  });

  //
  // GET CLASS NAME DEFINITION YEAR GROUP LIST
  //
  server.get('/api/v1/TimeTable/:timetableId/ClassNames/YearGroups', (req, res) => {
    res.json(db.get('get_class_name_definition_year_group_list').value());
  });

  //
  // GET CLASS NAME DEFINITION ITEMS
  //
  server.get('/api/v1/TimeTable/ClassNames', (req, res) => {
    res.json(db.get('get_class_name_definition_items').value());
  });

  //
  // GET CLASS NAME DEFINITION BY YEAR GROUP
  //
  server.get('/api/v1/TimeTable/:timetableId/ClassNameDefinition/:yearGroupId', (req, res) => {
    res.json(db.get('get_class_name_definition_by_year_group').value());
  });

  //
  // SET CLASS NAME DEFINITION BY YEAR GROUP
  //
  server.post('/api/v1/TimeTable/:timetableId/ClassNameDefinition/:yearGroupId', (req, res) => {
    res.status(204).end();
  });

  //
  // COPY CLASS NAME DEFINITION TO YEAR GROUP
  //
  server.post('/api/v1/TimeTable/:timetableId/ClassNameDefinition/copy', (req, res) => {
    res.status(204).end();
  });

  //
  // CHECK CLASS NAME DEFINITION TO YEAR GROUP
  //
  server.post('/api/v1/TimeTable/:timetableId/ClassNameDefinition/:yearGroupId/Validation', (req, res) => {
    res.json({ hasAnyDuplicates: true });
  });

  //
  // GET BACKUPS
  //
  server.get('/api/v1/TimeTable/TimeTables/:timeTableId/Versions', (req, res) => {
    res.json([
      {
        "id": 1,
        "backupName": " V.1",
        "versionNumber": 1,
        "description": "v1",
        "createdDate": "2023-09-13T14:28:14.34"
      }]);
  });

  //
  // CREATE BACKUP
  //
  server.post('/api/v1/TimeTable/TimeTables/:timeTableId/Versions', (req, res) => {
    res.json({ "name": 'V.1' });
  });

  //
  // UPDATE BACKUP
  //
  server.put('/api/v1/TimeTable/TimeTables/Versions/:versionId', (req, res) => {
    res.status(204).end();
  });

  //
  // COPY AS NEW MAIN BACKUP
  //
  server.post('/api/v1/TimeTable/TimeTables/Versions/:versionId/NewMaster', (req, res) => {
    res.status(204).end();
  });

  //
  // RESTORE BACKUP
  //
  server.post('/api/v1/TimeTable/TimeTables/:timeTableId/Versions/:versionId/Restore', (req, res) => {
    res.json({ "id": 185 });
  });

  //
  // GET CONFLICTS
  //
  server.get('/api/v1/TimeTable/:timetableId/Conflicts', (req, res) => {
    res.json({
      "conflicts": [],
      "roomConflictedSessionIds": [],
      "staffConflictedSessionIds": []
    });
  });

  //
  // ACCEPT CONFLICTS
  //
  server.put('/api/v1/TimeTable/Conflicts/Bulk/IsAccepted', (req, res) => {
    res.json({
      "conflicts": [],
      "roomConflictedSessionIds": [],
      "staffConflictedSessionIds": []
    });
  });

};
