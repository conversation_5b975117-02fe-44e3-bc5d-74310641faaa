@import 'apps/bromcom-timetables/src/assets/styles/variables';
@import 'apps/bromcom-timetables/src/app/planning-relationships/pages/planning-relationships/components/scheduling/components/timetable-specification/styles/tts-ag-grid';

.tts-subject-on-days-container {
  padding: 24px 0 0;

  .action-bar {
    @include action-bar;
  }

  .table-container {
    @include table-container;
  }

  ::ng-deep .ag-popup-editor {
    background-color: transparent;
    z-index: 10000;
    min-width: 100px;
    // -48 for grid checkbox width
    // 7.3 for gridoption columns flex sum
    // -34 for inner padding
    width: calc(((100% - 48px) / 7.3 * 1.5) - 34px);
    margin: -44px 16px 0;
  }

  ::ng-deep .mat-expansion-panel.new-row {
    margin-bottom: -32px;
  }

  ::ng-deep .mat-expansion-panel-header-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    .title-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  ::ng-deep .ag-header-cell.left-padding {
    padding-right: 0;
  }
}
