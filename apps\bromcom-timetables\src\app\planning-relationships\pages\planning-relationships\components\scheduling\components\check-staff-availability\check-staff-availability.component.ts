import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { BaseModalComponent } from '../../../../../../../_shared/components/BaseModalComponent';
import { IWithPeriodStructureResponse } from '../../../../../../../_shared/models/IWithPeriodStructureResponse';
import { ITimetableWeek } from '../../../../../../../_shared/models/ITimetableWeek';
import { ITimetableDayListItem } from '../../../../../../../_shared/models/ITimetableDayListItem';
import { ITimetableDayPeriod } from '../../../../../../../_shared/models/ITimetableDayPeriod';
import { ICurriculumPlanStaff } from '../../../../../../../_shared/models/ICurriculumPlanStaff';
import { isBreak } from '../../scheduling.helper';
import { ISessionWithBlocks } from '../../../../../../../_shared/models/ISessionWithBlocks';
import { IListOfBlocksResponse } from '../../../../../../../_shared/models/IListOfBlocksResponse';
import { ISubject } from '../../../../../../../_shared/models/ISubject';
import { isColorDark } from '@bromcom/ui';
import { INonContactCodesAssociation } from '../../../../../../../_shared/models/INonContactCodesAssociation';
import { INonContactCodes } from '../../../../../../../_shared/models/INonContactCodes';

const defaultBackground = 'f8fafc';

@Component({
  selector: 'bromcom-check-staff-availability',
  templateUrl: './check-staff-availability.component.html',
  styleUrls: ['./check-staff-availability.component.scss'],
})

export class CheckStaffAvailabilityComponent extends BaseModalComponent implements OnInit {
  @ViewChild('checkAvailabilityModal') checkAvailabilityModalRef!: ElementRef;

  @Input() set periods(periods: IWithPeriodStructureResponse) {
    this.weeks = periods?.periodStructure.weeks || [];
    this.periodData = periods;
    this.preprocessDaysAndPeriods();
  }

  @Input() set blocks(blocks: IListOfBlocksResponse[] | null) {
    this._blocks = blocks || [];
    this.setSessions();
  }

  @Input() set subjects(subjects: ISubject[] | null) {
    this._subjects = subjects || [];
  }
  
  @Input() set nonContactCodesAssociations(nonContactCodes: INonContactCodesAssociation[]) {
    this._nonContactCodesAssociations = nonContactCodes;
  }

  get nonContactCodesAssociations(): INonContactCodesAssociation[] {
    return this._nonContactCodesAssociations!;
  }
  
  @Input() set nonContactCodes(nonContactCodes: INonContactCodes[] | null) {
    this._nonContactCodes = nonContactCodes;
  }

  get nonContactCodes(): INonContactCodes[] {
    return this._nonContactCodes!;
  }

  weeks: ITimetableWeek[] = [];
  selectedStaffs: ICurriculumPlanStaff[] = [];
  sessions: ISessionWithBlocks[] = [];
  periodData!: IWithPeriodStructureResponse;
  textColor!: string;
  private _blocks!: IListOfBlocksResponse[];
  private _subjects!: ISubject[];
  private _nonContactCodesAssociations!: INonContactCodesAssociation[];
  private _nonContactCodes!: INonContactCodes[] | null;

  daysByWeek: Map<number, ITimetableDayListItem[]> = new Map();
  periodsByDay: Map<number, ITimetableDayPeriod[]> = new Map();

  ngOnInit(): void {
    this.setSessions();
  }

  identify(index: number, item: any) {
    return item?.id ?? index;
  }

  preprocessDaysAndPeriods(): void {
    this.daysByWeek.clear();
    this.periodsByDay.clear();

    this.weeks.forEach((week) => {
      const days = week.days.map((day) => ({
        ...day,
        text: `${day.dayDisplayName} ${week.weekNumber}`,
        id: week.weekNumber * 10 + day.dayOfWeek,
      }));

      this.daysByWeek.set(week.weekNumber, days);

      days.forEach((day) => {
        const periods = day.periods.filter(
          (period) => !isBreak(period.periodCode)
        );
        this.periodsByDay.set(day.id, periods);
      });
    });
  }

  getDays(week: ITimetableWeek): ITimetableDayListItem[] {
    return this.daysByWeek.get(week.weekNumber) || [];
  }

  getPeriods(day: ITimetableDayListItem): ITimetableDayPeriod[] {
    return this.periodsByDay.get(day.id!)?.map(p => {
      return {
        ...p,
        text: `P${p.periodDisplayName}`,
        id: p.id,
        isLunch: p.isLunch,
      }
    }) || [];
  }

  getSessions(period: ITimetableDayPeriod, staff: ICurriculumPlanStaff): ISessionWithBlocks[] {
    return this.sessions.filter((session) => session.periodId === period.id && (session.mainStaffId === staff.id ||
    session.additionalStaffIds?.includes(staff.id!))) || [];
  }
  
  getNonContactCodes(period: ITimetableDayPeriod, staff: ICurriculumPlanStaff) {
    return this._nonContactCodesAssociations.filter(n => {
      return n.staffId === staff.id && n.periodId === period.id
    }).map(nc => {
      const associations = this.nonContactCodesAssociations.filter(nca => {
        return nca.nonContactCodeGroupId != null && nca.nonContactCodeGroupId === nc.nonContactCodeGroupId && nca.staffId != null;
      });
      return { 
        ...nc,
        codeName: this._nonContactCodes?.find(code => code.id === nc.nonContactCodeId)?.codeName,
        isMultiStaffAssociated: associations.length > 1 }
    });
  }

  private setSessions(): void {
    this.sessions = this._blocks
  .flatMap((block) => 
    block.sessions?.map((session) => {
      const sessionSubjectId = block.subjectToYearGroups?.find(
        (subjectToYearGroup) => subjectToYearGroup.id === session.subjectToYearGroupId
      )?.subjectId;

      return {
        ...session,
        block,
        sessionName: this._subjects?.find((sub) => sub.id === sessionSubjectId)?.code?.toUpperCase() ?? block.blockCode!,
      };
    }) ?? []
  )
  .filter((session) => 
    this.selectedStaffs.some(
      (staff) =>
        session.mainStaffId === staff.id ||
        session.additionalStaffIds?.includes(staff.id!)
    )
  );
  }

  getColor(session: ISessionWithBlocks): string {
    const subjectId =
      session.block.subjectToYearGroups?.find(
        (group) => group.id === session.subjectToYearGroupId
      )?.subjectId || null;
    const backgroundColor = this._subjects.find((subject) => subject.id === subjectId)?.color || defaultBackground;
    this.textColor = isColorDark(backgroundColor) ? '#FFF' : '#000';
    return backgroundColor.startsWith('#') ? backgroundColor : `#${backgroundColor}`;
  }

  show(data: ICurriculumPlanStaff[]): void {
    this.selectedStaffs = data.map((staff) => ({
      ...staff,
      name: staff.name.replace(/\s?\(.*?\)/, ''),
    }));
    this.isOpen = true;
    this.setSessions();
    setTimeout(() => this.checkAvailabilityModalRef.nativeElement.show(), 100);
  }

  close(): void {
    this.isOpen = false;
    this.checkAvailabilityModalRef.nativeElement.hide();
  }
}
