<ng-container *ngIf="isOpen">
  <bcm-modal size="xxlarge" backdrop class="overview" #overviewModal (bcm-modal-before-close)="onClose()">
    <bcm-modal-header>{{ 'Curriculum Plan Overview' | translate }}</bcm-modal-header>

    <div class="modal-body">
      <div class="action-bar_">
        <div class="block">
          <bromcom-filter-list-chips
            id="yearGroupFilter"
            [dataItems]="{ items: yearGroups, startState: false }"
            label="{{ 'Year Groups' | translate }}"
            [placeholder]="'Year Groups' | translate"
            [appearance]="'checkbox'"
            [selection]="selectedYearGroupIds"
            [enableApplyFilterWithDefaultSelection]="true"
            [selectAllOnClearedApply]="false"
            (filterData)="onApplyYeargroupFilter($event)"
          ></bromcom-filter-list-chips>

          <bromcom-filter-list-chips
            id="departmentsFilter"
            [dataItems]="{ items: departmentList, startState: false }"
            label="{{ 'Departments' | translate }}"
            [placeholder]="'Departments' | translate"
            [appearance]="'checkbox'"
            [selection]="selectedDepartmentIds"
            [enableApplyFilterWithDefaultSelection]="true"
            [selectAllOnClearedApply]="false"
            (filterData)="onApplyDepartmentFilter($event)"
          ></bromcom-filter-list-chips>

          <bromcom-filter-list-chips
            [dataItems]="{ items: subjectList, startState: false }"
            label="{{ 'Subjects' | translate }}"
            [placeholder]="'Subjects' | translate"
            [appearance]="'checkbox'"
            [selection]="selectedSubjectIds"
            [enableApplyFilterWithDefaultSelection]="true"
            [selectAllOnClearedApply]="false"
            [chipTemplate]="chipTemplate"
            (filterData)="onApplySubjectFilter($event)"
          ></bromcom-filter-list-chips>

          <div class="action-buttons_">
            <bcm-button-group class="button-group" type="radio">
              <bcm-button
                class="action-button"
                kind="ghost"
                [attr.checked]="selectedOrganisedByType === organisedByTypes.YearGroup"
                (click)="organiseBy(organisedByTypes.YearGroup)"
              >
                <bcm-icon icon="far fa-calendar-alt"></bcm-icon>
              </bcm-button>
              <bcm-button
                class="action-button"
                kind="ghost"
                [attr.checked]="selectedOrganisedByType === organisedByTypes.Teacher"
                (click)="organiseBy(organisedByTypes.Teacher)"
              >
                <bcm-icon icon="fal fa-users-class"></bcm-icon>
              </bcm-button>
              <bcm-button
                class="action-button"
                kind="ghost"
                [attr.checked]="selectedOrganisedByType === organisedByTypes.Room"
                (click)="organiseBy(organisedByTypes.Room)"
              >
                <bcm-icon icon="fas fa-map-marker-alt"></bcm-icon>
              </bcm-button>
            </bcm-button-group>
          </div>
          <bcm-button class="button export-button" icon="far fa-file-export" kind="ghost" (click)="export()">
            {{ 'Export as Grid' | translate }}
          </bcm-button>
        </div>
      </div>

      <div class="overview-container">
        <div class="table-container" *ngFor="let yearGroup of overviewData">
          <table [ngStyle]="{'background' : 'url(' + tableBackgroundURL +')'}">
            <ng-container *ngFor="let row of yearGroup.cells; let rowCount = count; let rowIndex = index">
              <tr>
                <ng-container *ngFor="let col of row; let i = index">
                  <td
                    *ngIf="i !== 0 || rowIndex === 0"
                    [attr.rowspan]="rowIndex === 0 && i === 0 ? rowCount : 1"
                    [ngStyle]="{ 'background-color': '#' + (col.color ?? 'none') }"
                    [ngClass]="{
                      'white-color': isColorDarkHelper(col.color),
                      'yeargroup-header': rowIndex === 0 && i === 0
                    }"
                  >
                    <bcm-tooltip *ngIf="col.content" [message]="showToolTip(col)"
                                 trigger="hover"
                                 class="tooltip"
                                 color="slate">
                      <span *ngIf="col.content" [ngClass]="col.cellType === 'PeriodCount'? (+col.content  > totalBandCount ? 'count-span-red' : 'count-span') : null">{{ col.content }}</span>
                    </bcm-tooltip>
                    <span *ngIf="!col.content">&nbsp;</span>
                  </td>
                </ng-container>
              </tr>
            </ng-container>
          </table>
        </div>
      </div>
    </div>

    <bcm-modal-footer>
      <bcm-button data-dismiss kind="ghost">{{ 'Close' | translate }}</bcm-button>
    </bcm-modal-footer>
  </bcm-modal>
</ng-container>
