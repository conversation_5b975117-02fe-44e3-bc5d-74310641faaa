# Project conventional

## How to build and run the project on production mode.

Run `npm run time-tables:build:prod` to build the project. 

The build artifacts will be stored in the `dist/apps/bromcom-timetables` directory.

## How to build and run the project on development mode.

Run `npm run time-tables:build:dev` to build the project. 

The build artifacts will be stored in the `dist/apps/bromcom-timetables` directory.

## How to run project

Run `npm run time-tables:serv`

*It'll run the project on `http://localhost:4200/`*

## How to run mock server

Run `npm run time-tables:mock`

*It'll run the mock server on `http://localhost:9988/`*

