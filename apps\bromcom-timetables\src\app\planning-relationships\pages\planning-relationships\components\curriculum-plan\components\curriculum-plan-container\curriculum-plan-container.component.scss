@import '../../../../../../../../assets/styles/variables';

.curriculum-plan-container {
  height: 100%;

  .main-actions {
    height: 48px;
    display: flex;
    justify-content: space-between;
    overflow: auto;

    .add-actions {
      display: flex;

      .dropdown,
      .button,
      bromcom-dropdown-menu {
        width: 90px;
        margin-right: 8px;
      }

      bromcom-dropdown-menu {
        margin-right: 16px;
      }
    }

    .select-actions {
      display: flex;

      .list {
        width: 256px;
      }

      .list:not(:last-child) {
        margin-right: 8px;
      }
    }
  }

  .band-selector-container {
    width: 40px;
    height: calc(100% - 48px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 18px 14px;
    background: $color-blue-grey-100;

    border: 1px solid $color-blue-grey-200;
    border-radius: 4px;

    ::ng-deep .band-prev,
    ::ng-deep .band-next,
    ::ng-deep .band-actions {
      text-align: center;

      .icon {
        cursor: pointer;
        color: $color-blue-grey-700;

        i.fa-arrow-to-bottom,
        i.fa-arrow-to-top,
        i.far fa-pen,
        i.fa-trash-alt {
          font-size: 16px;
        }
      }
    }

    .band-main-content {
      height: 78px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;

      .band-name {
        font-size: 16px;
      }

      .band-actions {
        height: 44px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
      }
    }
  }

  ::ng-deep .list-circle {
    display: block;
    width: 20px;
    height: 20px;
    margin-left: 4px;
    margin-right: 8px;
    border-radius: 100px;
  }

  .planning-area {
    height: calc(100% - 48px);
    display: flex;
    overflow: hidden;
  }

  .band-side-panel {
    height: 100%;
  }

  .blocks-container {
    height: 100%;
    width: 100%;
    flex-grow: 1;
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    align-content: flex-start;
    overflow: auto;

    .block-row {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;

      &.hidden {
        height: 0;
      }

      .option-block,
      .complex-block {
        height: fit-content;
      }
    }
  }
}

@media screen and (max-width: 1365px) {
  .curriculum-plan-container {
    .main-actions {
      .select-actions {
        display: flex;

        .block-type-list {
          width: 224px;
        }
      }
    }
  }
}

@media screen and (max-width: 1279px) {
  .curriculum-plan-container {
    .main-actions {
      .select-actions {
        display: flex;

        .block-type-list {
          width: 112px;
        }
      }
    }
  }
}
