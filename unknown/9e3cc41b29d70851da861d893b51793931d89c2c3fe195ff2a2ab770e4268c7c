<p>{{'Preview' | translate}}</p>

<div class="table-container">
  <div class="preview" *ngIf="previewData">
    <div class="badge">
      {{'Cycle' | translate}}: {{previewData.numberOfWeeksInCycle}}&nbsp;
      <span *ngIf="previewData.numberOfWeeksInCycle && +previewData.numberOfWeeksInCycle === 1">
        {{ 'week' | translate}},&nbsp;
        </span>
      <span *ngIf="previewData.numberOfWeeksInCycle && +previewData.numberOfWeeksInCycle> 1">
       {{ 'weeks' | translate}},&nbsp;
        </span>
      <span>
      {{previewData.numberOfDaysInCycle}}&nbsp;
      </span>
      {{ 'days' | translate}},&nbsp;
      <span>
         {{previewData.numberOfPeriodsInCycle}}&nbsp;
      </span>
      {{'periods' | translate}}
    </div>
  </div>

  <table [hidden]="!hasAnyPreviewBlocks()">
    <tr *ngFor="let row of structure">
      <td *ngFor="let item of row"
          [attr.colspan]="item?.colspan"
          [attr.rowspan]="item?.rowspan"
          [ngClass]="{
            'group': item?.type === CELL_TYPE.Group,
            'option': item?.type === CELL_TYPE.Option,
            'linear': item?.type === CELL_TYPE.Linear,
            'complex': item?.type === CELL_TYPE.Complex || item?.type === CELL_TYPE.FreeSubjectInComplex}"
          [ngStyle]="{
            'background-color': item?.color? item.color : '#' + getSubject(item?.subjectId)?.color,
            'width': getCellWidth(item)
          }">
        <span *ngIf="item?.type === CELL_TYPE.FreeSubjectInComplex">
          --
        </span>
        <span *ngIf="item?.type === CELL_TYPE.Simple ||
                     item?.type === CELL_TYPE.Linear ||
                     item?.type === CELL_TYPE.Option ||
                     item?.type === CELL_TYPE.Complex"
              [ngClass]="{'white-color': isColorDarkHelper(getSubject(item?.subjectId)?.color)}">
          {{getSubject(item.subjectId)?.code | uppercase}}
        </span>
        <div *ngIf="item?.type === CELL_TYPE.Group" class="group-wrapper">
          <div class="circle">
            {{item.title}}</div>
        </div>
        <span *ngIf="item?.type === CELL_TYPE.Title">{{item.name}}</span>
        <span *ngIf="item?.type === CELL_TYPE.PeriodPerCycle">{{item.value}}</span>
        <span *ngIf="item?.type === CELL_TYPE.TotalPeriodCount">{{item.value}}</span>
        <span *ngIf="item?.type === CELL_TYPE.TotalPeriodCycle" [ngClass]="{'isGreater': previewData.numberOfPeriodsInCycle < item.value}">{{'Total' | translate}}: {{item.value}}</span>
        <span *ngIf="item?.type === CELL_TYPE.Empty"></span>
      </td>
    </tr>
  </table>

  <table [hidden]="hasAnyPreviewBlocks()" class="empty-table">
    <tbody>
    <tr>
      <td class="gray-cell">{{'Class' | translate}}</td>
      <td class="gray-cell" *ngFor="let cell of array13; let i = index"></td>
    </tr>
    <ng-container *ngIf="shouldDisplayDefaultClassRows">
      <tr *ngFor="let row of dynamicArray; let i = index">
        <td class="gray-cell">{{ yearGroupName | zeroPadding}}{{ i + 1 | zeroPadding}}</td>
        <td class="white-cell" *ngFor="let cell of array12"></td>
        <td class="gray-cell"></td>
      </tr>
    </ng-container>
    <tr>
      <td class="gray-cell">{{'Per Cycle' | translate}}</td>
      <td class="gray-cell" *ngFor="let cell of array13"></td>
    </tr>
    <tr>
      <td class="gray-cell">{{'Total' | translate}}</td>
      <td class="gray-cell" *ngFor="let cell of array13"></td>
    </tr>
    </tbody>
  </table>
</div>
