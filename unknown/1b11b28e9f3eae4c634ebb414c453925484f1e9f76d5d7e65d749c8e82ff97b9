@import 'apps/bromcom-timetables/src/assets/styles/variables';

@mixin displayFlexRow {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

@mixin action-bar() {
  @include displayFlexRow;
  align-items: center;

  .search-field {
    width: 256px;
  }

  .action-buttons {
    @include displayFlexRow;
    margin-bottom: 8px;

    .button {
      margin: 0 4px;
    }

    .button-group {
      margin-left: 16px;

      ::ng-deep &.bcm-button-group {
        border: none;
      }
    }
  }
}

@mixin table-container() {
  height: 45vh;

  ::ng-deep .ag-cell {
    height: 56px;
    padding: 12px 16px;
    background: white;
    border: none;
  }

  ::ng-deep .ag-cell-inline-editing,
  ::ng-deep .ag-cell-not-inline-editing:not(:last-child) {
    box-shadow: none;
    border-right: 1px solid $color-blue-grey-200;
    border-bottom: 1px solid $color-blue-grey-200;
    border-color: $color-blue-grey-200 !important;
    border-radius: 0;
  }

  ::ng-deep .ag-cell-not-inline-editing {
    border-bottom: 1px solid $color-blue-grey-200;
    display: flex;
    align-items: center;
  }

  ::ng-deep .ag-row-pinned .ag-cell-inline-editing,
  ::ng-deep .ag-row-pinned .ag-cell-not-inline-editing {
    border-bottom: 0;
  }

  ::ng-deep .ag-header-cell {
    border-right: 1px solid $color-blue-grey-200;
  }

  .bcm-chip__size-medium {
    font-size: 16px !important;
  }

  ::ng-deep .no-pointer-events .ag-floating-bottom-full-width-container .ag-row,
  ::ng-deep .no-pointer-events .ag-row-not-inline-editing {
    pointer-events: none;
  }

  ::ng-deep .ag-row-editing {
    pointer-events: auto !important;
  }
}
