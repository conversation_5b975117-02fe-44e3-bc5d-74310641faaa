import { Component, ElementRef, Input, ViewChild, HostListener } from '@angular/core';
import { GridOptions, GridReadyEvent, ICellRendererParams } from 'ag-grid-community';
import { GridApi } from 'ag-grid-community/dist/lib/gridApi';
import { FormControl } from '@angular/forms';
import { INCLUDED_TYPES } from '../../../../../../../../../_shared/enums/IncludedTypes';
import { Subject, switchMap, takeUntil } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@bromcom/ui';
import { gridOptions } from './tts-staff-on-days-gridoptions';
import { ITTSStaffOnDays } from '../../../../../../../../../_shared/models/ITTSStaffOnDays';
import { IStaff } from '../../../../../../../../../_shared/models/IStaff';
import { TimetableSpecificationsService } from '../../../../../../../../services/timetable-specifications.service';
import { RelationshipsService } from '../../../../../../../../services/relationships.service';
import { GeneralModalComponent } from '../../../../../../../../../_shared/components/general-modal/general-modal.component';
import { StaffService } from '../../../../../../../../../projects/services/staff.service';
import { IWithPeriodStructureResponse } from '../../../../../../../../../_shared/models/IWithPeriodStructureResponse';
import { NewTimetableService } from '../../../../../../../../../timetables/services/new-timetable.service';


@Component({
  selector: 'bromcom-tts-staff-on-days',
  templateUrl: './tts-staff-on-days.component.html',
  styleUrls: ['./tts-staff-on-days.component.scss']
})
export class TtsStaffOnDaysComponent {
  @ViewChild('ttsStaffRelationshipsModal', { static: false }) ttsStaffRelationshipsModal!: ElementRef;
  @ViewChild('deleteStaffOnDaysWarningModal') deleteStaffOnDaysWarningModal!: GeneralModalComponent;
  @ViewChild('excludeSubjectRelationshipWarningModal') excludeSubjectRelationshipWarningModal!: GeneralModalComponent;

  @Input() projectId!: number;
  @Input() timetableId!: number;

  params!: GridReadyEvent;
  gridApi!: GridApi;
  gridOptions!: GridOptions;
  searchControl = new FormControl(null);
  INCLUDED_TYPES = INCLUDED_TYPES;
  viewType: INCLUDED_TYPES = INCLUDED_TYPES.ACTIVE;
  deleteRowId!: number;
  dayList: any[] = [];
  gridData: ITTSStaffOnDays[] = [];
  staffOptions: Partial<IStaff>[] = [];
  filteredStaffOptions: any[] = [];
  isRemoveBulkDisabled = true;
  excludeRowIds: number[] = [];
  tableHeight: string = '45vh'; 
  readonly unsubscribe$: Subject<void> = new Subject();

  constructor(
    private timetableSpecificationsService: TimetableSpecificationsService,
    private staffService: StaffService,
    protected relationshipsService: RelationshipsService,
    private timetableService: NewTimetableService,
    public translate: TranslateService,
    private snackbar: SnackbarService,
    private el: ElementRef
  ) {
  }

  ngOnInit(): void {
    this.staffService.getList(this.projectId)
    .pipe(takeUntil(this.unsubscribe$))
    .subscribe(staffs => {
      this.staffOptions = staffs.map(staff => ({
        id: staff.id,
        typeId: staff.typeId,
        name: staff.firstName + " " + staff.lastName,
        firstName: staff.firstName,
        lastName: staff.lastName,
        isExcluded: staff.isExcluded
      })).filter((staff) => !staff.isExcluded)
      this.setRowData();
    });

    this.timetableService.getPeriodStructureByTimetableId(this.timetableId).subscribe((periodStructure) => {
      const periods = this.markPeriodsAndRemoveAMPMPeriods(periodStructure);
      this.dayList = periods?.periodStructure.weeks
        .map((week) =>
          week.days.map((day) => day.periods.map(period => {
            return {
              ...period,
              text: `${day.dayDisplayName.slice(0, 3)} (${week.weekDisplayName}) P${period.periodDisplayName}`,
              id: period.id,
              disabled: false
            };
          })).flat()
        )
        .flat();
    });
    
    this.gridOptions = gridOptions.call(this, {
      onAddNewRow: this.onAddNewRow.bind(this),
      onAcceptNewRow: this.onAcceptNewRow.bind(this),
      onCancelAddingNewRow: this.onCancelAddingNewRow.bind(this),
      onEditRow: this.onEditRow.bind(this),
      onDeleteRow: this.onDeleteRowClicked.bind(this),
      onAcceptRow: this.onAcceptRow.bind(this),
      onCancelEditRow: this.onCancelEditRow.bind(this),
      onExcludeRow: this.onExcludeRow.bind(this),
      onIncludeRow: this.onIncludeRow.bind(this)
    });
    
    this.timetableSpecificationsService.getStaffOnDaysList(this.timetableId)
      .subscribe(data => {
        this.gridData = data;
        this.setRowData();
      })
    
    this.searchControl.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(searchValue => {
        this.gridApi.setQuickFilter(searchValue ?? '');
      })

      setTimeout(() => {
        this.setRowData();
      }, 50)
  }

  ngAfterViewInit() {
    this.updateTableHeight();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  onGridReady(params: GridReadyEvent): void {
    this.params = params;
    this.gridApi = params.api;
    this.gridApi.setDomLayout('normal');
  }

  onAddNewRow(params: ICellRendererParams): void {
    this.gridOptions.api?.setPinnedBottomRowData([
      {
        id: 'newRowId',
        staffId: null,
        undesirablePeriodIds: null,
        preferredPeriodIds: null,
        isExcluded: false
      }
    ]);
    const rowColumns = this.params.columnApi.getColumns() ?? [];
    this.gridApi.startEditingCell({ rowIndex: 0, rowPinned: 'bottom', colKey: rowColumns[1]?.getColId() });
  }

  onAcceptNewRow(params: ICellRendererParams) {
    this.timetableSpecificationsService.addStaffOnDays(this.timetableId, {
      ...params.data,
      id: 0
    }).pipe(switchMap(() => this.timetableSpecificationsService.getStaffOnDaysList(this.timetableId)))
      .subscribe({
        next: data => {
          this.gridData = data;
          this.setRowData();
          this.gridOptions.api?.setPinnedBottomRowData([{}]);
          this.snackbar.saved();
        },
        error: ({ error }) => {
          if (error?.validationErrors && error?.validationErrors[0]) {
            this.snackbar.error(error.validationErrors[0].errorMessage);
          }
          const rowColumns = this.params.columnApi.getColumns() ?? [];
          this.gridApi.startEditingCell({ rowIndex: 0, rowPinned: 'bottom', colKey: rowColumns[1]?.getColId() });
        }
      })
  }

  onCancelAddingNewRow(): void {
    this.gridOptions.api?.setPinnedBottomRowData([{}]);
  }

  onEditRow(params: ICellRendererParams): void {
    const rowIndex = params.node.rowIndex;
    const rowColumns = this.params.columnApi.getColumns() ?? []
    if (rowIndex || rowIndex === 0) {
      this.gridApi.startEditingCell({ rowIndex, colKey: rowColumns[0].getColId() })
    }
  }

  onAcceptRow(params: ICellRendererParams): void {
    this.timetableSpecificationsService.editStaffOnDays(params.data.id, params.data)
      .pipe(switchMap(() => this.timetableSpecificationsService.getStaffOnDaysList(this.timetableId)))
      .subscribe({
        next: data => {
          this.gridData = data;
          this.setRowData();
          this.snackbar.saved();
        },
        error: ({ error }) => {
          if (error?.validationErrors && error?.validationErrors[0]) {
            this.snackbar.error(error.validationErrors[0].errorMessage);
          }
          const rowIndex = params.node.rowIndex;
          const rowColumns = this.params.columnApi.getColumns() ?? [];
          if (rowIndex || rowIndex === 0) {
            this.gridApi.startEditingCell({ rowIndex, colKey: rowColumns[0]?.getColId() });
          }
        }
      })
  }

  onCancelEditRow(params: ICellRendererParams): void {
    this.timetableSpecificationsService.getStaffOnDaysList(this.timetableId)
      .subscribe(data => {
        this.gridData = data;
        this.setRowData();
      })
  }

  onDeleteRowClicked(params: ICellRendererParams): void {
    this.deleteRowId = params.data.id;
    this.deleteStaffOnDaysWarningModal.show();
  }

  onDeleteRow(): void {
    this.deleteStaffOnDaysWarningModal.hide();
    this.timetableSpecificationsService.deleteStaffOnDays(this.deleteRowId)
      .pipe(switchMap(() => this.timetableSpecificationsService.getStaffOnDaysList(this.timetableId)))
      .subscribe(data => {
        this.gridData = data;
        this.setRowData();
        this.snackbar.success(this.translate.instant('Successful operation'));
      })
  }

  onExcludeRow(params: ICellRendererParams): void {
    this.excludeRowIds = [params.data.id];
    this.excludeSubjectRelationshipWarningModal.show();
  }

  onIncludeRow(params: ICellRendererParams): void {
    this.excludeIncludeRequest({ isExcluded: false, ids: [params.data.id] })
  }

  excludeIncludeBulk(isExcluded: boolean): void {
    this.gridApi.stopEditing();
    this.gridOptions.api?.setPinnedBottomRowData([{}]);
    const ids = this.gridApi.getSelectedRows().map(row => row.id);
    if (!isExcluded) {
      this.excludeIncludeRequest({ isExcluded, ids })
    } else {
      this.excludeRowIds = ids;
      this.excludeSubjectRelationshipWarningModal.show();
    }
  }

  excludeIncludeRequest(data: { isExcluded: boolean, ids: number[] }) {
    this.excludeSubjectRelationshipWarningModal?.modalComponentRef?.nativeElement.hide();
    this.timetableSpecificationsService.excludeStaffOnDays(data)
      .pipe(switchMap(() => this.timetableSpecificationsService.getStaffOnDaysList(this.timetableId)))
      .subscribe(data => {
        this.gridData = data;
        this.setRowData();
        this.snackbar.success(this.translate.instant('Successful operation'));
      })
  }

  viewTypeChange(event: Event): void {
    this.gridApi.stopEditing();
    this.viewType = (event as CustomEvent).detail.innerText.toLowerCase() === INCLUDED_TYPES.ACTIVE
      ? INCLUDED_TYPES.ACTIVE
      : INCLUDED_TYPES.EXCLUDED;
    this.setRowData();
    if (this.viewType == INCLUDED_TYPES.EXCLUDED) {
      this.gridOptions.api?.setPinnedBottomRowData([]);
    } else {
      this.gridOptions.api?.setPinnedBottomRowData([{}]);
    }
    this.setRowData();
  }

  setRowData(): void {
    this.viewType === INCLUDED_TYPES.ACTIVE
      ? setTimeout(() => {
        this.gridApi?.setRowData(this.gridData.filter(data => !data.isExcluded));
        this.gridApi?.redrawRows();
      }, 0)
      : setTimeout(() => {
        this.gridApi?.setRowData(this.gridData.filter(data => data.isExcluded));
        this.gridApi?.redrawRows();
      }, 0)
  }

  GetFilterdStaffList(staffId: string): any[] {
    const rowsToDisplay = this.gridApi?.getRenderedNodes()?.map(node => node?.data);
    if (rowsToDisplay && rowsToDisplay.length >= 0) {
      this.filteredStaffOptions = this.staffOptions;
      let mergedStaffIds: number[] = [];
      const selectedIds = rowsToDisplay.map(({ staffId }) => staffId);

      if (this.viewType === INCLUDED_TYPES.ACTIVE) {
        const excludedStaffs = this.gridData.filter(data => data.isExcluded).map(row => row.staffId);
        mergedStaffIds = selectedIds.concat(excludedStaffs);
      } else {
        const includedStaffs = this.gridData.filter(data => !data.isExcluded).map(row => row.staffId);
        mergedStaffIds = selectedIds.concat(includedStaffs);
      }

      this.filteredStaffOptions.forEach((obj) => {
        if (mergedStaffIds.includes(obj.id)) {
          obj.disabled = true;
        } else {
          obj.disabled = false;
        }
      })

      return this.filteredStaffOptions;
    }
    return this.staffOptions;
  }


  private markPeriodsAndRemoveAMPMPeriods(periodStructure: IWithPeriodStructureResponse) {
    const filtered = {
      ...periodStructure,
      periodStructure: {
        ...periodStructure.periodStructure,
        weeks: periodStructure.periodStructure.weeks.map((week) => {
          week.days = week.days.map((day) => {
            day.periods = day.periods
              .filter((period) => (['PERIOD'].includes(period.periodCode)))
            return day;
          });
          return week;
        })
      }
    };
    return filtered;
  }

  @HostListener('window:resize')
  updateTableHeight() {
    const infoMsg = this.el.nativeElement.querySelector('.info-msg');
    if (infoMsg) {
      const headerHeight = infoMsg.getBoundingClientRect().height;
      const headerMarginBottom = parseFloat(getComputedStyle(infoMsg).marginBottom);
      const totalHeight = headerHeight + headerMarginBottom;
      this.tableHeight = `calc(45vh - ${totalHeight}px)`;
    }
  }

}
