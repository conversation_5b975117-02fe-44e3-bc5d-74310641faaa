import { Component } from '@angular/core';
import { GridApi, ICellRendererParams, RowEditingStartedEvent, RowEditingStoppedEvent } from 'ag-grid-community';

export interface IStaffingActions extends ICellRendererParams {
  onEditRow: (params: ICellRendererParams) => {},
  onCancelEditRow: () => {},
  onAcceptRow: (params: ICellRendererParams) => {},
}

@Component({
  selector: 'bromcom-ag-grid-staffing-actions',
  templateUrl: './ag-grid-staffing-actions.component.html',
  styleUrls: ['./ag-grid-staffing-actions.component.scss']
})
export class AgGridStaffingActionsComponent {
  private params!: IStaffingActions;
  private gridApi!: GridApi;
  private grid!: HTMLElement
  private isEdit = false;

  get isEditActive(): boolean {
    return this.isEdit;
  }

  agInit(params: IStaffingActions): void {
    this.params = params;
    this.gridApi = params.api;
    this.grid = document.getElementById('staffing-grid')!;

    this.gridApi.addEventListener('rowEditingStarted', this.onRowEditingStarted.bind(this));
    this.gridApi.addEventListener('rowEditingStopped', this.onRowEditingStopped.bind(this));
  }

  onEditRow(): void {
    this.params.onEditRow(this.params);
  }

  onAcceptRow(): void {
    this.gridApi.stopEditing();
    this.params.onAcceptRow(this.params);
  }

  onCancelEditingRow(): void {
    this.gridApi.stopEditing(true)
    this.params.onCancelEditRow();
  }

  private onRowEditingStarted(event: RowEditingStartedEvent): void {
    this.grid?.classList.add('no-pointer-events')
    if (event.data.id === this.params.data.id) {
      this.isEdit = true;
    }
  }

  private onRowEditingStopped(event: RowEditingStoppedEvent): void {
    this.grid?.classList.remove('no-pointer-events')
    if (event.data.id === this.params.data.id) {
      this.isEdit = false;
    }
  }
}
