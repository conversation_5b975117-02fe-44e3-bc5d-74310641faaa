<div class="box" [ngClass]="{'active' : active}" cdkDrag>
  <div class="part-one">
    <img src="assets/images/drag-icon.png" alt="">
    <div>
      <div class="circle" [style]="{'background-color': getColor(getSubject(_item.subjectId)!.color)}"></div>
    </div>
    <div class="text">
      {{getBoxText()}}
    </div>
  </div>
  <div class="part-two" [formGroup]="formGroup">
    <input class="input" type="text" formControlName="classCount"
           (blur)="onClassCountInputBlur()"
           appAcceptNumbers>
    <input class="input" type="text" formControlName="periodCount"
           (blur)="onPeriodCountInputBlur()"
           appAcceptNumbers>
    <div (click)="removeSubjectToYearGroup(_item)" class="action">
      <img src="assets/images/remove-row-icon.png" alt="">
    </div>
  </div>
</div>
