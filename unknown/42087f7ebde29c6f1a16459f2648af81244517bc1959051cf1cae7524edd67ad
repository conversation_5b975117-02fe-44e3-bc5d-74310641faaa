import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ISubjectInformationPreview } from "../../../../../../../_shared/models/ISubjectInformationPreview";
import { Subject } from "rxjs";
import { ISubject } from "../../../../../../../_shared/models/ISubject";
import {
  SUBJECT_INFORMATION_PREVIEW_CELL_TYPE
} from "../../../../../../../_shared/enums/SubjectInformationPreviewCellType";
import { isColorDark } from '@bromcom/ui';
import { IRowTitle } from "../../../../../../../_shared/models/IRowTitle";
import { IPreviewStructure } from "../../../../../../../_shared/models/IPreviewStructure";

@Component({
  selector: 'bromcom-preview',
  templateUrl: './preview-timetable.component.html',
  styleUrls: ['./preview-timetable.component.scss']
})
export class PreviewTimetableComponent implements OnInit, OnDestroy {
  @Input() subjects!: ISubject[];
  @Input() yearGroupName: string | undefined;
  @Input() shouldDisplayDefaultClassRows: boolean | undefined;
  @Input() classCount$?: Subject<number>;
  @Input() previewData$!: Subject<ISubjectInformationPreview | null>;
  previewData!: ISubjectInformationPreview;
  structure!: IPreviewStructure[][];
  firstRow: ({ colspan: number } | null)[] = [];
  rowTitles: IRowTitle[] = [];
  CELL_TYPE = SUBJECT_INFORMATION_PREVIEW_CELL_TYPE;
  dynamicArray: number[] = [];
  readonly array12 = Array(12);
  readonly array13 = Array(13);
  private readonly unsubscribe$: Subject<void> = new Subject();

  ngOnInit(): void {
    this.previewData$.subscribe(previewData => {
      if (previewData) {
        this.previewData = previewData;
        this.updateStructure();
      }
    })

    this.classCount$?.subscribe(val => {
      this.dynamicArray = Array(val);
    })
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  private updateStructure(): void {
    this.initFirstRow();
    this.initRowTitles();
    this.structure = []
    let classCounter = 0;
    const subjectClassCounters = Array(this.previewData.optionsBlocks.length).fill(0);
    const optionBlockClassCounters = Array(this.previewData.optionsBlocks.length).fill(0);
    const complexSubjectClassCounters = Array(this.previewData.complexBlocks.length).fill(0);
    const complexBlockClassCounters = Array(this.previewData.complexBlocks.length).fill(0);
    this.rowTitles.map((title, rowIndex) => {
      const row = [];
      row.push(title);
      if (rowIndex === 0) {
        this.firstRow.map(item => {
          row.push(item);
        })
      } else {
        this.previewData.simpleBlocks?.map(block => {
          if (classCounter <= block.classCount) {
            row.push({
              type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Simple,
              ...block
            });
          } else if (rowIndex <= this.previewData.maxClassCount) {
            row.push({
              type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Empty,
              color: '#fff'
            });
          } else if (rowIndex === this.previewData.maxClassCount + 1) {
            row.push({
              value: block.periodPerCycle,
              type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.PeriodPerCycle
            });
          } else if (rowIndex === this.previewData.maxClassCount + 2) {
            row.push({
              value: block.totalPeriodCount,
              type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.TotalPeriodCount
            });
          }
        })

        this.previewData.linearBlocks?.map((item) => {
          if (rowIndex === 1) {
            row.push({
              type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Group,
              rowspan: item.classCount, title: 'L'
            });
          }
          item.subjects?.map((subject, subjectIndex) => {
            if (classCounter <= subject.classCount) {
              row.push({
                type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Linear,
                ...subject
              });
            } else if (subjectIndex < 1 && rowIndex <= this.previewData.maxClassCount) {
              row.push({
                type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Empty,
                color: '#fff',
                colspan: item.subjects.length + 1
              });
            } else if (subjectIndex < 1 && rowIndex === this.previewData.maxClassCount + 1) {
              row.push({
                value: item.periodPerCycle,
                type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.PeriodPerCycle,
                colspan: item.subjects.length + 1
              });
            } else if (subjectIndex < 1 && rowIndex === this.previewData.maxClassCount + 2) {
              row.push({
                value: item.totalPeriodCount,
                type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.TotalPeriodCount,
                colspan: item.subjects.length + 1
              });
            }
          })
        })

        this.previewData.optionsBlocks?.map((item, blockIndex) => {
          if (rowIndex === 1) {
            row.push({
              type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Group,
              rowspan: item.classCount,
              title: 'O'
            });
          }
          item.subjects.map((subject, subjectIndex) => {
            if (subjectClassCounters[blockIndex] <= subject.classCount && subjectIndex === optionBlockClassCounters[blockIndex]) {
              row.push({
                type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Option,
                ...subject
              })
            } else if (subjectClassCounters[blockIndex] > subject.classCount && subjectIndex === optionBlockClassCounters[blockIndex]) {
              subjectClassCounters[blockIndex] = 1;
              optionBlockClassCounters[blockIndex]++;
              if (optionBlockClassCounters[blockIndex] === this.previewData.optionsBlocks[blockIndex].subjects.length && rowIndex <= this.previewData.maxClassCount) {
                row.push({
                  type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Empty,
                  color: '#fff',
                  colspan: 2
                });
              } else if (subjectIndex < 1 && rowIndex === this.previewData.maxClassCount + 1) {
                row.push({
                  value: item.periodPerCycle,
                  type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.PeriodPerCycle,
                  colspan: 2
                });
              }
            } else if (optionBlockClassCounters[blockIndex] === this.previewData.optionsBlocks[blockIndex].subjects.length && subjectIndex < 1 && rowIndex <= this.previewData.maxClassCount) {
              row.push({
                type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Empty,
                color: '#fff',
                colspan: 2
              });
            } else if (subjectIndex < 1 && rowIndex === this.previewData.maxClassCount + 1) {
              row.push({
                value: item.periodPerCycle,
                type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.PeriodPerCycle,
                colspan: 2
              });
            } else if (subjectIndex < 1 && rowIndex === this.previewData.maxClassCount + 2) {
              row.push({
                value: item.totalPeriodCount,
                type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.TotalPeriodCount,
                colspan: 2
              });
            }
          });
        });
        
        this.previewData.complexBlocks?.map((item, blockIndex) => {
          const maxPeriodCount = Math.max(...item.subjects.map(subject => subject.periodPerCycle));
          if (rowIndex === 1) {
            row.push({
              type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Group,
              rowspan: item.classCount,
              title: 'C'
            });
          }
          item.subjects.map((subject, subjectIndex) => {
            if (complexSubjectClassCounters[blockIndex] <= subject.classCount && subjectIndex === complexBlockClassCounters[blockIndex]) {
              for (let i = 1; i <= maxPeriodCount; i++) {
                if ( i <= subject.periodPerCycle ) {
                  row.push({
                    type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Complex,
                    ...subject
                  })
                } else {
                  row.push({
                    type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.FreeSubjectInComplex,
                    color: '#f1f5f9'
                  })
                }
              }
            } else if (complexSubjectClassCounters[blockIndex] > subject.classCount && subjectIndex === complexBlockClassCounters[blockIndex]) {
              complexSubjectClassCounters[blockIndex] = 1;
              complexBlockClassCounters[blockIndex]++;
              if (complexBlockClassCounters[blockIndex] === this.previewData.complexBlocks[blockIndex].subjects.length && rowIndex <= this.previewData.maxClassCount) {
                row.push({
                  type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Empty,
                  color: '#fff',
                  colspan: maxPeriodCount + 1
                });
              } else if (subjectIndex < 1 && rowIndex === this.previewData.maxClassCount + 1) {
                row.push({
                  value: item.periodPerCycle,
                  type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.PeriodPerCycle,
                  colspan: maxPeriodCount + 1
                });
              }
            } else if (complexBlockClassCounters[blockIndex] === this.previewData.complexBlocks[blockIndex].subjects.length && subjectIndex < 1 && rowIndex <= this.previewData.maxClassCount) {
              row.push({
                type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Empty,
                color: '#fff',
                colspan: maxPeriodCount + 1
              });
            } else if (subjectIndex < 1 && rowIndex === this.previewData.maxClassCount + 1) {
              row.push({
                value: item.periodPerCycle,
                type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.PeriodPerCycle,
                colspan: maxPeriodCount + 1
              });
            } else if (subjectIndex < 1 && rowIndex === this.previewData.maxClassCount + 2) {
              row.push({
                value: item.totalPeriodCount,
                type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.TotalPeriodCount,
                colspan: maxPeriodCount + 1
              });
            }
          });
        });
      }
      if (this.structure.length === this.previewData.classNames.length + 1) {
        row.push({
          value: this.previewData.totalPeriodPerCycle,
          type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.TotalPeriodCycle
        });
      } else {
        row.push({
          type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Title
        })
      }
      this.structure.push(row as IPreviewStructure[]);
      classCounter++;
      subjectClassCounters.map((_, index) => subjectClassCounters[index]++);
      complexSubjectClassCounters.map((_, index) => complexSubjectClassCounters[index]++);
    })
  }

  getCellWidth(item: IPreviewStructure): string {
    if (item?.type === SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Group) {
      return '25px';
    } else if (item?.type === SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Linear || item?.type === SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Complex || item?.type === SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.FreeSubjectInComplex) {
      return '40px';
    }
    return '';
  }

  getSubject(subjectId: number | undefined): ISubject | undefined {
    return this.subjects.find((subject) => subjectId === subject.id);
  }

  isColorDarkHelper(color: string | undefined) {
    return color ? isColorDark(color) : false;
  }

  private initFirstRow() {
    this.firstRow = [];
    this.previewData?.simpleBlocks?.map(() => {
      this.firstRow.push(null);
    });
    this.previewData?.linearBlocks?.map(block => {
      this.firstRow.push({
        colspan: block.subjects.length + 1
      })
    });
    this.previewData?.optionsBlocks?.map(() => {
      this.firstRow.push({
        colspan: 2
      })
    });
    this.previewData?.complexBlocks?.map((block) => {
      const maxPeriodCount = Math.max(...block.subjects.map(subject => subject.periodPerCycle));
      this.firstRow.push({
        colspan: maxPeriodCount + 1
      })
    });
  }

  private initRowTitles() {
    const classNames = this.previewData?.classNames.map(name => ({
      name,
      type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Title
    }));
    this.rowTitles = [{
      name: 'Class',
      type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Title
    }, ...classNames, {
      name: 'Per cycle',
      type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Title
    }, {
      name: 'Total',
      type: SUBJECT_INFORMATION_PREVIEW_CELL_TYPE.Title
    }];
  }

  hasAnyPreviewBlocks() {
    if (!this.previewData) {
      return false;
    }
    return this.previewData.simpleBlocks.length > 0 || this.previewData.linearBlocks.length > 0 || this.previewData.optionsBlocks.length > 0 || this.previewData.complexBlocks.length > 0
  }
}
