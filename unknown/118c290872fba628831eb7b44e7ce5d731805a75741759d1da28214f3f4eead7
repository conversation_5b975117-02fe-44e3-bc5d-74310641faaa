<div class="box" [ngClass]="{'active' : active}" cdkDrag>
  <div class="part-one">
    <img src="assets/images/drag-icon.png" alt="">
    <div>
      <div class="circle"
           [style]="{'background-color': getColor(getSubject(_item.subjectToYearGroups[0].subjectId)!.color)}">
      </div>
    </div>
    <div class="text"
         [matTooltipDisabled]="getBoxText().length < 20"
         [matTooltip]="getBoxText()"
         [matTooltipPosition]="'above'">
      {{getBoxText()}}
    </div>
  </div>
  <div class="part-two" [formGroup]="formGroup">
    <input class="input" type="text" formControlName="classCount" appAcceptNumbers (blur)="onClassCountInputBlur()">
    <input class="input" type="text" formControlName="periodCount" appAcceptNumbers (blur)="onPeriodCountInputBlur()">
    <i
      [matMenuTriggerFor]="menu"
      [style]="{cursor: 'pointer'}"
      class="far fa-ellipsis-v" aria-hidden="true">
    </i>
  </div>
</div>

<mat-menu #menu="matMenu"
          class="linked-block-action-modal">
  <button mat-menu-item
          (click)="copyBlock()">
    <span class="text">{{'Copy Subject' | translate}}</span>
  </button>

  <button mat-menu-item
          (click)="remove()" class="first-menu-row">
    <span class="text">{{'Remove Subject' | translate}}</span>
  </button>

  <button mat-menu-item
          [matMenuTriggerFor]="innerLinearMenu">
    <span class="text">{{'Add To Linear Group' | translate}}</span>
  </button>

  <button mat-menu-item
          [matMenuTriggerFor]="innerOptionsMenu">
    <span class="text">{{'Add To Option Group' | translate}}</span>
  </button>
</mat-menu>

<mat-menu #innerLinearMenu="matMenu"
          class="linked-block-action-modal">
  <button mat-menu-item
          *ngFor="let item of linearGroups; let groupIndex = index"
          (click)="addSubjectToYearGroupToLinearGroup(groupIndex)">
    <span class="text">{{item?.blockName}}</span>
  </button>
</mat-menu>

<mat-menu #innerOptionsMenu="matMenu"
          class="linked-block-action-modal">
  <button mat-menu-item
          *ngFor="let item of optionsGroups; let groupIndex = index"
          (click)="addSubjectToYearGroupToOptionsGroup(groupIndex)">
    <span class="text">{{item?.blockName}}</span>
  </button>
</mat-menu>
