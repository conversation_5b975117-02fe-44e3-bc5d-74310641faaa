import { Pipe, PipeTransform } from '@angular/core';
import { ISession } from '../../../../../../_shared/models/ISession';
import { ISubjectToYearGroup } from '../../../../../../_shared/models/ISubjectToYearGroup';
import { TranslateService } from '@ngx-translate/core';
import { IStaff } from '../../../../../../_shared/models/IStaff';
import { BLOCK_TYPE } from '../../../../../../_shared/enums/BlockType';
import { IRooms } from '../../../../../../_shared/models/IRooms';

@Pipe({
  name: 'arrangeSessions',
})
export class ArrangeSessionsPipe implements PipeTransform {
  constructor(private translate?: TranslateService) {}

  transform(sessions: ISession[], subjectsInYearGroup: ISubjectToYearGroup[], className: string, staffList: IStaff[] | null, blockTypeId: number, rooms?: IRooms[] | null): ISession[] {
    let arrangedSessions: ISession[] = [];
    const staffMap = new Map<number, string>(staffList?.map((staff) => [staff.id, staff.code]));
    const roomMap = new Map<number, string>(rooms?.map((room) => [room.id, room.code]));
    const sessionsForClass = sessions.filter((session) => session.customClassName === className);

    const singlePeriodsForClass = sessionsForClass.filter((s) => s.joinedSessions.length === 1);
    const doublePeriodsForClass = sessionsForClass.filter((s) => s.joinedSessions.length > 1);

    let subjectsInClassRow: any[] = [];
    
    let columnId = 1;
    if(blockTypeId === BLOCK_TYPE.Complex) {
      subjectsInClassRow = this.arrangeSessions(singlePeriodsForClass, doublePeriodsForClass, blockTypeId, staffMap, roomMap, columnId);
    } else {
      subjectsInYearGroup.forEach((subject) => {
        if (!sessionsForClass.some((s) => s.subjectToYearGroupId === subject.id)) return;
        const singlePeriodsForSubject = singlePeriodsForClass.filter((s) => s.subjectToYearGroupId === subject.id);
        const doublePeriodsForSubject = doublePeriodsForClass.filter((s) => s.subjectToYearGroupId === subject.id);
        
        const sessionsForSubject = this.arrangeSessions(singlePeriodsForSubject, doublePeriodsForSubject, blockTypeId, staffMap, roomMap, columnId);
        columnId = sessionsForSubject.length ? sessionsForSubject[sessionsForSubject.length - 1].columnId + 1 : 1;
        subjectsInClassRow.push(...sessionsForSubject);
      });
    }

    arrangedSessions.push(...subjectsInClassRow);
    if(blockTypeId === BLOCK_TYPE.Complex) {
      arrangedSessions = arrangedSessions.map(session => {
        const sessionIndex = session.periodIndex!;
        const joinedSessionId = session.joinedSessions.find(s => s !== session.id);
        const pairedSession = sessionsForClass.find(s => s.id === joinedSessionId);
        const pairedColumnId = (session.joinedSessions.length > 1) ? pairedSession?.periodIndex : undefined;
        return { ...session, columnId: sessionIndex, pairedColumnId, sessionIndex };
      });
      arrangedSessions.sort((a, b) => a.periodIndex! - b.periodIndex!);
    }
    return arrangedSessions;
  }
  
  private arrangeSessions(singleSessions: ISession[], doubleSessions: ISession[], blockTypeId: number, staffMap: Map<number, string>, roomMap: Map<number, string> | undefined, columnId: number) {
    const subjectsInEachClass: any[] = [];
    
    this.updateLayout(singleSessions, doubleSessions, blockTypeId);
    
    //totalPeriodCount indicates the total number of columns in the visual block layout for this particular subject
    const totalPeriodCount: number =
    singleSessions.length > 0 && doubleSessions.length > 0 ? 3
      : doubleSessions.length > 0 ? 2
      : singleSessions.length > 0 ? 1
      : 0;
      
      //Added columnId to identify the column to which sessions belong
      for (const period of singleSessions) {
        period.columnId = columnId;
        period.sessionsCount = singleSessions.length;
        if(blockTypeId === BLOCK_TYPE.Complex) {
          columnId++;
        }
      }
      columnId++;
      
      const singlePeriodsWithStaffDetails = this.appendStaffAndRoomDetails(singleSessions, blockTypeId, staffMap, roomMap);
      const doublePeriodsWithStaffDetails = this.appendStaffAndRoomDetails(doubleSessions, blockTypeId, staffMap, roomMap);

      const singlePeriodInVisualBlockLayout = 1;
      const doublePeriodInVisualBlockLayout = 2;
      
      const toBeScheduledSessionsForSubjectInClass = singlePeriodsWithStaffDetails.filter(s => !s.toBeScheduled);
      const singlePeriodsToBeDisplayed = toBeScheduledSessionsForSubjectInClass.length > 0 ? toBeScheduledSessionsForSubjectInClass : singlePeriodsWithStaffDetails;
      const singlePeriodsInClass = blockTypeId !== BLOCK_TYPE.Complex ? singlePeriodsToBeDisplayed.slice(0, singlePeriodInVisualBlockLayout) : singlePeriodsToBeDisplayed;
      const doublePeriodsInClass: ISession[] = [];
      
      for (const session of doublePeriodsWithStaffDetails) {
        if (!doublePeriodsInClass.includes(session)) {
          const joinedSessions = session.joinedSessions;
          const paired = doublePeriodsWithStaffDetails.filter((session) =>
            joinedSessions?.every((s) => session.joinedSessions?.includes(s))
          );
          paired[0].customClass = 'isDouble';
          //To set columnId and pairedColumnId for each session in double period
          paired[0].columnId = columnId;
          paired[1].columnId = columnId + 1;
          paired[0].pairedColumnId = paired[1].columnId;
          paired[1].pairedColumnId = paired[0].columnId;
          paired[0].sessionsCount = paired[1].sessionsCount = doublePeriodsWithStaffDetails.length / 2;
          doublePeriodsInClass.push(...paired);
          if(blockTypeId === BLOCK_TYPE.Complex) {
            columnId = paired[1].columnId;
            columnId++;
          }
        }
      }
      if(doublePeriodsWithStaffDetails.length) {
        columnId++;
      }

      if(blockTypeId !== BLOCK_TYPE.Complex) {
        doublePeriodsInClass.splice(doublePeriodInVisualBlockLayout);
      }
      const subjectsInClass = [...singlePeriodsInClass, ...doublePeriodsInClass].sort((a, b) =>
        this.sortByjoinedSessionsLength(a, b)
      );
      subjectsInEachClass.push(...subjectsInClass);
      columnId += totalPeriodCount - 1; // Increment columnId based on totalPeriodCount
      
      return subjectsInEachClass;
  }

  private sortByjoinedSessionsLength(a: ISession, b: ISession): number {
    return a.joinedSessions.length - b.joinedSessions.length;
  }

  private appendStaffAndRoomDetails(sessions: ISession[], blockTypeId: number, staffMap: Map<number, string>, roomMap?: Map<number, string>): ISession[] {
    if (sessions.length === 0) {
      return sessions;
    }
    const firstStaffId = sessions[0].mainStaffId;
    const allSame = sessions.every((session) => firstStaffId != null && session.mainStaffId === firstStaffId);
    const anyNull = sessions.some((session) => session.mainStaffId == null);
    
    return sessions.map((s) => {
      s.sessionRoomName = s.roomId ? roomMap?.get(s.roomId) : '--';
      if (blockTypeId === BLOCK_TYPE.Complex) {
        return {
          staffDisplayName: s.mainStaffId !== null ? staffMap.get(s.mainStaffId) : '--',
          ...s,
        };
      } else if (allSame) {
        const staffName = firstStaffId !== null ? staffMap.get(firstStaffId) : '';
        return {
          staffDisplayName: staffName,
          sessionStaffName: s.mainStaffId !== null ? staffMap.get(s.mainStaffId) : '--',
          ...s,
        };
      } else if (anyNull) {
        return {
          staffDisplayName: '--',
          sessionStaffName: s.mainStaffId !== null ? staffMap.get(s.mainStaffId) : '--',
          tooltipContent: this.translate?.instant(`Some periods do not have assigned staff.`),
          ...s,
        };
      } else {
        const staffNames = new Set<string>(sessions.map((s) => (s.mainStaffId !== null ? staffMap.get(s.mainStaffId) || '' : '')));
        const toolTipContent: string = Array.from(staffNames).join(',');
        return {
          hasDifferentStaff: true,
          sessionStaffName: s.mainStaffId !== null ? staffMap.get(s.mainStaffId) : '--',
          tooltipContent: toolTipContent,
          ...s,
        };
      }
    });
  }
  
  private updateLayout(singlePeriodsForSubject: ISession[], doublePeriodsForSubject: ISession[], blockTypeId: number) {
    const scheduledSinglePeriodsForSubject = singlePeriodsForSubject.filter(p => p.periodId !== null);
    const scheduledDoublePeriodsForSubject = doublePeriodsForSubject.filter(p => p.periodId !== null);
    
    //Logic to handle drag and drop of simple block type sessions
    if (blockTypeId === BLOCK_TYPE.Complex) {
      singlePeriodsForSubject.filter(p => p.periodId !== null).forEach(p => p.isDraggable = false);
      singlePeriodsForSubject.filter(p => p.periodId == null).forEach(p => p.isDraggable = true);
      doublePeriodsForSubject.filter(p => p.periodId !== null).forEach(p => p.isDraggable = false);
      doublePeriodsForSubject.filter(p => p.periodId == null).forEach(p => p.isDraggable = true);
    } else {
      if(singlePeriodsForSubject.length > scheduledSinglePeriodsForSubject.length) {
        singlePeriodsForSubject.splice(0, singlePeriodsForSubject.length, ...singlePeriodsForSubject.filter(p => p.periodId === null));
        singlePeriodsForSubject.forEach(p => {
          if(p.periodId == null && !p.isDraggable) {
            p.isDraggable = true;
          }
        });
      } else if(singlePeriodsForSubject.length === scheduledSinglePeriodsForSubject.length) {
        singlePeriodsForSubject.forEach(p => p.isDraggable = false);
      }
      
      if(doublePeriodsForSubject.length > scheduledDoublePeriodsForSubject.length) {
        doublePeriodsForSubject.splice(0, doublePeriodsForSubject.length, ...doublePeriodsForSubject.filter(p => p.periodId === null));
        doublePeriodsForSubject.forEach(p => {
          if(p.periodId == null && !p.isDraggable) {
            p.isDraggable = true;
          }
        });
      } else if(doublePeriodsForSubject.length === scheduledDoublePeriodsForSubject.length) {
        doublePeriodsForSubject.map(p => p.isDraggable = false);
      }
    }
    
  }
}
