@import "apps/bromcom-timetables/src/assets/styles/variables";

.table-container {
  display: flex;
  width: fit-content;
  flex-direction: column;
}

table {
  width: fit-content;
  border-spacing: 4px;
}

td {
  width: 75px;
  min-width: 75px;
  height: 24px;
  background: $color-blue-grey-100;
  border: 1px solid $color-blue-grey-200;
  border-radius: 4px;

  span {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    font-size: 13px;
    color: $color-blue-grey-600;
  }
}

.group {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  background-color: $color-blue-grey-200;
  padding-top: 3px;
  vertical-align: top;

  .group-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    text-align: center;
  }

  .circle {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 18px;
    height: 18px;
    background: $color-blue-grey-50;
    border: 1px solid $color-white-0;
    border-radius: 24px;
    font-style: normal;
    font-weight: 500;
    font-size: 10px;
    line-height: 18px;
    color: $color-blue-grey-600;
  }
}

.white-color {
  color: $color-white-0 !important;
}

.empty-table {
  max-width: 100%;

  .gray-cell {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    color: $color-blue-grey-300;
  }

  .white-cell {
    background-color: $color-white-0;
    border: 1px solid $color-blue-grey-200;
    border-radius: 4px;
  }
}

.preview {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  width: 100%;
  height: 56px;
  background: $color-blue-grey-100;
  border: 1px solid $color-blue-200;
  border-radius: 8px;
  justify-content: space-between;

  .badge {
    display: flex;
    align-items: center;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: $color-blue-900;
    padding: 0 8px;
    height: 20px;
    background: $color-blue-300;
    border-radius: 20px;
    margin-right: 24px;
  }
}

.linear,
.option,
.complex {
  width: 60px;
  min-width: 60px;
}

.group {
  width: 28px;
  min-width: 28px;
}

.isGreater {
  color: $color-red-tertiary-600 !important;
}
