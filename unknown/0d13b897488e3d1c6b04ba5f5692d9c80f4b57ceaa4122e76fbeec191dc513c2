import {
  AfterViewChecked,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  ViewChild
} from '@angular/core';
import { ISubject } from "../../../../../../../_shared/models/ISubject";
import { IListOfBlocksResponse } from "../../../../../../../_shared/models/IListOfBlocksResponse";
import { IAddToGroupEvent } from "../../../../../../../_shared/models/IAddToGroupEvent";
import { CdkDragDrop, CdkDragEnter, CdkDragExit } from "@angular/cdk/drag-drop";
import {
  IRemoveSubjectToYearGroupFromGroupEvent
} from "../../../../../../../_shared/models/IRemoveSubjectToYearGroupFromGroupEvent";

@Component({
  selector: 'bromcom-subject-list',
  templateUrl: './drag-and-drop-simple.component.html',
  styleUrls: ['./drag-and-drop-simple.component.scss']
})
export class DragAndDropSimpleComponent implements AfterViewChecked {
  @ViewChild('contentId') contentRef!: ElementRef;
  @Input() id!: string;
  @Input() dropListConnectedTo!: string[];
  @Input() subjects!: ISubject[];
  @Input() data!: IListOfBlocksResponse[];
  @Input() linearGroups!: IListOfBlocksResponse[];
  @Input() optionsGroups!: IListOfBlocksResponse[];
  @Input() selectedSubjects!: IListOfBlocksResponse[][];
  @Output() removeSubjectEvent = new EventEmitter();
  @Output() addSubjectToYearGroupToLinearGroupEvent: EventEmitter<IAddToGroupEvent> = new EventEmitter();
  @Output() addSubjectToYearGroupToOptionsGroupEvent: EventEmitter<IAddToGroupEvent> = new EventEmitter();
  @Output() addSubjectToYearGroupToSimpleBlockEvent = new EventEmitter<IRemoveSubjectToYearGroupFromGroupEvent>();
  @Output() updateSubjectToYearGroupEvent = new EventEmitter<IListOfBlocksResponse>();
  @Output() updateSubjectInfoEvent = new EventEmitter<void>();
  @Output() copyBlockEvent = new EventEmitter<IListOfBlocksResponse>();
  
  isCdkDragging = false;

  constructor(private changeDetectorRef: ChangeDetectorRef) {
  }

  ngAfterViewChecked(): void {
    this.changeDetectorRef.detectChanges();
  }

  removeSubject(event: IListOfBlocksResponse) {
    this.removeSubjectEvent.emit(event);
  }

  addToLinearGroup(event: IAddToGroupEvent) {
    this.addSubjectToYearGroupToLinearGroupEvent.emit(event);
  }

  addToOptionsGroup(event: IAddToGroupEvent) {
    this.addSubjectToYearGroupToOptionsGroupEvent.emit(event);
  }

  updateSubjectToYearGroup(event: IListOfBlocksResponse) {
    this.updateSubjectToYearGroupEvent.emit(event);
  }

  scrollbarVisible = () => {
    return this.contentRef?.nativeElement?.scrollHeight > this.contentRef?.nativeElement?.clientHeight;
  }

  drop(event: CdkDragDrop<any[]>) {
    if (event.previousContainer !== event.container && event.isPointerOverContainer) {
      const previousContainerData = event.previousContainer.data[event.previousIndex];
      const subject = previousContainerData?.blockTypeId ? previousContainerData.subjectToYearGroups[0] : previousContainerData;
      const payload: IRemoveSubjectToYearGroupFromGroupEvent = {
        subject,
        targetIndex: event.currentIndex
      };
      this.addSubjectToYearGroupToSimpleBlockEvent.emit(payload);
    }
    this.isCdkDragging = false;
  }
  
  cdkDragEntered(evt: CdkDragEnter) {
    this.isCdkDragging = true;
  }
  
  cdkDropExited(evt: CdkDragExit) {
    this.isCdkDragging = false;
  }

  updateSubjectInfo(): void {
    this.updateSubjectInfoEvent.emit();
  }

  copyBlock(block: IListOfBlocksResponse): void {
    this.copyBlockEvent.emit(block);
  }
}
