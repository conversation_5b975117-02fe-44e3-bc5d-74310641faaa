<div cdkDropList
     [id]="id"
     class="wrapper"
     [ngClass]="{'drag-entered': isCdkDragging && data.subjectToYearGroups.length > 0, 'collapsed': !isExpanded}"
     [cdkDropListData]="data.subjectToYearGroups"
     [cdkDropListConnectedTo]="dropListConnectedTo"
     (cdkDropListDropped)="drop($event)"
     (cdkDropListEntered)="cdkDragEntered($event)"
     (cdkDropListExited)="cdkDropExited($event)">

  <div class="empty-drag-wrapper" *ngIf="data.subjectToYearGroups.length <= 0" [ngClass]="{'has-close-icon': data.isAdditionalGroup, 'drag-entered': isCdkDragging}">
    <span *ngIf="data.isAdditionalGroup" class="icon-holder"><bcm-icon class="icon" (click)="onRemoveEmptySubjectGroup(data, type)" icon="fas fa-times"></bcm-icon></span>
    <div class="empty-drag">
      <div class="header"
           [class.disabled]="selectedSubjects | isDisabled">{{'Drag Your Subjects Here' | translate}}</div>
      <div class="description" *ngIf="DRAG_AND_DROP_GROUP_TYPE_ENUM.Linear === type"
           [class.disabled]="selectedSubjects | isDisabled">{{'To add new Linear Group select subject from subject panel' | translate}}</div>
      <div class="description" *ngIf="DRAG_AND_DROP_GROUP_TYPE_ENUM.Option === type"
           [class.disabled]="selectedSubjects | isDisabled">{{'To add new Option Group select subject from subject panel' | translate}}</div>
    </div>
  </div>


  <div class="content" *ngIf="data.subjectToYearGroups.length > 0">
    <div class="header">
      <div class="part-one">
        <img src="assets/images/drag-icon.png" alt="">
        <div class="expand-icons">
          <bcm-icon class="icon" *ngIf="isExpanded" icon="fa-solid fa-chevron-down" (click)="toggleExpand()">
          </bcm-icon>

          <bcm-icon class="icon" *ngIf="!isExpanded" icon="fa-solid fa-chevron-right" (click)="toggleExpand()">
          </bcm-icon>
        </div>
        <div *ngIf="!editTitle">{{_title}}</div>
        <div *ngIf="editTitle" class="title">
          <bromcom-input-field size="small" [clearable]="false" [formControl]="titleForm"></bromcom-input-field>
          <div class="info" *ngIf="titleForm.getError('required')">
            <bcm-tooltip message="{{'Group name is required.' | translate}}"
                         trigger="hover" class="name" open-delay="500">
              <img src="assets/images/info-icon.png" alt="">
            </bcm-tooltip>
          </div>
          <div class="info" *ngIf="titleForm.getError('custom')">
            <bcm-tooltip message="{{'Group name must be unique.' | translate}}"
                         trigger="hover" class="name" open-delay="500">
              <img src="assets/images/info-icon.png" alt="">
            </bcm-tooltip>
          </div>
        </div>
      </div>


      <div class="action">
        <bcm-icon [hidden]="editTitle"
                  [matMenuTriggerFor]="menu"
                  class="icon"
                  icon="far fa-ellipsis-h">
        </bcm-icon>

        <bcm-button *ngIf="editTitle" size="small" kind="link"
                    (click)="cancelEditTitle()">{{'Cancel' | translate}}
        </bcm-button>

        <bcm-button *ngIf="editTitle" size="small" kind="ghost"
                    [disabled]="titleForm.invalid"
                    (click)="saveEditTitle()">{{'Save' | translate}}
        </bcm-button>
      </div>
    </div>

    <ng-container *ngIf="isExpanded">
      <ng-container *ngFor="let item of data.subjectToYearGroups; let dragAndDropIndex = index">
        <bromcom-group-box-drag [item]="item"
                                [type]="type"
                                [blockIndex]="index"
                                [subjects]="subjects"
                                [index]="dragAndDropIndex"
                                [updateClassAndPeriodCount$]="updateClassAndPeriodCount$"
                                [data]="data"
                                (updateSubjectToYearGroupEvent)="updateSubjectToYearGroup($event)"
                                (updateSubjectInfoEvent)="updateSubjectInfo()"
                                (removeEvent)="removeItem($event)">
        </bromcom-group-box-drag>
      </ng-container>
    </ng-container>
  </div>
</div>


<mat-menu #menu="matMenu"
          class="linked-block-action-modal">
  <button mat-menu-item
          (click)="clickMenuItem('rename')">
    <span class="text">{{'Rename' | translate}}</span>
  </button>

  <button mat-menu-item
          (click)="clickMenuItem('delete')">
    <span class="text">{{'Delete' | translate}}</span>
  </button>

  <button mat-menu-item
          *ngIf="type === DRAG_AND_DROP_GROUP_TYPE_ENUM.Linear"
          (click)="clickMenuItem('update')">
    <span class="text">{{'Edit No. of Classes' | translate}}</span>
  </button>

  <button mat-menu-item
          *ngIf="type !== DRAG_AND_DROP_GROUP_TYPE_ENUM.Linear"
          (click)="clickMenuItem('update')">
    <span class="text">{{'Edit Period Count' | translate}}</span>
  </button>
</mat-menu>

<ng-container *ngIf="isModalOpen">
<bcm-modal [id]="modalId"
           size="small" backdrop
           (bcm-modal-before-close)="onModalClose()"
           #editPeriodAndNoOfClassModalComponent>
  <bcm-modal-header>{{_title}}</bcm-modal-header>

  <div class="modal-container">
    <label *ngIf="type === DRAG_AND_DROP_GROUP_TYPE_ENUM.Linear" for="noOfCLass">
      {{'Number of Classes' | translate}}*
    </label>
    <input class="input"
           id="noOfCLass"
           *ngIf="type === DRAG_AND_DROP_GROUP_TYPE_ENUM.Linear"
           [formControl]="noOfCLassOrPeriodCountForm"
           appAcceptNumbers/>

    <label *ngIf="type === DRAG_AND_DROP_GROUP_TYPE_ENUM.Option" for="noOfCLass">
      {{'Period Count' | translate}}*
    </label>
    <input class="input"
           id="periodCount"
           *ngIf="type === DRAG_AND_DROP_GROUP_TYPE_ENUM.Option"
           [formControl]="noOfCLassOrPeriodCountForm"
           appAcceptNumbers/>
  </div>

  <bcm-modal-footer>
    <bcm-button data-dismiss kind="ghost">{{'Cancel' | translate}}</bcm-button>
    <bcm-button (click)="saveNoOfClassOrPeriodCount()">{{'Save' | translate}}</bcm-button>
  </bcm-modal-footer>
</bcm-modal>
</ng-container>

<bromcom-general-modal #updatePeriodCountWarningModal
                             icon="far fa-exclamation-triangle"
                             type="warningAmber"
                             [dismiss]="false"
                             [header]="warningHeader"
                             [cancelText]="'Cancel' | translate"
                             [nextText]="'Proceed' | translate"
                             [description]="warningDescription"
                             (nextEvent)="updatePeriodCount()"
                             (cancelEvent)="showEditPeriodCount()">
</bromcom-general-modal>
