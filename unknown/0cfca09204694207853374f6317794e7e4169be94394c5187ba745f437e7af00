import { transformToAGGridConfig } from '@bromcom/core';
import { AgGridNoDataComponent } from '@bromcom/ui';
import { TtsScheduleOrderComponent } from './tts-schedule-order.component';
import {
  AgGridPrioritiesDropdownComponent
} from '../ag-grid-priorities-dropdown/ag-grid-priorities-dropdown.component';
import { ICellRendererParams } from 'ag-grid-community';

export function gridOptions(this: TtsScheduleOrderComponent) {
  return transformToAGGridConfig({
    getRowId: params => params.data.id,
    animateRows: false,
    suppressRowClickSelection: true,
    suppressClickEdit: true,
    suppressCellFocus: true,
    noRowsOverlayComponent: AgGridNoDataComponent,
    noRowsOverlayComponentParams: {
      style: 'display: flex; justify-content: center; align-items: center; height: 100%; width: 100%; min-height: 162px; padding: 48px 0 0 0',
      noRowsMessageFunc: () => this.translate.instant(`No data available! No blocks were found.`)
    },
    tooltipShowDelay: 500,
    rowHeight: 48,
    columnDefs: [
      {
        field: 'yearGroup',
        headerName: this.translate.instant('Year Group'),
        minWidth: 120,
        flex: 1,
        menuTabs: [],
        sortable: false
      },
      {
        field: 'blockName',
        headerName: this.translate.instant('Block'),
        minWidth: 160,
        flex: 1,
        menuTabs: [],
        sortable: false,
        tooltipValueGetter: params => {
          return params.data.blockName?.length ? params.data.blockName : null
        },
        cellStyle: {
          display: 'block',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }
      },
      {
        field: 'subjects',
        headerName: this.translate.instant('Subjects'),
        minWidth: 150,
        flex: 1,
        menuTabs: [],
        sortable: false,
        tooltipValueGetter: params => {
          return params.data.subjects?.length ? params.data.subjects.join(', ') : null
        },
        cellStyle: {
          display: 'block',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }
      },
      {
        field: 'departments',
        headerName: this.translate.instant('Departments'),
        minWidth: 150,
        flex: 1,
        menuTabs: [],
        sortable: false,
        tooltipValueGetter: params => {
          return params.data.departments?.length ? params.data.departments.join(', ') : null
        },
        cellStyle: {
          display: 'block',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }
      },
      {
        field: 'order',
        headerName: this.translate.instant('Order'),
        minWidth: 150,
        flex: 1,
        editable: true,
        menuTabs: [],
        sortable: false,
        cellClass: 'order-cell',
        cellRenderer: AgGridPrioritiesDropdownComponent,
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            gridApi: this.gridApi,
            params,
            arrayLength: params.data.maxOrder
          }
        }
      }
    ]
  });
}
