const scheduledData = [
  {
    id: 1001001,
    blockTypeId: 1,
    blockName: 'Scheduled 1001',
    bandIds: [4],
    //Updated with classCount and PeriodCount which will be the maximum of classCount and periodCount in subjectToYearGroups
    classCount: 2,
    periodCount: 8,
    subjectToYearGroups: [
      {
        id: 5676,
        yearGroupId: 2,
        subjectId: 5676,
        classCount: 2,
        periodCount: 8,
        singlePeriodCount: 2,
        doublePeriodCount: 3,
        colorCode: '#fce3ac',
      },
    ],
    sessions: [
      {
        id: 10010010,
        subjectToYearGroupId: 5676,
        additionalStaffIds: [], mainStaffId: 1,
        roomId: 1,
        periodId: 12836,
        sessionName: 'Art',
        className: 'S1',
        joinedSessions: [10010010],
      },
      {
        id: 10010020,
        subjectToYearGroupId: 5676,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12836,
        sessionName: 'Art',
        className: 'S2',
        joinedSessions: [10010011],
      },
      {
        id: 10010021,
        subjectToYearGroupId: 5676,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12838,
        sessionName: 'Art',
        className: 'S3',
        joinedSessions: [10010021, 10010022],
      },
      {
        id: 10010022,
        subjectToYearGroupId: 5676,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12839,
        sessionName: 'Art',
        className: 'S3',
        joinedSessions: [10010021, 10010022],
      },
      {
        id: 10010023,
        subjectToYearGroupId: 5676,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12838,
        sessionName: 'Art',
        className: 'S5',
        joinedSessions: [10010023, 10010024],
      },
      {
        id: 10010024,
        subjectToYearGroupId: 5676,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12839,
        sessionName: 'Art',
        className: 'S5',
        joinedSessions: [10010023, 10010024],
      },
      {
        id: 10010025,
        subjectToYearGroupId: 5676,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12838,
        sessionName: 'Art',
        className: 'S7',
        joinedSessions: [10010025, 10010026],
      },
      {
        id: 10010026,
        subjectToYearGroupId: 5676,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12839,
        sessionName: 'Art',
        className: 'S7',
        joinedSessions: [10010025, 10010026],
      },
    ],
  },
  {
    id: 1001002,
    blockTypeId: 1,
    blockName: 'Scheduled 1002',
    bandIds: [4, 5],
    //Updated with classCount and PeriodCount which will be the maximum of classCount and periodCount in subjectToYearGroups
    classCount: 1,
    periodCount: 1,
    subjectToYearGroups: [
      {
        id: 5675,
        yearGroupId: 2,
        subjectId: 5675,
        classCount: 1,
        periodCount: 1,
        singlePeriodCount: 1,
        doublePeriodCount: 0,
        colorCode: '#727272',
      },
    ],
    sessions: [
      {
        id: 10010011,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12837,
        sessionName: 'Art',
        className: 'S81',
        joinedSessions: [10010011],
      },
    ],
  },
  {
    id: 10010021,
    blockTypeId: 1,
    blockName: 'Scheduled 1002',
    bandIds: [5, 6],
    //Updated with classCount and PeriodCount which will be the maximum of classCount and periodCount in subjectToYearGroups
    classCount: 1,
    periodCount: 1,
    subjectToYearGroups: [
      {
        id: 5675,
        yearGroupId: 2,
        subjectId: 5675,
        classCount: 1,
        periodCount: 1,
        singlePeriodCount: 1,
        doublePeriodCount: 0,
        colorCode: '#929292',
      },
    ],
    sessions: [
      {
        id: 100100111,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12838,
        sessionName: 'Art',
        className: 'S82',
        joinedSessions: [100100111],
      },
    ],
  },
  {
    id: 100100211,
    blockTypeId: 1,
    blockName: 'Scheduled 1002',
    bandIds: [6, 7],
    //Updated with classCount and PeriodCount which will be the maximum of classCount and periodCount in subjectToYearGroups
    classCount: 1,
    periodCount: 1,
    subjectToYearGroups: [
      {
        id: 5675,
        yearGroupId: 2,
        subjectId: 5675,
        classCount: 1,
        periodCount: 1,
        singlePeriodCount: 1,
        doublePeriodCount: 0,
        colorCode: '#a2a2a2',
      },
    ],
    sessions: [
      {
        id: 1001001111,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12839,
        sessionName: 'Art',
        className: 'S83',
        joinedSessions: [1001001111],
      },
      {
        id: 1001001112,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12839,
        sessionName: 'Art',
        className: 'S84',
        joinedSessions: [1001001112],
      },
    ],
  },
  {
    id: 1001002111,
    blockTypeId: 1,
    blockName: 'Scheduled 1002',
    bandIds: [5, 6, 7],
    //Updated with classCount and PeriodCount which will be the maximum of classCount and periodCount in subjectToYearGroups
    classCount: 1,
    periodCount: 1,
    subjectToYearGroups: [
      {
        id: 5675,
        yearGroupId: 2,
        subjectId: 5675,
        classCount: 1,
        periodCount: 1,
        singlePeriodCount: 1,
        doublePeriodCount: 0,
        colorCode: '#a2a2a2',
      },
    ],
    sessions: [
      {
        id: 10010011111,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12840,
        sessionName: 'Art',
        className: 'S85',
        joinedSessions: [10010011111],
      },
      {
        id: 10010011121,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12840,
        sessionName: 'Art',
        className: 'S86',
        joinedSessions: [10010011121],
      },
      {
        id: 10010011122,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12840,
        sessionName: 'Art',
        className: 'S87',
        joinedSessions: [10010011122],
      },
      {
        id: 10010011123,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12840,
        sessionName: 'Art',
        className: 'S88',
        joinedSessions: [10010011123],
      },
    ],
  },
  {
    id: 10010021111,
    blockTypeId: 1,
    blockName: 'Scheduled 1002',
    bandIds: [4, 5, 6, 7],
    //Updated with classCount and PeriodCount which will be the maximum of classCount and periodCount in subjectToYearGroups
    classCount: 1,
    periodCount: 1,
    subjectToYearGroups: [
      {
        id: 5675,
        yearGroupId: 2,
        subjectId: 5675,
        classCount: 1,
        periodCount: 1,
        singlePeriodCount: 1,
        doublePeriodCount: 0,
        colorCode: '#a2a2a2',
      },
    ],
    sessions: [
      {
        id: 100100111111,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12841,
        sessionName: 'Art',
        className: 'S89',
        joinedSessions: [100100111111],
      },
      {
        id: 100100111211,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12841,
        sessionName: 'Art',
        className: 'S90',
        joinedSessions: [100100111211],
      },
      {
        id: 100100111221,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12841,
        sessionName: 'Art',
        className: 'S91',
        joinedSessions: [100100111221],
      },
      {
        id: 100100111231,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12841,
        sessionName: 'Art',
        className: 'S92',
        joinedSessions: [100100111231],
      },
    ],
  },
  {
    id: 10010003,
    blockTypeId: 1,
    blockName: 'Scheduled 10003',
    bandIds: [8],
    //Updated with classCount and PeriodCount which will be the maximum of classCount and periodCount in subjectToYearGroups
    classCount: 1,
    periodCount: 1,
    subjectToYearGroups: [
      {
        id: 5677,
        yearGroupId: 3,
        subjectId: 5677,
        classCount: 1,
        periodCount: 1,
        singlePeriodCount: 1,
        doublePeriodCount: 0,
        colorCode: '#a2a2a2',
      },
    ],
    sessions: [
      {
        id: 10010012,
        subjectToYearGroupId: 5677,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12838,
        sessionName: 'Art',
        className: 'S9',
        joinedSessions: [10010012],
      },
    ],
  },
  {
    id: 10010004,
    blockTypeId: 1,
    blockName: 'Scheduled 10004',
    bandIds: [12],
    //Updated with classCount and PeriodCount which will be the maximum of classCount and periodCount in subjectToYearGroups
    classCount: 1,
    periodCount: 1,
    subjectToYearGroups: [
      {
        id: 5677,
        yearGroupId: 4,
        subjectId: 5677,
        classCount: 1,
        periodCount: 1,
        singlePeriodCount: 1,
        doublePeriodCount: 0,
        colorCode: '#a2a2a2',
      },
    ],
    sessions: [
      {
        id: 10010013,
        subjectToYearGroupId: 5677,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12839,
        sessionName: 'History',
        className: 'S10',
        joinedSessions: [10010013],
      },
    ],
  },
  {
    id: 10010005,
    blockTypeId: 1,
    blockName: 'Scheduled 10005',
    bandIds: [16],
    //Updated with classCount and PeriodCount which will be the maximum of classCount and periodCount in subjectToYearGroups
    classCount: 1,
    periodCount: 1,
    subjectToYearGroups: [
      {
        id: 5676,
        yearGroupId: 5,
        subjectId: 5676,
        classCount: 1,
        periodCount: 1,
        singlePeriodCount: 1,
        doublePeriodCount: 0,
        colorCode: '#a2a2a2',
      },
    ],
    sessions: [
      {
        id: 10010014,
        subjectToYearGroupId: 5676,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: 12838,
        sessionName: 'Art',
        className: 'S11',
        joinedSessions: [10010014],
      },
    ],
  },
];
const blocksData = [
  {
    id: 10003,
    blockTypeId: 1,
    blockName: 'Simple',
    bandIds: [7],
    /*TO DO: classCount and PeriodCount will be a calculated value based on the classCount and periodCount in subjectToYearGroups
    which will be the maximum of classCount and periodCount in subjectToYearGroups*/
    classCount: 4,
    periodCount: 9,
    subjectToYearGroups: [
      {
        id: 5685,
        yearGroupId: 2,
        subjectId: 5685,
        classCount: 4,
        periodCount: 9,
        singlePeriodCount: 3,
        doublePeriodCount: 3,
        colorCode: '#059669',
      },
    ],
    sessions: [
      {
        id: 10003001,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C1',
        joinedSessions: [10003001],
      },
      {
        id: 10003002,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C1',
        joinedSessions: [10003002],
      },
      {
        id: 10003003,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C1',
        joinedSessions: [10003003],
      },
      {
        id: 10003004,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C2',
        joinedSessions: [10003004],
      },
      {
        id: 10003005,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C2',
        joinedSessions: [10003005],
      },
      {
        id: 10003006,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C2',
        joinedSessions: [10003006],
      },
      {
        id: 10003007,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C3',
        joinedSessions: [10003007],
      },
      {
        id: 10003008,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C3',
        joinedSessions: [10003008],
      },
      {
        id: 10003009,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C3',
        joinedSessions: [10003009],
      },
      {
        id: 10003010,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C4',
        joinedSessions: [10003010],
      },
      {
        id: 10003011,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C4',
        joinedSessions: [10003011],
      },
      {
        id: 10003012,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C4',
        joinedSessions: [10003012],
      },
      {
        id: 10003013,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C1',
        joinedSessions: [10003013, 10003014],
      },
      {
        id: 10003014,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C1',
        joinedSessions: [10003013, 10003014],
      },
      {
        id: 10003015,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C1',
        joinedSessions: [10003015, 10003016],
      },
      {
        id: 10003016,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C1',
        joinedSessions: [10003015, 10003016],
      },
      {
        id: 10003017,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C1',
        joinedSessions: [10003017, 10003018],
      },
      {
        id: 10003018,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C1',
        joinedSessions: [10003017, 10003018],
      },
      {
        id: 10003019,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C2',
        joinedSessions: [10003019, 10003020],
      },
      {
        id: 10003020,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C2',
        joinedSessions: [10003019, 10003020],
      },
      {
        id: 10003021,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C2',
        joinedSessions: [10003021, 10003022],
      },
      {
        id: 10003022,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C2',
        joinedSessions: [10003021, 10003022],
      },
      {
        id: 10003023,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C2',
        joinedSessions: [10003023, 10003024],
      },
      {
        id: 10003024,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C2',
        joinedSessions: [10003023, 10003024],
      },
      {
        id: 10003025,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C3',
        joinedSessions: [10003025, 10003026],
      },
      {
        id: 10003026,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C3',
        joinedSessions: [10003025, 10003026],
      },
      {
        id: 10003027,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C3',
        joinedSessions: [10003027, 10003028],
      },
      {
        id: 10003028,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C3',
        joinedSessions: [10003027, 10003028],
      },
      {
        id: 10003029,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C3',
        joinedSessions: [10003029, 10003030],
      },
      {
        id: 10003030,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C3',
        joinedSessions: [10003029, 10003030],
      },
      {
        id: 10003031,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C4',
        joinedSessions: [10003031, 10003032],
      },
      {
        id: 10003032,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C4',
        joinedSessions: [10003031, 10003032],
      },
      {
        id: 10003033,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C4',
        joinedSessions: [10003033, 10003034],
      },
      {
        id: 10003034,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C4',
        joinedSessions: [10003033, 10003034],
      },
      {
        id: 10003035,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C4',
        joinedSessions: [10003035, 10003036],
      },
      {
        id: 10003036,
        subjectToYearGroupId: 5685,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'Drama',
        className: 'C4',
        joinedSessions: [10003035, 10003036],
      },
    ],
  },
  {
    id: 10004,
    blockTypeId: 2,
    blockName: 'Linear',
    bandIds: [4,5,6,7],
    classCount: 3,
    periodCount: 2,
    subjectToYearGroups: [
      {
        id: 5675,
        yearGroupId: 2,
        subjectId: 5675,
        classCount: 3,
        periodCount: 2,
        singlePeriodCount: 2,
        doublePeriodCount: 0,
        colorCode: '#2DD4BF',
      },
      {
        id: 5684,
        yearGroupId: 2,
        subjectId: 5684,
        classCount: 3,
        periodCount: 2,
        singlePeriodCount: 2,
        doublePeriodCount: 0,
        colorCode: '#C084FC',
      },
      {
        subjectToYearGroupId: 5681,
        yearGroupId: 2,
        subjectId: 5681,
        classCount: 3,
        periodCount: 2,
        singlePeriodCount: 0,
        doublePeriodCount: 1,
        colorCode: '#84CC16',
      },
    ],
    sessions: [
      {
        id: 10004001,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Art',
        className: 'C1',
        joinedSessions: [10004001],
      },
      {
        id: 10004002,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Art',
        className: 'C1',
        joinedSessions: [10004002],
      },
      {
        id: 10004003,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Art',
        className: 'C2',
        joinedSessions: [10004003],
      },
      {
        id: 10004004,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Art',
        className: 'C2',
        joinedSessions: [10004004],
      },
      {
        id: 10004005,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Art',
        className: 'C3',
        joinedSessions: [10004005],
      },
      {
        id: 10004006,
        subjectToYearGroupId: 5675,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Art',
        className: 'C3',
        joinedSessions: [10004006],
      },
      {
        id: 10004007,
        subjectToYearGroupId: 5684,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'DT',
        className: 'C1',
        joinedSessions: [10004007],
      },
      {
        id: 10004008,
        subjectToYearGroupId: 5684,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'DT',
        className: 'C1',
        joinedSessions: [10004008],
      },
      {
        id: 10004009,
        subjectToYearGroupId: 5684,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'DT',
        className: 'C2',
        joinedSessions: [10004009],
      },
      {
        id: 10004010,
        subjectToYearGroupId: 5684,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'DT',
        className: 'C2',
        joinedSessions: [10004010],
      },
      {
        id: 10004011,
        subjectToYearGroupId: 5684,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'DT',
        className: 'C3',
        joinedSessions: [10004011],
      },
      {
        id: 10004012,
        subjectToYearGroupId: 5684,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'DT',
        className: 'C3',
        joinedSessions: [10004012],
      },
      {
        id: 10004013,
        subjectToYearGroupId: 5681,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Chemistry',
        className: 'C1',
        joinedSessions: [10004013, 10004014],
      },
      {
        id: 10004014,
        subjectToYearGroupId: 5681,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Chemistry',
        className: 'C1',
        joinedSessions: [10004013, 10004014],
      },
      {
        id: 10004015,
        subjectToYearGroupId: 5681,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Chemistry',
        className: 'C2',
        joinedSessions: [10004015, 10004016],
      },
      {
        id: 10004016,
        subjectToYearGroupId: 5681,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Chemistry',
        className: 'C2',
        joinedSessions: [10004015, 10004016],
      },
      {
        id: 10004017,
        subjectToYearGroupId: 5681,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Chemistry',
        className: 'C3',
        joinedSessions: [10004017, 10004018],
      },
      {
        id: 10004018,
        subjectToYearGroupId: 5681,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Chemistry',
        className: 'C3',
        joinedSessions: [10004017, 10004018],
      },
    ],
  },
  {
    id: 10005,
    blockTypeId: 3,
    blockName: 'Options',
    bandIds: [4,5,6,7],
    classCount: 4,
    periodCount: 6,
    subjectToYearGroups: [
      {
        id: 5714,
        yearGroupId: 2,
        subjectId: 5714,
        classCount: 4,
        periodCount: 6,
        singlePeriodCount: 6,
        doublePeriodCount: 0,
        colorCode: '#5B21B6',
      },
      {
        id: 5715,
        yearGroupId: 2,
        subjectId: 5715,
        classCount: 4,
        periodCount: 6,
        singlePeriodCount: 6,
        doublePeriodCount: 0,
        colorCode: '#F97316',
      },
      {
        id: 5716,
        yearGroupId: 2,
        subjectId: 5716,
        classCount: 4,
        periodCount: 6,
        singlePeriodCount: 6,
        doublePeriodCount: 0,
        colorCode: '#FECDD3',
      },
      {
        id: 5717,
        yearGroupId: 2,
        subjectId: 5717,
        classCount: 4,
        periodCount: 6,
        singlePeriodCount: 6,
        doublePeriodCount: 0,
        colorCode: '#F87171',
      },
    ],
    sessions: [
      {
        id: 10005001,
        subjectToYearGroupId: 5714,
        additionalStaffIds: [], mainStaffId: 0,
        roomId: null,
        periodId: null,
        sessionName: 'ASDAN',
        className: 'C1',
        joinedSessions: [10005001],
      },
      {
        id: 10005002,
        subjectToYearGroupId: 5714,
        additionalStaffIds: [], mainStaffId: 1,
        roomId: null,
        periodId: null,
        sessionName: 'ASDAN',
        className: 'C1',
        joinedSessions: [10005002],
      },
      {
        id: 10005003,
        subjectToYearGroupId: 5714,
        additionalStaffIds: [], mainStaffId: 3,
        roomId: null,
        periodId: null,
        sessionName: 'ASDAN',
        className: 'C1',
        joinedSessions: [10005003],
      },
      {
        id: 10005004,
        subjectToYearGroupId: 5714,
        additionalStaffIds: [], mainStaffId: 2,
        roomId: null,
        periodId: null,
        sessionName: 'ASDAN',
        className: 'C1',
        joinedSessions: [10005004],
      },
      {
        id: 10005005,
        subjectToYearGroupId: 5714,
        additionalStaffIds: [], mainStaffId: 2,
        roomId: null,
        periodId: null,
        sessionName: 'ASDAN',
        className: 'C1',
        joinedSessions: [10005005],
      },
      {
        id: 10005006,
        subjectToYearGroupId: 5714,
        additionalStaffIds: [], mainStaffId: 3,
        roomId: null,
        periodId: null,
        sessionName: 'ASDAN',
        className: 'C1',
        joinedSessions: [10005006],
      },
      {
        id: 10005007,
        subjectToYearGroupId: 5715,
        additionalStaffIds: [], mainStaffId: 2,
        roomId: null,
        periodId: null,
        sessionName: 'French',
        className: 'C2',
        joinedSessions: [10005007],
      },
      {
        id: 10005008,
        subjectToYearGroupId: 5715,
        additionalStaffIds: [], mainStaffId: 2,
        roomId: null,
        periodId: null,
        sessionName: 'French',
        className: 'C2',
        joinedSessions: [10005008],
      },
      {
        id: 10005009,
        subjectToYearGroupId: 5715,
        additionalStaffIds: [], mainStaffId: 2,
        roomId: null,
        periodId: null,
        sessionName: 'French',
        className: 'C2',
        joinedSessions: [10005009],
      },
      {
        id: 10005010,
        subjectToYearGroupId: 5715,
        additionalStaffIds: [], mainStaffId: 2,
        roomId: null,
        periodId: null,
        sessionName: 'French',
        className: 'C2',
        joinedSessions: [10005010],
      },
      {
        id: 10005011,
        subjectToYearGroupId: 5715,
        additionalStaffIds: [], mainStaffId: 2,
        roomId: null,
        periodId: null,
        sessionName: 'French',
        className: 'C2',
        joinedSessions: [10005011],
      },
      {
        id: 10005012,
        subjectToYearGroupId: 5715,
        additionalStaffIds: [], mainStaffId: 2,
        roomId: null,
        periodId: null,
        sessionName: 'French',
        className: 'C2',
        joinedSessions: [10005012],
      },
      {
        id: 10005013,
        subjectToYearGroupId: 5716,
        additionalStaffIds: [], mainStaffId: 3,
        roomId: null,
        periodId: null,
        sessionName: 'Physics',
        className: 'C3',
        joinedSessions: [10005013],
      },
      {
        id: 10005014,
        subjectToYearGroupId: 5716,
        additionalStaffIds: [], mainStaffId: 3,
        roomId: null,
        periodId: null,
        sessionName: 'Physics',
        className: 'C3',
        joinedSessions: [10005014],
      },
      {
        id: 10005015,
        subjectToYearGroupId: 5716,
        additionalStaffIds: [], mainStaffId: 3,
        roomId: null,
        periodId: null,
        sessionName: 'Physics',
        className: 'C3',
        joinedSessions: [10005015],
      },
      {
        id: 10005016,
        subjectToYearGroupId: 5716,
        additionalStaffIds: [], mainStaffId: 3,
        roomId: null,
        periodId: null,
        sessionName: 'Physics',
        className: 'C3',
        joinedSessions: [10005016],
      },
      {
        id: 10005017,
        subjectToYearGroupId: 5716,
        additionalStaffIds: [], mainStaffId: 3,
        roomId: null,
        periodId: null,
        sessionName: 'Physics',
        className: 'C3',
        joinedSessions: [10005017],
      },
      {
        id: 10005018,
        subjectToYearGroupId: 5716,
        additionalStaffIds: [], mainStaffId: 3,
        roomId: null,
        periodId: null,
        sessionName: 'Physics',
        className: 'C3',
        joinedSessions: [10005018],
      },
      {
        id: 10005019,
        subjectToYearGroupId: 5717,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Science',
        className: 'C4',
        joinedSessions: [10005019],
      },
      {
        id: 10005020,
        subjectToYearGroupId: 5717,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Science',
        className: 'C4',
        joinedSessions: [10005020],
      },
      {
        id: 10005021,
        subjectToYearGroupId: 5717,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Science',
        className: 'C4',
        joinedSessions: [10005021],
      },
      {
        id: 10005022,
        subjectToYearGroupId: 5717,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Science',
        className: 'C4',
        joinedSessions: [10005022],
      },
      {
        id: 10005023,
        subjectToYearGroupId: 5717,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Science',
        className: 'C4',
        joinedSessions: [10005023],
      },
      {
        id: 10005024,
        subjectToYearGroupId: 5717,
        additionalStaffIds: [], mainStaffId: null,
        roomId: null,
        periodId: null,
        sessionName: 'Science',
        className: 'C4',
        joinedSessions: [10005024],
      },
    ],
  },
];

const generateBlocks = (db, yearGroupIdString) => {
  const yearGroupId = Number(yearGroupIdString)
  const yeargroup = generatedYeargroupsMockData().find((yg) => yg.id == yearGroupId);

  const subjects = db
    .get('get_subjects')
    .value()
    .sort((a, b) => (a.id < b.id ? -1 : 1));
  const minSubjectId = subjects[0].id;

  const generated = [];
  //const generated = [...Array(4).keys()].map((blockIndex) => {
  //  const blockId = 1000 + (blockIndex + 1);
  //  const bandIds = [...Array(blockIndex + 1).keys()];

  //  const subjectToYearGroups = [...Array(yearGroupId).keys()].map((subjectToYearGroupIndex) => {
  //    const subjectId = minSubjectId + subjectToYearGroupIndex + blockIndex;
  //    return {
  //      subjectToYearGroupId: subjectToYearGroupIndex + 1,
  //      yearGroupId: Number(yearGroupId),
  //      subjectId: subjectId,
  //      classCount: blockIndex + 1,
  //      periodCount: Math.floor((blockIndex + 1) / 2) * 2 + (blockIndex + 1),
  //      singlePeriodCount: blockIndex + 1,
  //      doublePeriodCount: Math.floor((blockIndex + 1) / 2),
  //      colorCode: `#${subjects.find((subject) => subject.id == subjectId)?.color ?? '000'}`,
  //    };
  //  });

  //  //Generating sessions
  //  let sessions = [];
  //  let maxClassCount = subjectToYearGroups.length > 0 ? subjectToYearGroups[0].classCount : 0;
  //  let maxPeriodCount = subjectToYearGroups.length > 0 ? subjectToYearGroups[0].periodCount : 0;
  //  subjectToYearGroups.forEach((subjectToYearGroup) => {
  //    if (subjectToYearGroup.classCount > maxClassCount) {
  //      maxClassCount = subjectToYearGroup.classCount;
  //    }
  //    if (subjectToYearGroup.periodCount > maxPeriodCount) {
  //      maxPeriodCount = subjectToYearGroup.classCount;
  //    }
  //    const numberOfRelatedSessions =
  //      (subjectToYearGroup.doublePeriodCount * 2 + subjectToYearGroup.singlePeriodCount) *
  //      subjectToYearGroup.classCount;

  //    const relatedSessions = [...Array(numberOfRelatedSessions).keys()].map((sessionIndex) => {
  //      const sessionId = blockId * 1000 + (sessionIndex + 1);
  //      return {
  //        id: sessionId,
  //        subjectToYearGroupId: subjectToYearGroup.id,
  //        additionalStaffIds: [], mainStaffId: null,
  //        roomId: null,
  //        periodId: null,
  //        sessionName: `${subjects.find((subject) => subject.id == subjectToYearGroup.id)?.shortName ?? ''}`,
  //        className: `C${
  //          Math.floor(
  //            sessionIndex / (subjectToYearGroup.doublePeriodCount * 2 + subjectToYearGroup.singlePeriodCount)
  //          ) + 1
  //        }`,
  //        joinedSessions: [sessionId],
  //      };
  //    });
  //    const numberOfDoublesPerPeriod = subjectToYearGroup.doublePeriodCount * 2;
  //    for (let j = 0; j < subjectToYearGroup.classCount; j++) {
  //      for (
  //        let i = j * (subjectToYearGroup.doublePeriodCount * 2 + subjectToYearGroup.singlePeriodCount);
  //        i <
  //        j * (subjectToYearGroup.doublePeriodCount * 2 + subjectToYearGroup.singlePeriodCount) +
  //          numberOfDoublesPerPeriod;
  //        i += 2
  //      ) {
  //        relatedSessions[i].joinedSessions = [
  //          ...relatedSessions[i].joinedSessions,
  //          ...relatedSessions[i + 1].joinedSessions,
  //        ];
  //        relatedSessions[i + 1].joinedSessions = [...relatedSessions[i].joinedSessions];
  //      }
  //    }
  //    sessions = [...sessions, ...relatedSessions];
  //  });
  //  return {
  //    id: blockId,
  //    blockTypeId: yearGroupId,
  //    blockName: `Simple ${blockId}`,
  //    bandIds: bandIds.map((bandId) => (((yearGroupId -1) * 4) + bandId)),
  //    classCount: maxClassCount,
  //    periodCount: maxPeriodCount,
  //    subjectToYearGroups: subjectToYearGroups,
  //    sessions: sessions,
  //  };
  //});

  return [...scheduledData.filter(data => data.subjectToYearGroups[0].yearGroupId == yearGroupId), ...generated.concat(blocksData)];
  // return [];
};

const generatedYeargroupsMockData = (req) =>
  [...Array(12).keys()].map((index) => {
    return {
      id: index + 1,
      name: `${index + 1}`,
      description: `YG(${index + 1})`,
      ncYearGroupId: index + 1 + 2,
      nextYearGroupId: index + 1 + 1,
      isExcluded: false,
      originalId: index + 1,
      projectId: req?.params?.projectId,
      totalPupilCount: 10 * (index + 1),
    };
  });

module.exports = { generateBlocks, generatedYeargroupsMockData, scheduledData };
