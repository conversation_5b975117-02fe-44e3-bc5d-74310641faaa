import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  Renderer2,
  ViewChild
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { LoadingSpinnerService, SnackbarService } from '@bromcom/ui';
import { STAFFING_VIEW_TYPES } from '../../../../../_shared/enums/StaffingViewTypes';
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  distinctUntilChanged,
  EMPTY,
  finalize,
  forkJoin,
  map,
  Observable,
  startWith,
  Subject,
  switchMap,
  takeUntil,
  tap
} from 'rxjs';
import { CurriculumPlanService } from '../../../../services/curriculum-plan.service';
import { RelationshipsService } from '../../../../services/relationships.service';
import { BaseTwoPaneComponent } from '../common/two-pane.component';
import { SchedulingService } from '../../../../services/scheduling.service';
import { NewTimetableService } from '../../../../../timetables/services/new-timetable.service';
import { IWithPeriodStructureResponse } from '../../../../../_shared/models/IWithPeriodStructureResponse';
import { IYearGroupWithBands } from '../../../../../_shared/models/IYearGroupWithBands';
import { IListOfBlocksResponse } from '../../../../../_shared/models/IListOfBlocksResponse';
import { BandService } from '../../../../services/band.service';
import { TranslateService } from '@ngx-translate/core';
import { SCHEDULING_VIEW_TYPES } from '../../../../../_shared/enums/SchedulingViewTypes';
import { ORGANISED_BY_TYPES } from '../../../../../_shared/enums/OrganisedByTypes';
import { NonContactCodesService } from '../../../../../projects/services/non-contact-codes.service';
import { INonContactCodes } from '../../../../../_shared/models/INonContactCodes';
import { CurriculumPlanBlocksService } from '../../../../services/curriculum-plan-blocks';
import { ISchedulePageInitProperties } from '../../../../../_shared/models/ISchedulePageInitProperties';
import { ICurriculumPlanStaff } from '../../../../../_shared/models/ICurriculumPlanStaff';
import {
  CheckForMissingStaffRoomComponent
} from '../curriculum-plan/components/check-for-missing-staff-room/check-for-missing-staff-room.component';
import { ICurriculumPlanRoom } from '../../../../../_shared/models/ICurriculumPlanRoom';
import { SwapDataModalComponent } from '../common/swap-data-modal/swap-data-modal.component';
import { NotesComponent } from '../planning-relationship-actions/notes/notes.component';
import { IConflict } from '../../../../../_shared/models/IConflict';
import { ISwapStaffDataEntity } from '../../../../../_shared/models/ISwapStaffDataEntity';
import { IStaffWithContactInfo } from '../../../../../_shared/models/IStaffWithContactInfo';
import { GeneralModalComponent } from '../../../../../_shared/components/general-modal/general-modal.component';
import { ISwapRoomDataEntity } from '../../../../../_shared/models/ISwapRoomDataEntity';
import { IRooms } from '../../../../../_shared/models/IRooms';
import { PlanningRelationshipsService } from '../../../../services/planning-relationships.service';
import {
  formatBlock
} from '../curriculum-plan/components/curriculum-plan-container/curriculum-plan-container-formatter-helpers';
import { MatMenuTrigger } from "@angular/material/menu";
import {
  CurriculumPlanSubjectsComponent
} from '../curriculum-plan/components/curriculum-plan-subjects/curriculum-plan-subjects.component';
import {
  CurriculumPlanStaffComponent
} from '../curriculum-plan/components/curriculum-plan-staff/curriculum-plan-staff.component';
import {
  CurriculumPlanRoomsComponent
} from '../curriculum-plan/components/curriculum-plan-rooms/curriculum-plan-rooms.component';
import { IMissingEntity } from '../../../../../_shared/models/IMissingStaffRoomList';
import {
  LunchSplitPeriodsModalComponent
} from './components/lunch-split-periods-modal/lunch-split-periods-modal.component';
import {
  TimetableSpecificationComponent
} from './components/timetable-specification/timetable-specification.component';
import { AutoScheduleComponent } from './components/auto-schedule/auto-schedule.component';
import { NonContactCodesComponent } from './components/non-contact-codes/non-contact-codes.component';
import { SubstituteStaffModalComponent } from '../common/substitute-staff-modal/substitute-staff-modal.component';
import { ISessionAssociations } from '../../../../../_shared/models/ISessionAssociations';
import { TimetableAreaComponent } from "./components/timetable-area/timetable-area.component";
import { MatTabChangeEvent } from '@angular/material/tabs';
import { IYearGroup } from '../../../../../_shared/models/IYearGroup';
import { ReportsComponent } from './components/reports/reports.component';

@Component({
  selector: 'bromcom-scheduling',
  templateUrl: './scheduling.component.html',
  styleUrls: ['./scheduling.component.scss']
})
export class SchedulingComponent extends BaseTwoPaneComponent implements OnInit, OnDestroy {
  @ViewChild('warningModalPopup') warningModalPopup!: GeneralModalComponent;
  @ViewChild('editSubjectsModal') editSubjectsModal!: ElementRef;
  @ViewChild('editStaffsModal') editStaffsModal!: ElementRef;
  @ViewChild('editRoomsModal') editRoomsModal!: ElementRef;
  @ViewChild('notesPanelComponent') notesPanelComponent!: NotesComponent;

  @ViewChild(MatMenuTrigger) trigger: MatMenuTrigger | undefined;

  @ViewChild('timetableSpecificationComponent') timetableSpecificationComponent!: TimetableSpecificationComponent;
  @ViewChild('checkForMissingComponent') checkForMissingComponent!: CheckForMissingStaffRoomComponent;
  @ViewChild('swapDataModal') swapDataModal!: SwapDataModalComponent;
  @ViewChild('autoSchedule') autoScheduleComponent!: AutoScheduleComponent;
  @ViewChild('lunchSplitPeriods') lunchSplitPeriods!: LunchSplitPeriodsModalComponent;
  @ViewChild('reportsComponent') reportsComponent!: ReportsComponent;
  @ViewChild('nonContactCodesComponent') nonContactCodesComponent!: NonContactCodesComponent;
  @ViewChild('timetableAreaComponent') timetableAreaComponent!: TimetableAreaComponent;

  @ViewChild('subjectsSidePanel') subjectsSidePanel!: CurriculumPlanSubjectsComponent;
  @ViewChild('staffSidePanel') staffSidePanel!: CurriculumPlanStaffComponent;
  @ViewChild('roomSidePanel') roomSidePanel!: CurriculumPlanRoomsComponent;
  @ViewChild('substituteStaffModal') substituteStaffModal!: SubstituteStaffModalComponent;

  @Input() projectId!: number;
  @Input() timetableId!: number;
  @Input() projectName!: string;
  @Input() timetableName!: string;

  @Output() changeToCurriculumTab = new EventEmitter<any>();
  @Output() changeToSchedulingTab = new EventEmitter<any>();


  viewType = STAFFING_VIEW_TYPES.Blocks;
  viewTypes = STAFFING_VIEW_TYPES;

  schedulingViewTypes = SCHEDULING_VIEW_TYPES;
  selectedSchedulingViewType = SCHEDULING_VIEW_TYPES.Detailed;

  organisedByTypes = ORGANISED_BY_TYPES;
  selectedOrganisedByType$ = new BehaviorSubject(ORGANISED_BY_TYPES.YearGroup);

  selectedYearGroupControl = new FormControl<number | null>(null);
  showAll: BehaviorSubject<boolean> = new BehaviorSubject(true);
  missingFilter$: BehaviorSubject<('Room' | 'Staff')[]> = new BehaviorSubject<('Room' | 'Staff')[]>([]);
  missingSession$: BehaviorSubject<IMissingEntity | null> = new BehaviorSubject<IMissingEntity | null>(null);
  updateBlockLayout$: BehaviorSubject<IListOfBlocksResponse | null> = new BehaviorSubject<IListOfBlocksResponse | null>(
    null
  );
  yearGroups: IYearGroupWithBands[] = [];
  filteredYearGroups: IYearGroupWithBands[] = [];

  periodStructure!: IWithPeriodStructureResponse;
  blockData: IListOfBlocksResponse[] = [];
  filterBlocks$!: Observable<IListOfBlocksResponse[]>;

  nonContactCodes!: INonContactCodes[];
  displayTimetableData!: ICurriculumPlanStaff | ICurriculumPlanRoom;

  isMissingStaffRoomOpen = false;
  resetMissingStaffRoomFilter = false;
  staffRoomFilterOptions = [
    { text: 'Staff', id: 0 },
    { text: 'Room', id: 1 }
  ]

  openedModal: string | null = null;

  readonly unsubscribe$: Subject<void> = new Subject();
  data$ = new BehaviorSubject<IListOfBlocksResponse[] | null>(null);
  staffRoomSelection$ = new BehaviorSubject<number[]>([]);
  staffsData: ISwapStaffDataEntity[] = [];
  staffList: IStaffWithContactInfo[] = [];
  roomsData: ISwapRoomDataEntity[] = [];
  roomList: IRooms[] = [];
  selectedStaffData!: ISwapStaffDataEntity;
  selectedRoomData!: ISwapRoomDataEntity;
  warningModalTitle!: string;
  warningModalMain = this.translationService.instant('Please resolve or accept the conflicts for the swap to be available.');
  conflictList: IConflict[] = [];
  isSwapStaffView!: boolean;
  isSplitLunch = false;
  private ttLoaded = false;

  constructor(
    private translationService: TranslateService,
    private relationships: RelationshipsService,
    private snackbar: SnackbarService,
    private curriculumPlanService: CurriculumPlanService,
    private curriculumPlanBlockService: CurriculumPlanBlocksService,
    public schedulingService: SchedulingService,
    public bandService: BandService,
    private loading: LoadingSpinnerService,
    private timetableService: NewTimetableService,
    private nonContactCodesService: NonContactCodesService,
    private planningRelationships: PlanningRelationshipsService,
    renderer: Renderer2
  ) {
    super(renderer);

    this.schedulingService.pageInitProps$.pipe(takeUntil(this.unsubscribe$)).subscribe(pageInitProps => {
      this.pageInit(pageInitProps);
      if (pageInitProps.showConflict) {
        this.schedulingService.scrollToConflict$.next(pageInitProps.showConflict);
      }

      if (pageInitProps.showSession) {
        this.schedulingService.scrollToSession$.next(pageInitProps.showSession);
        const selection = [this.staffRoomFilterOptions.find(opt => opt.text === pageInitProps.showSession!.missingAssignment!)!.id];
        setTimeout(() => {
          this.onApplyFilter({
            selectedItemIds: selection,
            clearFilter: false
          });
          this.missingSession$.next(pageInitProps.showSession ?? null);
          this.staffRoomSelection$.next(selection);
        }, 1000)

      }
    })
  }

  ngOnInit(): void {

    if (this.schedulingService.hasPageInitProp) {
      this.pageInit(this.schedulingService.pageInitProps$.value);
    }
    this.loadInitialData();
    this.setupYearGroupControl();
    this.setupBlockLayoutSubscription();
    this.setupFilterBlocksSubscription();
    this.setupStateChangeSubscriptions();
  
    this.schedulingService.filteredData$.next(null);

     this.schedulingService.triggerFetchLatestData$
    .pipe(takeUntil(this.unsubscribe$))
    .subscribe(() => this.fetchLatestData());

    this.refreshData();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    this.schedulingService.pageInitProps$.next({});
  }

  pageInit(pageInitProps: ISchedulePageInitProperties) {
    if (pageInitProps.showConflict) {
      this.organiseBy(ORGANISED_BY_TYPES[pageInitProps.showConflict.type]);
      this.schedulingService.pageInitProps$.next({})
    }

    if (pageInitProps.showSession) {
      this.organiseBy(ORGANISED_BY_TYPES.YearGroup);
      this.schedulingService.pageInitProps$.next({});
    }
  }

  private setupYearGroupControl(): void {
    // Handle year group control value changes
    this.selectedYearGroupControl.valueChanges
      .pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$))
      .subscribe(selectedYearGroup => {
        this.curriculumPlanService.selectedYearGroup$.next(selectedYearGroup);
        this.fetchNonContactAssociations();
      });

    this.curriculumPlanService.selectedYearGroup$
      .pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$))
      .subscribe(selectedYearGroup => {
        if (selectedYearGroup) {
          this.updateBlocksForYearGroup(selectedYearGroup);
        }
      });
  }

  private updateBlocksForYearGroup(selectedYearGroup: number): void {
    const filterOptions = this.curriculumPlanBlockService.filterOptionsForBlocks$.getValue();
    this.curriculumPlanBlockService.getBlocksByYearGroupFilter(this.timetableId, selectedYearGroup, filterOptions)
      .subscribe(blockResponse => {
        const blocks = formatBlock.call(this, blockResponse);
        this.curriculumPlanBlockService.blocksByYearGroup$.next(blocks);

        this.selectedYearGroupControl.setValue(selectedYearGroup);
        this.schedulingService.stateChanged$.next();
      });
  }

  private setupBlockLayoutSubscription(): void {
  // Update block layout and data streams
  combineLatest([
    this.schedulingService.fetchedBlocks$.pipe(startWith(null)),
    this.updateBlockLayout$
  ])
    .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$),
      map(([blockData, updatedBlockLayout]) => {
        if (!blockData) return [];
        if (updatedBlockLayout) {
          const blockIndex = blockData.findIndex((block) => block.id === updatedBlockLayout.id);
          if (blockIndex > -1) {
            blockData[blockIndex].sessions = updatedBlockLayout?.sessions;
          }
        }
        return [...blockData];
      })
    )
    .subscribe((data) => this.data$.next(data));
  }

  private setupFilterBlocksSubscription(): void {
  // Filter blocks based on year group and type
  this.filterBlocks$ = combineLatest([
    this.data$,
    this.selectedYearGroupControl.valueChanges,
    this.selectedOrganisedByType$
  ])
    .pipe(
      takeUntil(this.unsubscribe$),
      map(([blocks, selectedYearGroup, selectedOrganisedByType]) => {
        if (selectedOrganisedByType === ORGANISED_BY_TYPES.YearGroup) {
          this.updateSessionAssociations(blocks);
          return blocks!.filter(b => b.subjectToYearGroups[0]?.yearGroupId === selectedYearGroup);
        } else {
          return blocks!;
        }
      })
    );
  }

  private loadInitialData(): void {
    // Fetch initial data for the page
    forkJoin([
      this.schedulingService.getYearGroupList(this.projectId),
      this.schedulingService.getStaffListWithContactInfo(this.projectId, this.timetableId),
      this.schedulingService.getRoomList(this.projectId),
      this.nonContactCodesService.getList(this.projectId),
      this.timetableService.getPeriodStructureByTimetableId(this.timetableId),

      this.schedulingService.getSubjectList(this.projectId),
      this.relationships.getStaffToSubjectRelations(this.projectId, this.timetableId),
      this.schedulingService.getDepartmentList(this.projectId),
      this.schedulingService.getRoomsWithDepartment(this.projectId, this.timetableId)
  ]).subscribe(([yearGroups, staffList, roomList, nonContactCodes, periodStructure]) => {
      this.initializeYearGroups(yearGroups);
      this.staffList = staffList;
      this.roomList = roomList;
      this.nonContactCodes = nonContactCodes;
      this.periodStructure = this.markPeriodsAndRemoveAMPMPeriods(periodStructure);
  });

    this.fetchConflictData();

    this.timetableService.getPeriodStructureByTimetableId(this.timetableId).subscribe((periodStructure) => {
      this.isSplitLunch = periodStructure.periodStructure.isSplitLunch;
      this.periodStructure = this.markPeriodsAndRemoveAMPMPeriods(periodStructure);
    });
  }

  private initializeYearGroups(yearGroups: IYearGroup[]): void {
    this.yearGroups = yearGroups
      .filter(yg => !yg.isExcluded)
      .map(yg => ({ ...yg, text: yg.description, bands: [] }))
      .sort((a, b) => Number(b.name) - Number(a.name));
    this.selectedYearGroupControl.setValue(
      this.curriculumPlanService.selectedYearGroup$.getValue() ?? this.yearGroups[0]?.id
    );
  }

  private fetchConflictData(): void {
    this.planningRelationships.getConflictsList(this.timetableId).subscribe(conflicts => {
      this.planningRelationships.conflictsData$.next(conflicts);
    });

    this.planningRelationships.conflictsData$.subscribe(conflictList => {
      this.conflictList = conflictList?.conflicts ?? [];
    });
  }

  private setupStateChangeSubscriptions(): void {
  // Handle state changes for year group, filters, etc.
  combineLatest([
    this.selectedYearGroupControl.valueChanges,
    this.showAll,
    this.selectedOrganisedByType$,
    this.schedulingService.stateChanged$
  ])
    .pipe(
      takeUntil(this.unsubscribe$),
        tap(([selectedYearGroup]) => {
          // Triggering Curriculum plan's staff changes
          this.curriculumPlanService.selectedYearGroup$.next(selectedYearGroup);
        }),
      switchMap(([selectedYearGroup, showAll, selectedOrganisedByType]) => {
        if (showAll || selectedOrganisedByType !== ORGANISED_BY_TYPES.YearGroup) {
          this.filteredYearGroups = this.yearGroups;
          return this.schedulingService.getListOfBlocks(this.timetableId).pipe(
            tap(([listOfBlocks]) => {
                this.updateBlockLayout$.next(listOfBlocks);
                return listOfBlocks;
              }),
            catchError(() => EMPTY)
          );
        } else {
          this.filteredYearGroups = this.yearGroups.filter(yg => yg.id === selectedYearGroup);
          return this.schedulingService.getListOfBlocksForYear(this.timetableId, selectedYearGroup)
            .pipe(tap(([listOfBlocks]) => {
                this.updateBlockLayout$.next(listOfBlocks);
                return listOfBlocks;
              }))
          }
      })
    )
    .subscribe();

    this.schedulingService.nccChanges$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(() => {
        this.fetchNonContactAssociations();
        this.nonContactCodesService.getAssociations(this.timetableId).subscribe();
        if(this.timetableAreaComponent)
        {
          this.timetableAreaComponent.updateConflictsAndContactTimes();
        }
      });

    combineLatest([this.selectedYearGroupControl.valueChanges, this.showAll, this.selectedOrganisedByType$])
      .pipe(
        takeUntil(this.unsubscribe$),
        switchMap(([selectedYearGroup, showAll, selectedOrganisedByType]) => {
          if (showAll || selectedOrganisedByType !== ORGANISED_BY_TYPES.YearGroup) {
            return this.bandService.getListOfBands(this.timetableId).pipe(
                catchError(()=> EMPTY)
              );
          } else {
            return this.bandService.getListOfBandsByYearGroup(this.timetableId, selectedYearGroup ?? 0);
          }
        })
      )
      .subscribe();
  }

  private fetchNonContactAssociations(): void {
    this.nonContactCodesService.getAssociations(this.timetableId).subscribe();
  }

  openNotes() {
    this.notesPanelComponent.show();
  }

  onTTAreaLoaded() {
    this.ttLoaded = true;
  }

  showAllYearGroupsChanged(showAll: boolean): void {
    if (this.ttLoaded) {
      this.showAll.next(showAll);
    }
  }

  sidePanelChange() {
    this.subjectsSidePanel.searchControl.setValue(null);
    this.staffSidePanel.searchControl.setValue(null);
    this.roomSidePanel.searchControl.setValue(null);
  }

  switchViewType(type: STAFFING_VIEW_TYPES): void {
    this.viewType = type;
  }

  onToggleRightBox(): void {
    this.isLeftBoxVisible = !this.isLeftBoxVisible;
  }

  editSubjects(show: boolean): void {
    if (show) {
      this.openedModal = 'Subject';
      setTimeout(() => {
        this.editSubjectsModal.nativeElement.show();
      })
    }
  }

  updateSessionAssociations(blocks: IListOfBlocksResponse[] | null) {
    const sessions: ISessionAssociations[] = blocks?.flatMap(b => {
      return b.sessions?.filter(session => session.periodId !== null)
        .map(({ id, roomId, periodId, mainStaffId, additionalStaffIds = [] }) => ({
          id,
          roomId,
          periodId,
          blockId: b.id,
          yearGroupId: b.subjectToYearGroups[0].yearGroupId,
          bandIds: b.bandIds ?? [],
          staffIds: [
            ...(mainStaffId ? [mainStaffId] : []),
            ...additionalStaffIds
          ]
        })) ?? [];
    }) ?? [];
    this.schedulingService.sessionAssociations$.next(sessions);
  }

  onBackToSubjects(): void {
    this.relationships
      .getSubjectsList(this.projectId)
      .pipe(
        switchMap(() => this.schedulingService.getDepartmentList(this.projectId)),
        switchMap(() => this.schedulingService.getSubjectList(this.projectId))
      )
      .subscribe(() => {
        this.fetchLatestData();
        this.openedModal = null;
        if(this.editSubjectsModal){
          this.editSubjectsModal.nativeElement.hide();
        }
      });
  }

  editStaffs(show: boolean): void {
    if (show) {
      this.openedModal = 'Staff';
      setTimeout(() => {
        this.editStaffsModal.nativeElement.show();
      })
    }
  }

  displayTimetable(data: ICurriculumPlanStaff | ICurriculumPlanRoom): void {
    if (data) {
      this.displayTimetableData = { ...data };
    }
  }

  onSwapStaff(data: ICurriculumPlanStaff) {
    this.isSwapStaffView = true;
    const conflicts = this.conflictList.filter(c => c.type === "Teacher" && !c.isAccepted).map(conflicts => conflicts.parameters);
    const hasConflicts = conflicts.some((c: any) => c.staffId === data.staffId);
    if (hasConflicts) {
      this.warningModalTitle = data.name + ' ' + this.translationService.instant('has unresolved conflicts.');
      this.warningModalPopup.show();
    } else {
      setTimeout(() => {
        this.swapDataModal.show();
      });
      this.curriculumPlanService.getAllAssignedContactTimeForStaff(this.timetableId).subscribe(staffs => {
        this.staffsData = this.staffList.filter(staff => staff.id !== data.staffId).map(s => {
          const assigned = staffs.find(staff => staff.staffId === s.id)!.assigned;
          return {
            ...s,
            hasConflict: conflicts.some((c: any) => c.staffId === s.id),
            assigned: assigned,
            remaining: s.totalContactTime > assigned ? s.totalContactTime - assigned : 0
          };
        }) as ISwapStaffDataEntity[];
      });
      const selectedData = this.staffList.find(staff => staff.id === data.staffId);
      this.selectedStaffData = selectedData as ISwapStaffDataEntity;
    }
  }

  onSwapRoom(data: ICurriculumPlanRoom) {
    this.isSwapStaffView = false;
    const conflicts = this.conflictList.filter(c => c.type === "Room" && !c.isAccepted).map(conflicts => conflicts.parameters);
    const hasConflicts = conflicts.some((c: any) => c.roomId === data.id);
    if (hasConflicts) {
      this.warningModalTitle = 'Room ' + data.code + ' ' + this.translationService.instant('has unresolved conflicts.');
      this.warningModalPopup.show();
    } else {
      setTimeout(() => {
        this.swapDataModal.show();
      });
      this.curriculumPlanService.getAllAssignedContactTimeForRoom(this.timetableId).subscribe(rooms => {
        this.roomsData = this.roomList.filter(room => room.id !== data.id).map(r => {
          const selectedRoom = rooms.find(room => room.id === r.id);
          const assigned = selectedRoom!.assignedContactTime;
          const available = selectedRoom!.contactTime;
          return {
            ...r,
            type: selectedRoom?.type,
            site: selectedRoom?.site,
            hasConflict: conflicts.some((c: any) => c.roomId === r.id),
            contactTime: available,
            assigned: assigned,
            remaining: available > assigned ? available - assigned : 0
          };
        }) as ISwapRoomDataEntity[];
      });
      const selectedData = this.roomList.find(room => room.id === data.id);
      this.selectedRoomData = selectedData as ISwapRoomDataEntity;
    }
  }

  onSubstituteStaff(data: ICurriculumPlanStaff) {
    setTimeout(() => {
      this.substituteStaffModal.show();
    });
    this.curriculumPlanService.getAllAssignedContactTimeForStaff(this.timetableId).subscribe(staffs => {
      this.staffsData = this.staffList.filter(staff => staff.id !== data.staffId).map(s => {
        const assigned = staffs.find(staff => staff.staffId === s.id)!.assigned;
        return {
          ...s,
          assigned: assigned,
          remaining: s.totalContactTime > assigned ? s.totalContactTime - assigned : 0
        };
      }) as ISwapStaffDataEntity[];
    });
    const selectedData = this.staffList.find(staff => staff.id === data.staffId);
    this.selectedStaffData = selectedData as ISwapStaffDataEntity;
  }

  onBackToStaffs(): void {
    this.loading.open();
    this.relationships
      .getStaffsList(this.projectId)
      .pipe(
        switchMap(() => this.relationships.getStaffToSubjectRelations(this.projectId, this.timetableId)),
        switchMap(() => this.curriculumPlanService.getAllAssignedContactTimeForStaff(this.timetableId)),
        switchMap(() => this.schedulingService.getStaffListWithContactInfo(this.projectId, this.timetableId)),
        finalize(() => this.loading.close())
      )
      .subscribe((staffListWithContactInfo) => {
        this.staffList = staffListWithContactInfo;
        this.fetchLatestData();
        this.openedModal = null;
        if(this.editStaffsModal){
          this.editStaffsModal.nativeElement.hide();
        }
      });
  }

  editRooms(show: boolean): void {
    if (show) {
      this.openedModal = 'Room';
      setTimeout(() => {
        this.editRoomsModal.nativeElement.show();
      })
    }
  }

  onBackToRooms(): void {
    this.loading.open();
    this.relationships
      .getRoomsList(this.projectId)
      .pipe(
        switchMap(() => this.schedulingService.getRoomsWithDepartment(this.projectId, this.timetableId)),
        finalize(() => this.loading.close()))
      .subscribe(() => {
        this.openedModal = null;
        this.fetchLatestData();
        if(this.editRoomsModal){
          this.editRoomsModal.nativeElement.hide();
        }
      });
  }

  updateBlockLayout(updatedBlockData: IListOfBlocksResponse) {
    this.updateBlockLayout$.next(updatedBlockData);
  }

  simpleDetailSwitch(type: SCHEDULING_VIEW_TYPES) {
    this.selectedSchedulingViewType = type;
  }

  organiseBy(type: ORGANISED_BY_TYPES) {
    if (type !== ORGANISED_BY_TYPES.YearGroup) {
      this.missingSession$.next(null);
    }
    this.selectedOrganisedByType$.next(type);
  }

  onMissingStaffRoomBlur(e: Event) {
    const event = e as CustomEvent;
    if (this.isMissingStaffRoomOpen && !event.detail) {
      this.isMissingStaffRoomOpen = false;
    }
  }

  onApplyFilter(data: { selectedItemIds: number[], clearFilter: boolean }) {
    this.missingFilter$.next(data.selectedItemIds.map(id => this.staffRoomFilterOptions.find(opt => opt.id === id)!.text as ('Room' | 'Staff')));
    this.trigger?.closeMenu();
  }

  onCancel(data: boolean) {
    this.trigger?.closeMenu();
  }

  onSwitchToSidebarView(event: 'Staff' | 'Room') {
    this.switchViewType(STAFFING_VIEW_TYPES[event]);
    this.isLeftBoxVisible = true;
  }

  doPrint(printMode: 'LARGE' | 'COMPACT' | 'CANVAS') {
    this.schedulingService.printState$.next(printMode);
  }

  showReport() {
    this.checkForMissingComponent.show();
  }

  onChangeToCurriculumTab(): void {
    this.changeToCurriculumTab.emit(true);
  }

  onChangeToSchedulingTab(): void {
    this.changeToSchedulingTab.emit(true);
  }
  
  onOpenTimetableSpecificationsModal(): void{
    this.openTimetableSpecifications()
  }

  private markPeriodsAndRemoveAMPMPeriods(periodStructure: IWithPeriodStructureResponse) {
    const filtered = {
      ...periodStructure,
      periodStructure: {
        ...periodStructure.periodStructure,
        weeks: periodStructure.periodStructure.weeks.map((week) => {
          week.days = week.days.map((day) => {
            day.periods.forEach((period, index, periods) => {
              period.isAM = index === 0 ? true : (period.periodCode === 'SESSION' ? !(day.periods[index - 1].isAM) : day.periods[index - 1].isAM);
            });
            day.periods = day.periods
              .filter((period) => !(period.periodCode === 'SESSION'));
            return day;
          });
          return week;
        })
      }
    };
    return filtered;
  }

  onCloseModal(which: string) {

    switch(this.openedModal) {
      case "Subject": {
        this.onBackToSubjects();
         break;
      }
      case "Staff": {
         this.onBackToStaffs();
         break;
      }
      case "Room": {
         this.onBackToRooms();
         break;
      }
   }

    this.openedModal = null;
  }

  fetchLatestData() {
    this.updateBlockLayout$.next(null);
    this.schedulingService.stateChanged$.next();
    this.curriculumPlanBlockService.stateChanged$.next();
    this.planningRelationships.getConflictsList(this.timetableId).subscribe((conflicts) => this.planningRelationships.conflictsData$.next(conflicts));
  }

  openTimetableSpecifications(): void {
    this.timetableSpecificationComponent.show(this.timetableId, this.projectId);
  }

  openTimetableSpecificationsTab(index: number): void {
    this.timetableSpecificationComponent.show(this.timetableId, this.projectId, index);
  }

  openAutoSchedule(): void {
    this.autoScheduleComponent.show();
  }

  openLunchPeriodsModal(): void {
    this.lunchSplitPeriods.show(this.timetableId);
  }
  
  openReports(): void {
    this.reportsComponent.show();
  }

  openNonContactCodes(): void {
    this.nonContactCodesComponent.show(this.timetableId, this.projectId, this.staffList);
  }

  refreshData(): void{
    this.schedulingService.classNameDefinition$.subscribe((flag) => {
        if(flag)
        {
          this.fetchLatestData();
          this.schedulingService.classNameDefinition$.next(false);
        }
    });
  }

  onChangeStaff(): void {
    this.fetchLatestData();
  }


  handleBlockColorUpdate(event: { colorCode: string | null, block: IListOfBlocksResponse }) {
    if (event.block.id != null) {
      this.curriculumPlanBlockService.updateBlockColor({
        blockId: event.block.id,
        colorCode: event.colorCode
      }).pipe(
        tap({
          next: () => {
            this.snackbar.success(this.translationService.instant("Successfully updated block colour"));
            this.schedulingService.stateChanged$.next();
          },
          error: (err) => {
            this.snackbar.error(this.translationService.instant(err.error.validationErrors[0].errorMessage));
          }
        })
      ).subscribe();
    }
  }
}
