<div class="band-selector-container">
  <div class="band-prev"
       [class.disable]="selectedBandIndex === 0"
       (click)="updateSelectedBandIndex(-1)">
    <bcm-icon class="icon" icon="far fa-arrow-to-top"></bcm-icon>
  </div>

  <div class="band-main-content" *ngIf="bandsByYearGroup[selectedBandIndex]">
    <div *ngIf="totalCount$ | async as totalCount; else noTotalCount" [ngClass]="((totalCount ?? 0) > ((totalPeriodCount$ | async) ?? 0)) ? 'div-period-total-count-red' : 'div-period-total-count'">
      <bcm-tooltip
        [message]="((totalCount ?? 0) > ((totalPeriodCount$ | async) ?? 0) ? 'Total band count exceeds the allowed maximum.' : 'Total number of periods') | translate"
        trigger="hover"
        color="slate">
        <span>{{ totalCount != null ? totalCount : 0 }}</span>
      </bcm-tooltip>
    </div>

    <ng-template #noTotalCount>
      <div class="div-period-total-count">
        <bcm-tooltip [message]="'Total number of periods' | translate" trigger="hover" color="slate">
          <span>0</span>
        </bcm-tooltip>
      </div>
    </ng-template>

    <div *ngIf="!editBandName"
         class="band-name">
      {{bandsByYearGroup[selectedBandIndex] ? bandsByYearGroup[selectedBandIndex].bandName : ''}}
    </div>

    <bromcom-input-field *ngIf="editBandName"
                         class="band-name-input"
                         [clearable]="false"
                         [maxLength]="2"
                         [formControl]="bandNameControl"></bromcom-input-field>

    <div class="band-actions">
      <bcm-icon *ngIf="!editBandName" class="icon" icon="far fa-pen" (click)="onEditBandName()"></bcm-icon>
      <bcm-icon *ngIf="editBandName" class="icon" icon="far fa-check" (click)="onSaveBandName()"
                [class.disable]="bandNameControl.errors"></bcm-icon>
      <bcm-icon *ngIf="editBandName" class="icon" icon="far fa-times" (click)="onCancelBandName()"></bcm-icon>

      <div *ngIf="!editBandName" class="disable" [class.disable]="bandsByYearGroup.length === 1">
        <bcm-icon class="icon" icon="far fa-trash-alt"
                  (click)="showDeleteBand()"></bcm-icon>
      </div>

      <div class="menu">
        <bcm-icon (click)="showBandBulkActions()" class="icon" icon="far fa-ellipsis-h"></bcm-icon>
        <span [matMenuTriggerFor]="menu"></span>
      </div>
    </div>
  </div>
  <div class="band-next"
       [class.disable]="selectedBandIndex === bandsByYearGroup.length - 1"
       (click)="updateSelectedBandIndex(1)">
    <bcm-icon class="icon" icon="far fa-arrow-to-bottom"></bcm-icon>
  </div>
</div>

<bromcom-delete-confirmation #bandConfirmationComponent
                             [headerText]="'Are you sure you want to delete this band with all the blocks in it?' | translate"
                             [mainText]="'If you proceed all blocks within this band will be deleted.' | translate"
                             (deleteClicked)="deleteBand($event)"></bromcom-delete-confirmation>

<mat-menu #menu="matMenu"
          class="block-action-modal"
          #bandBulkActionsRef>
  <div class="block-vertical">
    <button mat-menu-item
            [disabled]="disableRemoveByBand"
            [matMenuTriggerFor]="copyBandStructureRef">
      {{'Copy band structure' | translate }}
    </button>

    <button mat-menu-item
            [disabled]="disableRemoveByYear"
            (click)="onRemoveBlocksClicked(true)">
      {{'Remove all the blocks from year group' | translate }}
    </button>

    <button mat-menu-item
            [disabled]="disableRemoveByBand"
            (click)="onRemoveBlocksClicked(false)">
      {{'Remove all the blocks from band' | translate }}
    </button>

    <button mat-menu-item
            [disabled]="!reviewGeneratedYearGroups.includes(selectedYearGroup)"
            (click)="onRestoreReviewVersionClicked()">
      {{'Restore Block Planning version' | translate }}
    </button>
  </div>
</mat-menu>

<mat-menu #copyBandStructureRef>
  <div class="block-vertical">
    <button *ngFor="let band of bandsOptions, index as index"
            mat-menu-item
            [disabled]="selectedBandIndex === index"
            (click)="onCopyClicked(band.id)">
      {{band.bandName}}
    </button>
  </div>
</mat-menu>

<bromcom-delete-confirmation #deleteFromYearGroupConfirmationComponent
                             [headerText]="('Are you sure you want to delete all blocks from current year group?' | translate)"
                             [mainText]="('The blocks will be permanently deleted.' | translate)"
                             (deleteClicked)="onRemoveBlocks($event, true)"></bromcom-delete-confirmation>

<bromcom-delete-confirmation #deleteFromBandConfirmationComponent
                             [headerText]="('Are you sure you want to delete all blocks from current band?' | translate)"
                             [mainText]="('The blocks will be permanently deleted.' | translate)"
                             (deleteClicked)="onRemoveBlocks($event, false)"></bromcom-delete-confirmation>
