:host {
  display: block;
}

.timetable-grid {
  font-size: 12px;
  width: 100% !important;
  height: 100% !important;

  ::ng-deep {
    .ag-header {
      background-color: #f8f9fa;
      border-bottom: 2px solid #dee2e6;
    }

    .ag-header-cell {
      border-right: 1px solid #dee2e6;
      font-weight: 600;
      text-align: center;
    }

    .ag-row {
      border-bottom: 1px solid #dee2e6;
    }

    .ag-cell {
      border-right: 1px solid #dee2e6;
      padding: 4px 8px;
      display: flex;
      align-items: center;
    }

    // Department cells
    .department-cell {
      background-color: #e9ecef;
      font-weight: 600;
      writing-mode: vertical-lr;
      text-orientation: mixed;
      text-align: center;
      justify-content: center;
      border-right: 2px solid #adb5bd;
      min-height: 120px;

      .ag-cell-wrapper {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    // Subject cells
    .subject-cell {
      background-color: #f1f3f4;
      font-weight: 500;
      padding-left: 16px;
      border-right: 2px solid #adb5bd;
    }

    // Staff cells
    .staff-cell {
      padding-left: 32px;
      background-color: #ffffff;
    }

    // Period cells
    .period-cell {
      padding: 2px;
      text-align: center;
      justify-content: center;
    }

    // Timetable cell content
    .timetable-cell {
      width: 100%;
      height: 40px;
      border-radius: 4px;
      padding: 4px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 10px;
      font-weight: 500;
      color: white;
      text-align: center;

      .class-code {
        font-size: 9px;
        line-height: 1.1;
      }

      .room-info {
        font-size: 8px;
        opacity: 0.9;
        margin-top: 1px;
      }
    }

    // Planning cell
    .planning-cell {
      background-color: #6c757d !important;
      color: white;
      font-weight: 600;
    }

    // Year group colors
    .year-7 {
      background-color: #28a745; // Green
    }

    .year-8 {
      background-color: #17a2b8; // Teal
    }

    .year-9 {
      background-color: #fd7e14; // Orange
    }

    .year-10 {
      background-color: #dc3545; // Red
    }

    .year-11 {
      background-color: #6f42c1; // Purple
    }

    .default-color {
      background-color: #6c757d; // Gray
    }

    // Remove default ag-grid selection styles
    .ag-row-selected {
      background-color: inherit !important;
    }

    .ag-row-hover {
      background-color: rgba(0, 0, 0, 0.02) !important;
    }
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 16px;
  gap: 16px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;

  .page-info {
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    padding: 0 12px;
    white-space: nowrap;
  }
}

.fullscreen-modal {
  ::ng-deep {
    .modal-dialog {
      max-width: 100vw !important;
      width: 100vw !important;
      height: 100vh !important;
      margin: 0 !important;
    }

    .modal-content {
      height: 100vh !important;
      border-radius: 0 !important;
      border: none !important;
    }

    .modal-body {
      padding: 0 !important;
      height: calc(100vh - 120px) !important;
      overflow: hidden !important;
    }
  }
}
