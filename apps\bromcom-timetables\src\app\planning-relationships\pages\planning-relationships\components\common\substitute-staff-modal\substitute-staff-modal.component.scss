@import "apps/bromcom-timetables/src/assets/styles/variables";

@mixin displayFlexRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

$body-height: 70vh;
$header-height: 49px;
$inner-padding: 24px * 2;
$action-bar-margin-bottom: calc(8px);

.substitute-staff-modal {
  z-index: 10;
  display: flex !important;

  .modal-body {
    padding: 24px;
    height: fit-content;
    padding-bottom: 5px !important;

    .action-bar {
      @include displayFlexRow;
      flex-wrap: wrap;
      margin-bottom: $action-bar-margin-bottom;

      .left-side {
        @include displayFlexRow;
        align-items: center;

        .search-field {
          min-width: 256px;
        }
      }
    }

    .table-container {
      height: 60vh;
    }

    .substitute-confirmation-holder {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 24px;
      align-self: stretch;
      padding: 8px 0px 19px 0px;
    }

    .info-holder {
      display: flex;
      align-items: center;
      justify-content: center;
      color: $color-blue-500;
      font-size: 52px;
      font-weight: 500;
    }

    ::ng-deep .disabled {
      opacity: 0.7;
    }
  }

  .substitute-confirmation-butons {
    justify-content: space-between;
    display: flex;
    width: 100%;

    .confirmation-action-buttons {
      gap: 8px;
      display: flex;
    }
  }
}

.confirmation-view {
  justify-content: center;
  align-items: center;
  
  .substitute-confirmation-holder {
    align-items: center !important;
    justify-content: center !important;
  }
  
  .confirmation-label-holder {
    display: flex;
    text-align: center;
    align-items: center;
    justify-content: center;
    
    .confirmation-label {
      color: $color-blue-grey-700;
      font-size: 20px;
      font-weight: 600;
      line-height: 28px;
    }
  }
}

::ng-deep .confirmation-view .bcm-modal__container-header {
  display: none !important;
}

::ng-deep substitute-staff-modal:not(.confirmation-view) .bcm-modal__container-header {
  display: flex !important;
}

::ng-deep .confirmation-view .bcm-modal__container {
  width: 430px !important;
}

.mr-15 {
  margin-right: 15px;
}
