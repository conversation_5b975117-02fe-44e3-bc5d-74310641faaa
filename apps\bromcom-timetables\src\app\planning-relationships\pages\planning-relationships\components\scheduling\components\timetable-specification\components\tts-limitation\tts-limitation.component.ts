import { Component, ElementRef, OnDestroy, OnInit, Input, ViewChild, HostListener } from '@angular/core';
import {
  GeneralModalComponent
} from '../../../../../../../../../_shared/components/general-modal/general-modal.component';
import { TimetableSpecificationsService } from '../../../../../../../../services/timetable-specifications.service';
import { ITTSLimitation } from '../../../../../../../../../_shared/models/ITTSLimitation';
import { GridApi } from 'ag-grid-community/dist/lib/gridApi';
import { GridOptions, GridReadyEvent, ICellRendererParams } from 'ag-grid-community';
import { gridOptions } from './tts-limitation-gridoptions';
import { Subject, switchMap, takeUntil } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@bromcom/ui';
import { FormControl } from '@angular/forms';
import { ISubject } from '../../../../../../../../../_shared/models/ISubject';
import { INCLUDED_TYPES } from '../../../../../../../../../_shared/enums/IncludedTypes';
import { RelationshipsService } from '../../../../../../../../services/relationships.service';

@Component({
  selector: 'bromcom-tts-limitation',
  templateUrl: './tts-limitation.component.html',
  styleUrls: ['./tts-limitation.component.scss'],
})
export class TtsLimitationComponent implements OnInit, OnDestroy {
  @ViewChild('ttsSubjectRelationshipsModal', { static: false }) ttsSubjectRelationshipsModal!: ElementRef;
  @ViewChild('deleteSubjectOnDaysWarningModal') deleteSubjectOnDaysWarningModal!: GeneralModalComponent;
  @ViewChild('excludeSubjectRelationshipWarningModal') excludeSubjectRelationshipWarningModal!: GeneralModalComponent;

  @Input() projectId!: number;
  @Input() timetableId!: number;

  searchControl = new FormControl(null);
  params!: GridReadyEvent;
  INCLUDED_TYPES = INCLUDED_TYPES;
  viewType: INCLUDED_TYPES = INCLUDED_TYPES.ACTIVE;
  gridApi!: GridApi;
  originalGridData: ITTSLimitation[] = [];
  gridData: ITTSLimitation[] = [];
  gridOptions!: GridOptions;
  excludeRowIds: number[] = [];
  isRemoveBulkDisabled = true;
  subjectsOptions: Partial<ISubject>[] = [];
  dayList: any[] = [];
  deleteRowId!: number;
  filteredStaffOptions: any[] = [];
  tableHeight: string = '45vh';

  readonly unsubscribe$: Subject<void> = new Subject();

  constructor(
    private timetableSpecificationsService: TimetableSpecificationsService,
    private snackbar: SnackbarService,
    protected relationshipsService: RelationshipsService,
    protected translate: TranslateService,
    private el: ElementRef
  ) {
  }

  ngOnInit(): void {
    this.relationshipsService.subjectsData$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(subjects => this.subjectsOptions = subjects.map(subject => ({
        id: subject.id,
        name: subject.name,
        text: subject.name,
        code: subject.code,
        departmentId: subject.departmentId,
        departmentName: subject.departmentName,
        color: '#' + subject.color
      })));

    this.timetableSpecificationsService.getLimitationsList(this.timetableId)
      .subscribe(data => {
        this.originalGridData = JSON.parse(JSON.stringify(data))
        this.gridData = data;
        this.setRowData();
      })
    

    setTimeout(() => {
      this.setRowData();
    }, 50)

    this.searchControl.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(searchValue => {
        this.gridApi.setQuickFilter(searchValue ?? '');
      })

    this.gridOptions = gridOptions.call(this, {
      onGridReady: this.onGridReady.bind(this),
      onAddNewRow: this.onAddNewRow.bind(this),
      onAcceptNewRow: this.onAcceptNewRow.bind(this),
      onCancelAddingNewRow: this.onCancelAddingNewRow.bind(this),
      onEditRow: this.onEditRow.bind(this),
      onDeleteRow: this.onDeleteRowClicked.bind(this),
      onAcceptRow: this.onAcceptRow.bind(this),
      onCancelEditRow: this.onCancelEditRow.bind(this),
      onExcludeRow: this.onExcludeRow.bind(this),
      onIncludeRow: this.onIncludeRow.bind(this)
    });
  }

  ngAfterViewInit() {
    this.updateTableHeight();
  }

  setRowData(): void { 
    this.viewType === INCLUDED_TYPES.ACTIVE
      ? setTimeout(() => {
        this.gridApi?.setRowData(this.gridData.filter(data => !data.isExcluded));
        this.gridApi?.redrawRows();
      }, 0)
      : setTimeout(() => {
        this.gridApi?.setRowData(this.gridData.filter(data => data.isExcluded));
        this.gridApi?.redrawRows();
      }, 0)
  }

  onExcludeRow(params: ICellRendererParams): void {
    this.excludeRowIds = [params.data.id];
    this.excludeSubjectRelationshipWarningModal.show();
  }

  onIncludeRow(params: ICellRendererParams): void {
    this.excludeIncludeRequest({ isExcluded: false, ids: [params.data.id] })
  }

  excludeIncludeBulk(isExcluded: boolean): void {
    this.gridApi.stopEditing();
    this.gridOptions.api?.setPinnedBottomRowData([{}]);
    const ids = this.gridApi.getSelectedRows().map(row => row.id);
    if (!isExcluded) {
      this.excludeIncludeRequest({ isExcluded, ids })
    } else {
      this.excludeRowIds = ids;
      this.excludeSubjectRelationshipWarningModal.show();
    }
  }

  excludeIncludeRequest(data: { isExcluded: boolean, ids: number[] }) {
    this.excludeSubjectRelationshipWarningModal?.modalComponentRef?.nativeElement.hide();
    this.timetableSpecificationsService.excludeLimitation(data)
      .pipe(switchMap(() => this.timetableSpecificationsService.getLimitationsList(this.timetableId)))
      .subscribe(data => {
        this.originalGridData = JSON.parse(JSON.stringify(data));
        this.gridData = data;
        this.setRowData();
        this.snackbar.success(this.translate.instant('Successful operation'));
      })
  }

  onGridReady(params: GridReadyEvent): void {
    this.params = params;
    this.gridApi = params.api;
    this.gridApi.setDomLayout('normal');
  }

  onAddNewRow(params: ICellRendererParams): void {
    this.gridOptions.api?.setPinnedBottomRowData([
      {
        id: 'newRowId',
        subjectsIds: [],
        sessionsPerPeriod: null,
        isExcluded: false
      }
    ]);
    const rowColumns = this.params.columnApi.getColumns() ?? [];
    this.gridApi.startEditingCell({ rowIndex: 0, rowPinned: 'bottom', colKey: rowColumns[1]?.getColId() });
  }

  onAcceptNewRow(params: ICellRendererParams) {
    if (!this.validateSessionsPerPerid(params)) {
      this.errorAcceptNewRow(params);
      return;
    }

    this.timetableSpecificationsService.addLimitation(this.timetableId, {
      ...params.data,
      id: 0,
      sessionsPerPeriod: Number(params.data.sessionsPerPeriod)
    }).pipe(switchMap(() => this.timetableSpecificationsService.getLimitationsList(this.timetableId)))
      .subscribe({
        next: data => {
          this.originalGridData = JSON.parse(JSON.stringify(data));
          this.gridData = data;
          this.setRowData();
          this.gridOptions.api?.setPinnedBottomRowData([{}]);
          this.snackbar.saved();
        },
        error: ({ error }) => {
          if (error?.validationErrors && error?.validationErrors[0]) {
            this.snackbar.error(error.validationErrors[0].errorMessage);
          }
          this.errorAcceptNewRow(params);
        }
      })
  }

  errorAcceptNewRow(params: ICellRendererParams){
    this.gridOptions.api?.setPinnedBottomRowData([
      {
        id: 'newRowId',
        subjectsIds: [],
        sessionsPerPeriod: null,
        isExcluded: false
      }
    ]);
    const rowColumns = this.params.columnApi.getColumns() ?? [];
    this.gridApi.startEditingCell({ rowIndex: 0, rowPinned: 'bottom', colKey: rowColumns[1]?.getColId() });
  }
  

  onCancelAddingNewRow(): void {
    this.gridOptions.api?.setPinnedBottomRowData([{}]);
  }

  onEditRow(params: ICellRendererParams): void {
    const rowIndex = params.node.rowIndex;
    const rowColumns = this.params.columnApi.getColumns() ?? []
    if (rowIndex || rowIndex === 0) {
      this.gridApi.startEditingCell({ rowIndex, colKey: rowColumns[0].getColId() })
    }
  }

  onAcceptRow(params: ICellRendererParams): void {
    if (!this.validateSessionsPerPerid(params)) {
      this.errorAcceptRow(params);
      return;
    }


    this.timetableSpecificationsService.editLimitation(params.data.id, params.data)
    .pipe(switchMap(() => this.timetableSpecificationsService.getLimitationsList(this.timetableId)))
    .subscribe(
      {
        next: data => {
            this.originalGridData = JSON.parse(JSON.stringify(data));
            this.gridData = data;
          this.setRowData();
          this.snackbar.saved();
        },
        error: ({ error }) => {
          //this.gridOptions.api?.undoCellEditing();
          if (error?.validationErrors && error?.validationErrors[0]) {
            this.snackbar.error(error.validationErrors[0].errorMessage);
          }
          this.errorAcceptRow(params);
        }
      })
  }

  validateSessionsPerPerid(params: ICellRendererParams){
    if(!Number.isInteger(Number(params.data.sessionsPerPeriod)) || !(Number(params.data.sessionsPerPeriod) > 0) || !(Number(params.data.sessionsPerPeriod) < 1000)){
      this.snackbar.error("Sessions per period value must be an integer between 1 and 999");
      return false;
    }

    return true;
  }

  errorAcceptRow(params: ICellRendererParams){
    const rowIndex = params.node.rowIndex;
    const rowColumns = this.params.columnApi.getColumns() ?? [];
    if (rowIndex || rowIndex === 0) {
      this.gridApi.startEditingCell({ rowIndex, colKey: rowColumns[0]?.getColId() });
    }
  }

  validateSubjects(subjectIds: number[]): boolean {
    return subjectIds && subjectIds.length > 0; 
  }

  validateSessionPerPeriod(sessionsPerPeriod: number): boolean {
    return sessionsPerPeriod > 0 && sessionsPerPeriod < 1000; 
  }

  onCancelEditRow(params: ICellRendererParams): void {
    this.timetableSpecificationsService.getLimitationsList(this.timetableId)
      .subscribe(data => {
        this.originalGridData = JSON.parse(JSON.stringify(data));
        this.gridData = data;
        this.setRowData();
      })
  }

  onDeleteRowClicked(params: ICellRendererParams): void {
    this.deleteRowId = params.data.id;
    this.deleteSubjectOnDaysWarningModal.show();
  }

  onDeleteRow(): void {
    this.deleteSubjectOnDaysWarningModal.hide();
    this.timetableSpecificationsService.deleteLimitation(this.deleteRowId)
      .pipe(switchMap(() => this.timetableSpecificationsService.getLimitationsList(this.timetableId)))
      .subscribe(data => {
        this.originalGridData = JSON.parse(JSON.stringify(data));
        this.gridData = data;
        this.setRowData();
        this.snackbar.success(this.translate.instant('Successful operation'));
      })
  }
  
  viewTypeChange(event: Event): void {
    this.gridApi.stopEditing();
    this.setRowData();
    this.viewType = (event as CustomEvent).detail.innerText.toLowerCase() === INCLUDED_TYPES.ACTIVE
      ? INCLUDED_TYPES.ACTIVE
      : INCLUDED_TYPES.EXCLUDED;
    this.gridData = JSON.parse(JSON.stringify(this.originalGridData));
    this.setRowData();
    if (this.viewType == INCLUDED_TYPES.EXCLUDED) {
      this.gridOptions.api?.setPinnedBottomRowData([]);
    } else {
      this.gridOptions.api?.setPinnedBottomRowData([{}]);
    }
    this.setRowData();
  }

  groupSubjectsBySection(subjects: any[]): any {
    return subjects.reduce((acc, subject) => {
        const section = subject.section || 'Other';
        if (!acc[section]) {
            acc[section] = [];
        }
        acc[section].push(subject);
        return acc;
    }, {} as { [key: string]: any[] });
  }

  @HostListener('window:resize')
  updateTableHeight() {
    const infoMsg = this.el.nativeElement.querySelector('.info-msg');
    if (infoMsg) {
      const headerHeight = infoMsg.getBoundingClientRect().height;
      const headerMarginBottom = parseFloat(getComputedStyle(infoMsg).marginBottom);
      const totalHeight = headerHeight + headerMarginBottom;
      this.tableHeight = `calc(45vh - ${totalHeight}px)`;
    }
  }

  ngOnDestroy(): void {
  }
}
