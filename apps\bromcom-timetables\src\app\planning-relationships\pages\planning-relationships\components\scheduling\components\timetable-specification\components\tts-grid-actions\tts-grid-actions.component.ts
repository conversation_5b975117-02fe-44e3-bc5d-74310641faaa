import { ChangeDetectorRef, Component } from '@angular/core';
import {
  CellValueChangedEvent,
  GridApi,
  ICellRendererParams,
  RowEditingStartedEvent,
  RowEditingStoppedEvent,
  RowValueChangedEvent
} from 'ag-grid-community';
import { ITTSSubjectRelationship } from '../../../../../../../../../_shared/models/ITTSSubjectRelationship';
import { TtsBlocksOnDaysComponent } from '../tts-blocks-on-days/tts-blocks-on-days.component';

interface ITtsGridActions extends ICellRendererParams {
  gridId: string,
  type: string,
  parent: TtsBlocksOnDaysComponent,
  blockToYearData?: { blockId: number, yearGroupId: number }[],
  allowMultipleRuleId?: number,
  onAcceptNewRow: (params: ICellRendererParams) => {},
  onCancelAddingNewRow: () => {},
  onEditRow: (params: ICellRendererParams) => {},
  onAcceptRow: (params: ICellRendererParams) => {},
  onCancelEditRow: (params: ICellRendererParams) => {},
  onDeleteRow: (params: ICellRendererParams) => {},
  onExcludeRow: (params: ICellRendererParams) => {},
  onIncludeRow: (params: ICellRendererParams) => {},
}

@Component({
  selector: 'bromcom-tts-grid-actions',
  templateUrl: './tts-grid-actions.component.html',
  styleUrls: ['./tts-grid-actions.component.scss']
})
export class TtsGridActionsComponent {
  private agGridElement?: HTMLElement;

  data!: ITTSSubjectRelationship;
  gridApi!: GridApi;
  params!: ITtsGridActions;
  isExcluded = false;
  isNewRow = false;
  disable = false;
  isEdit = false;
  parent!: TtsBlocksOnDaysComponent;

  agInit(params: ITtsGridActions): void {
    this.agGridElement = document.getElementById(params.gridId)!;

    this.params = params;
    this.gridApi = params.api;
    this.data = params.data;
    this.isExcluded = params.data.isExcluded;
    this.isNewRow = params.data.id === 'newRowId';
    this.parent = params.parent;
    this.disableButtons(params as any);

    this.gridApi.addEventListener('cellValueChanged', this.disableButtons.bind(this));
    this.gridApi.addEventListener('rowEditingStarted', this.onRowEditingStarted.bind(this));
    this.gridApi.addEventListener('rowEditingStopped', this.onRowEditingStopped.bind(this));
  }

  private disableButtons(event: RowValueChangedEvent) {
    if (this.params.type === 'subjectRelationship') {
      this.disable = !event.data.yearGroupIds?.length || !event.data.firstSubjectId || !event.data.typeId || !event.data.secondSubjectId || event.data.firstSubjectId === event.data.secondSubjectId
    } else if (this.params.type === 'limitation') {
      this.disable = !event.data.subjectsIds?.length || !event.data.sessionsPerPeriod;
    } else if (this.params.type === 'parttimestaff') {
      this.disable = !event.data.staffId || !event.data.daysPerWeek;
    } else if (this.params.type === 'subjectOnDays') {
      const lengthOfArrays = (event.data.notAvailablePeriodIds?.length ?? 0) + (event.data.undesirablePeriodIds?.length ?? 0) + (event.data.preferredPeriodIds?.length ?? 0);
      const uniqueList = [...new Set((event.data.notAvailablePeriodIds || []).concat(event.data.undesirablePeriodIds || [], event.data.preferredPeriodIds || []))]
      this.disable = !event.data.yearGroupIds?.length || !event.data.subjectId || (!event.data.notAvailablePeriodIds?.length && !event.data.undesirablePeriodIds?.length && !event.data.preferredPeriodIds?.length) || lengthOfArrays !== uniqueList.length
    } else if (this.params.type === 'blockOnDays') {
      let isBlockOrYGChange = false;
      if (event.type === "cellValueChanged") {
        const cellChange = event as CellValueChangedEvent<{ [key: string]: string }>;
        isBlockOrYGChange = (cellChange.colDef.field === 'yearGroupId' || cellChange.colDef.field === 'blockIds');
      }
      const isNewRow = event.data.id === "newRowId";
      const currentData = this.parent.gridDataClone.find(d => d.id === event.data.id);
      if (!isNewRow) {
        const periodLimitValidation = currentData?.blockOnDaysType && this.params.allowMultipleRuleId === currentData.blockOnDaysType ? currentData.periodLimit != null : true;
        const blockYearValidation = currentData?.id === this.params?.data?.id ? this.params.blockToYearData?.filter(data => currentData?.blockIds?.includes(data.blockId)).every(filteredData => filteredData.yearGroupId === currentData?.yearGroupId) : true;
        this.disable = (isBlockOrYGChange && !blockYearValidation) || !periodLimitValidation || !currentData?.blockOnDaysType || !currentData?.blockIds?.length || !currentData?.yearGroupId;
      } else {
        const periodLimitValidation = event.data.blockOnDaysType && this.params.allowMultipleRuleId === event.data.blockOnDaysType ? event.data.periodLimit != null : true;
        const blockYearValidation = this.params.blockToYearData?.filter(data => event.data.blockIds?.includes(data.blockId)).every(filteredData => filteredData.yearGroupId === event.data.yearGroupId);
        this.disable = !blockYearValidation || !periodLimitValidation || !event.data.blockOnDaysType || !event.data.blockIds?.length || !event.data.yearGroupId;
      }
    } else if (this.params.type === 'staffOnDays') {
      const rowsToDisplay = this.gridApi?.getRenderedNodes()?.map(node => node?.data);
      let hasDuplicates = (event.data.staffId) ? rowsToDisplay.filter(n => n.staffId == event.data.staffId).length : 0;
      if (event.data.id == "newRowId") {
        hasDuplicates++;
      }

      const intersections = event.data.undesirablePeriodIds.filter((e: any) => event.data.preferredPeriodIds.indexOf(e) !== -1);
      this.disable = !event.data.staffId || (!event.data.undesirablePeriodIds?.length && !event.data.preferredPeriodIds?.length) || intersections.length > 0 || hasDuplicates > 1;
    } else if (this.params.type === 'staffRelationship') {
      const rowsToDisplay = this.gridApi?.getRenderedNodes()?.map(node => node?.data);
      const hasDuplicates = !!rowsToDisplay.find(row => row.staffId === event.data.staffId)

      const intersections = event.data.undesirablePeriodIds.filter((e: any) => event.data.preferredPeriodIds.indexOf(e) !== -1);
      this.disable = !event.data.staffId || (!event.data.undesirablePeriodIds?.length && !event.data.preferredPeriodIds?.length) || intersections.length > 0 || hasDuplicates;
    }
  }

  private onRowEditingStarted(event: RowEditingStartedEvent): void {
    this.agGridElement?.classList.add('no-pointer-events');

    if (event.data.id === this.params.data.id) {
      this.isEdit = true;
      this.disableButtons(event);
    }
  }

  private onRowEditingStopped(event: RowEditingStoppedEvent): void {
    this.agGridElement?.classList.remove('no-pointer-events');

    if (event.data.id === this.params.data.id) {
      this.params.data = event.data;
      this.isEdit = false;
    }
    this.gridApi.redrawRows();
  }

  onAcceptNewRow(): void {
    this.gridApi.stopEditing();
    setTimeout(() => this.params.onAcceptNewRow(this.params), 150)
  }

  onCancelAddingNewRow(): void {
    this.agGridElement?.classList.remove('no-pointer-events');
    this.params.onCancelAddingNewRow();
  }

  onEditRow() {
    this.params.onEditRow(this.params);
  }

  onAcceptRow(): void {
    this.gridApi.stopEditing();
    setTimeout(() => this.params.onAcceptRow(this.params), 150)
  }

  onCancelEditRow(): void {
    this.gridApi.stopEditing();
    this.params.onCancelEditRow(this.params);
  }

  onDeleteRow() {
    this.agGridElement?.classList.remove('no-pointer-events');
    this.params.onDeleteRow(this.params);
  }

  onExcludeRow() {
    this.params.onExcludeRow(this.params)
  }

  onIncludeRow() {
    this.params.onIncludeRow(this.params)
  }
}
