import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { customValueSetter, staffingGridOptions } from './staffing.helpers';
import {
  ColDef,
  GridOptions,
  GridReadyEvent,
  ICellRendererParams,
  IRowNode,
  ISetFilter
} from 'ag-grid-community';
import { groupFilterByValue } from '@bromcom/core';
import { AgGridNumericComponent, LoadingSpinnerService, SnackbarService } from '@bromcom/ui';
import { GridApi } from 'ag-grid-community/dist/lib/gridApi';
import { StaffingService } from '../../../../services/staffing.service';
import { IStaffingListElement } from '../../../../../_shared/models/IStaffingList';
import { IStaffingStaffRow } from '../../../../../_shared/models/IStaffingStaffRow';
import { IStaffingYearGroup } from '../../../../../_shared/models/IStaffingYearGroup';
import { IStaffingSubject } from '../../../../../_shared/models/IStaffingSubject';
import { IStaffingActions } from './components/ag-grid-staffing-actions/ag-grid-staffing-actions.component';
import { IStaff } from '../../../../../_shared/models/IStaff';
import { ISubject } from '../../../../../_shared/models/ISubject';
import { IYearGroup } from '../../../../../_shared/models/IYearGroup';
import { PlanningRelationshipsService } from '../../../../services/planning-relationships.service';
import { FormControl } from '@angular/forms';
import { debounceTime, distinctUntilChanged, finalize, forkJoin, Subject, switchMap, takeUntil } from 'rxjs';
import { RelationshipsService } from '../../../../services/relationships.service';
import {
  IStaffingUpdateYearGroupContactTimeData
} from '../../../../../_shared/models/IStaffingUpdateYearGroupContactTimeData';
import { IStaffingBottomPinnedRows } from '../../../../../_shared/models/IStaffingBottomPinnedRows';
import { IAggFuncParams } from 'ag-grid-community/dist/lib/entities/colDef';

@Component({
  selector: 'bromcom-staffing',
  templateUrl: './staffing.component.html',
  styleUrls: ['./staffing.component.scss']
})
export class StaffingComponent implements OnInit, OnDestroy {
  @Input() projectId!: number;
  @Input() timetableId!: number;
  expandSwitchControl = new FormControl<boolean>(false);
  staffingSearchControl = new FormControl(null);
  staffingGridOptions!: GridOptions;
  gridApi!: GridApi;
  params!: GridReadyEvent;
  isColumnsInitFinished = false;

  staffingDataOriginal: IStaffingStaffRow[] = [];
  dataLength = 0;
  staffs: IStaff[] = []
  yearGroups: IYearGroup[] = []
  yearGroupColumnsName: string[] = []
  subjects: ISubject[] = []
  filterModelValues: string[] = []
  filterIds = {
    subjectIds: [],
    departmentIds: []
  };
  isFilterApplied = false;
  calculatedTotalSums!: IStaffingBottomPinnedRows;
  subjectInfoTotal!: IStaffingBottomPinnedRows;

  readonly unsubscribe$: Subject<void> = new Subject();

  constructor(
    protected translate: TranslateService,
    protected staffing: StaffingService,
    private planningRelationships: PlanningRelationshipsService,
    private relationships: RelationshipsService,
    private loading: LoadingSpinnerService,
    private snackbar: SnackbarService
  ) {
  }

  ngOnInit() {
    this.relationships.staffListData$
      .pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$))
      .subscribe((value => this.staffs = value as Partial<IStaff>[] as IStaff[]))
    this.planningRelationships.yearGroupsData$
      .pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$))
      .subscribe((value => this.yearGroups = value.filter(yearGroup => !yearGroup.isExcluded)
      .sort((a, b) => Number(a.name) - Number(b.name))))
    this.relationships.subjectsData$
      .pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$))
      .subscribe((value => this.subjects = value as ISubject[]))

    this.staffingSearchControl.valueChanges
      .pipe(
        takeUntil(this.unsubscribe$),
        debounceTime(500))
      .subscribe((searchValue: string | null) => {
        this.setRowData(searchValue ?? '');
        this.gridApi.ensureIndexVisible(0, 'top');
      });

    this.expandSwitchControl.valueChanges.pipe(
      takeUntil(this.unsubscribe$))
      .subscribe(switchValue => {
        if (typeof switchValue === 'boolean') {
          this.gridApi?.forEachNodeAfterFilter(node => {
            node.setExpanded(switchValue);
          });
        }
      });

    this.staffingGridOptions = staffingGridOptions.call(this, {
      onEditRow: (params: ICellRendererParams) => this.onEditRowClicked(params),
      onCancelEditRow: () => this.onCancelEditRowClicked(),
      onAcceptRow: (params: ICellRendererParams) => this.onAcceptRow(params)
    } as IStaffingActions);

    this.loading.open();
    forkJoin([
      this.planningRelationships.getYearGroupList(this.projectId),
      this.staffing.getStaffingList(this.projectId, this.timetableId),
      this.staffing.getSubjectInfoTotal(this.timetableId)
    ]).pipe(
      finalize(() => this.loading.close()))
      .subscribe(([, staffingListResponse, subjectInfoTotal]) => {
        this.staffingDataOriginal = this.formatData(staffingListResponse.filter(element => element.subjects.length));
        this.subjectInfoTotal = this.calculateSubjectInfoTotal(subjectInfoTotal);
        setTimeout(() => {
          this.addColumns()
          this.isColumnsInitFinished = true
        }, 500)

        setTimeout(() => {
          this.setRowData('')
        }, 1000)
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  onGridReady(params: GridReadyEvent): void {
    this.params = params
    this.gridApi = params.api;
  }

  formatData(staffingDataRaw: IStaffingListElement[]): IStaffingStaffRow[] {
    const newData: IStaffingStaffRow[] = []
    staffingDataRaw.forEach((staff: IStaffingListElement, index: number) => {
      const currentStaff = this.staffs.find(element => element.id === staff.staffId)
      if (!currentStaff) return;

      const newRow: IStaffingStaffRow = {
        id: `${index}`,
        staffId: currentStaff.id,
        firstName: currentStaff.firstName,
        lastName: currentStaff.lastName,
        staffCode: currentStaff.code,
        totalContact: currentStaff.totalContactTime,
        totalMain: 0,
        orgHierarchy: [`${index}`]
      }

      this.addYearGroupsData(staff, newRow, 'totalContactTime');
      newData.push(newRow);

      staff.subjects.forEach((subject: IStaffingSubject, subIndex: number) => {
        const currentSubject = this.subjects.find(element => element.id === subject.subjectId)
        if (!currentSubject) return
        const newSubject: IStaffingStaffRow = {
          ...newRow,
          id: `${index} ${subIndex}`,
          department: currentSubject.departmentName ?? '',
          mainSubject: currentSubject.code,
          mainSubjectTooltip: currentSubject.name,
          totalContact: subject.contactTime ?? 0,
          totalMain: 0,
          orgHierarchy: [`${index}`, `${subIndex}`]
        }

        this.addYearGroupsData(subject, newSubject, 'contactTime');
        newData.push(newSubject)
      })
    })
    return newData;
  }

  onAcceptRow(params: ICellRendererParams): void {
    const updateYearGroupContactTimeData = this.generateList(params);
    this.loading.open();
    params.data.orgHierarchy.length === 1
      ? this.staffing.updateStaffTotalContactTime(params.data.staffId, params.data.totalContact)
        .pipe(
          switchMap(() => this.relationships.getStaffsList(this.projectId)),
          finalize(() => this.loading.close()))
        .subscribe({
          next: () => {
            this.planningRelationships.isCanDeactivate$.next(true);
            this.snackbar.saved()
          }
        })
      : this.staffing.updateYearGroupContactTimeByStaff(this.timetableId, params.data.staffId, updateYearGroupContactTimeData)
        .pipe(
          finalize(() => this.loading.close()))
        .subscribe({
          next: () => {
            this.planningRelationships.isCanDeactivate$.next(true);
            this.snackbar.saved()
          }
        })
    this.refreshCalculations(params);
  }

  generateList(params: ICellRendererParams): IStaffingUpdateYearGroupContactTimeData[] {
    const yearGroupsList = this.yearGroupColumnsName?.map(yearGroupName => {
      return {
        subjectId: this.subjects.find(subject => subject.name === params.data.mainSubjectTooltip)?.id,
        yearGroupId: this.yearGroups.find(yearGroup => yearGroup.description.toLowerCase().replace(' ', '') === yearGroupName)?.id,
        contactTime: params.data[yearGroupName]
      }
    }) as IStaffingUpdateYearGroupContactTimeData[];

    return [...yearGroupsList, {
      subjectId: this.subjects.find(subject => subject.name === params.data.mainSubjectTooltip)?.id,
      yearGroupId: null,
      contactTime: params.data.totalContact
    }] as IStaffingUpdateYearGroupContactTimeData[]
  }

  refreshCalculations(params?: ICellRendererParams): void {
    const parentNode = this.gridApi?.getRowNode(params?.data.orgHierarchy[0])
    const childNode = params?.data.orgHierarchy.length === 2 ? this.gridApi?.getRowNode(params?.data.orgHierarchy.join(' ')) : null

    if (parentNode) {
      this.calculateRowValues(parentNode);
    }

    if (childNode) {
      this.calculateRowValues(childNode);
    }

    this.calculateBottomPinnedRow();
  }

  removeFilterValue(filterValue: string) {
    const filterModel = this.gridApi.getFilterModel();
    for (const field in filterModel) {
      if (Object.prototype.hasOwnProperty.call(filterModel, field)) {
        const filter = filterModel[field];
        if (filter && filter.values && filter.values.includes(filterValue)) {
          const newValues = filter.values.filter((value: string) => value !== filterValue);
          this.gridApi.getFilterInstance(field)?.setModel({ ...filter, values: newValues });
          this.gridApi.onFilterChanged();
          if (!newValues.length) {
            const newFilterModel = { ...filterModel };
            newFilterModel[field] = {};
            filter.values.forEach((value: string) => {
              newFilterModel[field][value] = true;
            });
            this.gridApi.setFilterModel(newFilterModel);
          }
          break;
        }
      }
    }
  }

  updateStaffing(): void {
    this.staffing.updateStaffingList(this.timetableId).subscribe(() => {
      this.snackbar.saved();
      forkJoin([
        this.staffing.getStaffingList(this.projectId, this.timetableId),
        this.staffing.getSubjectInfoTotal(this.timetableId)
      ]).pipe(
        finalize(() => this.loading.close()))
        .subscribe(([staffingListResponse, subjectInfoTotal]) => {
          this.staffingDataOriginal = this.formatData(staffingListResponse.filter(element => element.subjects.length));
          this.subjectInfoTotal = this.calculateSubjectInfoTotal(subjectInfoTotal);
          this.setRowData('');
          this.gridApi.redrawRows();
        })
    })
  }

  private calculateRowValues(node: IRowNode): void {
      const rowTotalMainValue = this.yearGroupColumnsName.reduce((acc, curr) => acc + parseInt(node.data[curr]), 0)
      this.gridApi.getRowNode(node.data.id)?.setDataValue('totalMain', rowTotalMainValue)

      if (node.data?.orgHierarchy.length === 1) {
        this.yearGroupColumnsName.forEach(yearGroup => {
          const parentRowYearGroupsSum = node?.childrenAfterFilter?.reduce((accChild, currChild) => accChild + parseInt(currChild.data[yearGroup]), 0)
          node?.setDataValue(yearGroup, parentRowYearGroupsSum)
        })

        const rowTotalMainValue = this.yearGroupColumnsName.reduce((acc, curr) => acc + parseInt(node.data[curr]), 0)
        node?.setDataValue('totalMain', rowTotalMainValue)
      }
  }

  private setRowData(controlValue: string): void {
    this.gridApi?.setRowData(
      groupFilterByValue(controlValue,
        this.staffingDataOriginal,
        ['id', 'totalMain', 'actions', ...this.yearGroupColumnsName]));
    this.refreshCalculations();

    this.excludeBlankFilter('department')
    this.excludeBlankFilter('mainSubject')
  }

  private excludeBlankFilter(columnId: string): void {
    const allRowData: IStaffingStaffRow[] = [];
    this.gridApi.forEachNode(node => allRowData.push(node.data));
    const values = [...new Set(allRowData.map(data => data[columnId] as string ?? null).filter(x => x))];
    const instance = this.staffingGridOptions.api!.getFilterInstance<ISetFilter>(columnId)!;

    instance.setFilterValues(values);
    instance.applyModel();
    this.staffingGridOptions.api!.onFilterChanged();
  }

  private onEditRowClicked(params: ICellRendererParams): void {
    const rowIndex = params.node.rowIndex ?? 0
    const colKey = 'totalContact'
    this.planningRelationships.isCanDeactivate$.next(false);
    this.gridApi.startEditingCell({ rowIndex, colKey });
  }

  private onCancelEditRowClicked(): void {
    this.planningRelationships.isCanDeactivate$.next(true);
  }

  protected calculateBottomPinnedRow(): void {
    const sumsBase: IStaffingBottomPinnedRows = {
      totalContact: 0,
      totalMain: 0,
      mainSubject: ''
    }

    this.yearGroupColumnsName.forEach(yearGroup => {
      sumsBase[yearGroup] = 0;
    });

    this.calculatedTotalSums = this.calculateTotalSums({ ...sumsBase })
    const calculatedSubjectTotalSums = this.calculateSubjectTotalSums({ ...sumsBase })

    if (this.filterModelValues.length) {
      this.staffing.getSubjectInfoTotalByFilter(this.timetableId, this.filterIds)
        .subscribe(response => {
          this.subjectInfoTotal = this.calculateSubjectInfoTotal(response);
          this.gridApi.setPinnedBottomRowData([this.calculatedTotalSums, calculatedSubjectTotalSums, this.subjectInfoTotal])
        })
    } else {
      this.staffing.getSubjectInfoTotal(this.timetableId)
        .subscribe(subjectInfoTotal => {
          this.subjectInfoTotal = this.calculateSubjectInfoTotal(subjectInfoTotal);
          this.gridApi.setPinnedBottomRowData([this.calculatedTotalSums, this.subjectInfoTotal]);
        })
    }
  }

  private calculateTotalSums(totalSums: IStaffingBottomPinnedRows): IStaffingBottomPinnedRows {
    if (this.staffingSearchControl.value) {
      return this.calculatedTotalSums
    }

    this.dataLength = 0;

    this.gridApi.forEachNode(rowNode => {
      if (rowNode.data?.orgHierarchy.length === 1) {
        this.yearGroupColumnsName.forEach(yearGroup => {
          totalSums[yearGroup] += rowNode.data[yearGroup]
        });
        totalSums.totalContact += rowNode.data.totalContact;
        totalSums.totalMain += rowNode.data.totalMain;
        totalSums.mainSubject = this.translate.instant('Total');
        this.dataLength += 1;
      }
    });
    return totalSums
  }

  private calculateSubjectTotalSums(subjectTotalSums: IStaffingBottomPinnedRows): IStaffingBottomPinnedRows {
    if (this.filterModelValues.length) {
      this.dataLength = 0;
    }

    this.gridApi.forEachNodeAfterFilter(rowNode => {
      if (rowNode.data?.orgHierarchy.length > 1 && this.filterModelValues.length) {
        this.yearGroupColumnsName.forEach(yearGroup => {
          subjectTotalSums[yearGroup] += rowNode.data[yearGroup]
        });
        subjectTotalSums.totalContact += rowNode.data.totalContact;
        subjectTotalSums.totalMain += rowNode.data.totalMain;
        subjectTotalSums.mainSubject = this.translate.instant('Subject Total');
        this.dataLength += 1;
      }
    });
    return subjectTotalSums
  }

  private addColumns(): void {
    const newColumns: ColDef[] = [];
    const existingColumnIndex = this.staffingGridOptions.columnDefs?.findIndex((column: ColDef) => column.field === 'totalContact') ?? 0;
    this.yearGroups.forEach(yearGroup => {
      if (!yearGroup.name) return

      const fieldName = yearGroup.description.toLowerCase().replace(' ', '');
      this.yearGroupColumnsName.push(fieldName)
      newColumns.push({
        field: fieldName,
        headerName: yearGroup.name,
        flex: 1,
        minWidth: 90,
        sortable: false,
        menuTabs: [],
        cellClass: 'center-cell',
        headerClass: 'center-cell',
        resizable: true,
        editable: (params) => params.data.orgHierarchy.length > 1,
        cellEditor: AgGridNumericComponent,
        valueSetter: customValueSetter,
        cellStyle: () => {
          return { borderRight: '1px solid var(--ag-row-border-color)' };
        },
        aggFunc: (params: IAggFuncParams) => {
          let sum = 0;
          params.values.forEach((value: number) => (sum += value));
          return sum;
        }
      });
    });
    this.staffingGridOptions.columnDefs?.splice(existingColumnIndex + 1, 0, ...newColumns);
  }

  private addYearGroupsData(element: IStaffingListElement | IStaffingSubject, newSubject: IStaffingStaffRow, contactTimeType: keyof IStaffingYearGroup): void {
    element.yearGroups.forEach((yearGroup: IStaffingYearGroup) => {
      const matchingYearGroup = this.yearGroups.find(yg => yg.id === yearGroup.yearGroupId)?.description?.toLowerCase().replace(' ', '');
      if (matchingYearGroup) {
        newSubject[matchingYearGroup] = yearGroup[contactTimeType];
        newSubject['totalMain'] += yearGroup[contactTimeType] ?? 0
      }
    })
  }

  private calculateSubjectInfoTotal(subjectInfoTotal: { yearGroupId: number, contactTime: number }[]): IStaffingBottomPinnedRows {
    const result: any = { totalContact: 0, mainSubject: this.translate.instant('Subject Info. Total') };
    subjectInfoTotal.forEach(item => {
      const matchingItemDescription = this.yearGroups.find((element) => element.id === item.yearGroupId)?.description.toLowerCase().replace(' ', '');
      if (matchingItemDescription) {
        result[matchingItemDescription] = item.contactTime;
      }
      if (item.contactTime) {
        result.totalContact += item.contactTime
      }
    });

    return result;
  }
}
