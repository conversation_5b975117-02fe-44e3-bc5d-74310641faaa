@import "apps/bromcom-timetables/src/assets/styles/variables";

.lunch-split-periods-modal {
  .bcm-alert {
    display: flex;
    align-items: flex-start;
  }
  .body {
    .info-msg {
      padding: 8px 12px;
      display: flex;
      gap: 8px;
      border-radius: 8px;
      background-color: $color-grey-default-50;
      width: 100%;
      margin-bottom: 16px;
    }

    .lbl-info {
      display: flex;
      color: $color-grey-default-text;
      font-weight: 500;
      font-size: 16px;
      padding-left: 10px;

      i {
        padding-top: 4px;
      }
    }

    .spn-info {
      padding-left: 10px;
    }
  }

  .period-containers {
    display: flex;
    gap: 16px;
    margin-top: 16px;
    overflow-x: auto;
    overflow-y: hidden;

    .period {
      flex: 1 1 200px;

      border-radius: 8px;
      max-width: calc(100% / 3);
      min-width: 200px;
      border: 1px solid $color-grey-300;
      box-sizing: border-box;
      max-height: 55vh;

      .header {
        background-color: $color-grey-default-50;
        color: $color-grey-default-text;
        padding: 12px 16px;
        font-weight: bold;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        border-bottom: 2px solid $color-grey-300;
      }

      .body {
        height: calc(100% - 48px);
        overflow-y: auto;

        .no-data {
          display: flex;
          flex-direction: column;
          border-bottom-left-radius: 8px;
          border-bottom-right-radius: 8px;

          .no-data-header {
            font-size: 14px;
            font-weight: bold;
          }
        }
      }

      .card {
        background: white;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: grab;
        margin: 12px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .card:active {
        cursor: grabbing;
      }
    }
  }

  .footer {
    text-align: right;
  }
}
