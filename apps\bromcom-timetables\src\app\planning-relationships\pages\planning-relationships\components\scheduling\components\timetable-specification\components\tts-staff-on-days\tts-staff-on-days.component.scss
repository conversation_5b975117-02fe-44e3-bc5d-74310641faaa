@import 'apps/bromcom-timetables/src/assets/styles/variables';
@import 'apps/bromcom-timetables/src/app/planning-relationships/pages/planning-relationships/components/scheduling/components/timetable-specification/styles/tts-ag-grid';

.tts-staff-on-days-container {
  padding: 24px 0 0;

  .info-msg {
    width: 100%;
    color: $color-blue-100;
    align-content: center;
    height: 40px;
    border-radius: 8px;
    margin-bottom: 26px;
  }

  .lbl-info {
    color: $color-blue-900;
    font-weight: 500;
    line-height: 40px;
    size: 16px;
    padding-left: 10px;
  }

  .spn-info {
    padding-left: 10px;
  }


  .action-bar {
    @include action-bar;
  }

  .table-container {
    @include table-container;
  }

  ::ng-deep .ag-popup-editor {
    background-color: transparent;
    z-index: 10000;
    min-width: 142px;
    // -48 for grid checkbox width
    // 4.8 for gridoption columns flex sum
    // -34 for inner padding
    width: calc(((100% - 48px) / 4.8 * 1.5) - 34px);
    margin: -44px 16px 0;
  }

  ::ng-deep .mat-expansion-panel.new-row {
    margin-bottom: -32px;
  }
}
