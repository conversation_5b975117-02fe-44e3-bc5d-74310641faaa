<div class="expanded-linear-block-container"
     (mouseenter)="onMouseEnter()"
     (mouseleave)="onMouseLeave()">

  <bromcom-expanded-header [selectedBlock]="selectedBlock"
                           [selectedYearGroupId]="selectedYearGroupId"
                           [timetableId]="timetableId"
                           (openEditClassCodesModalEvent)="openEditClassCodesModal($event)"
                           (openLinkBlocksModalComponentEvent)="openLinkBlocksModal($event)">
  </bromcom-expanded-header>

  <div class="subject-selector">
    <table *ngIf="selectedBlock?.subjectToYearGroups">
      <thead>
      <tr>
        <th class="action border left-action"
            (click)="leftClick($event)"
            [class.disable]="isLeftStepperDisabled()">
          <bcm-icon class="icon"
                    icon="far fa-arrow-to-left">
          </bcm-icon>
        </th>

        <ng-template #moreSubjectsTemplate>
          <th class="border body-th"
              [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}"
              [ngStyle]="{'background-color' : subject?.subjectId | getSubjectColor: subjects}"
              [class.dashed]="isHovered && !subject.subjectId&& isSubjectDraggingActive"
              [class.active]="selectedSubjectId === subject.id || subjectIndex === index"
              cdkDropList
              [id]="'expandedEmptyLinearSubjectPlaceholder' + selectedBlock.id"
              (cdkDropListDropped)="dropSubject($event)"
              *ngFor="let subject of (selectedBlock.subjectToYearGroups | slice:firstIndex:lastIndex), index as index"
              (click)="subjectClicked(subject.id!)">

            <div class="block" *ngIf="subject.subjectId">
              {{subject.subjectId | getSubjectCode:subjects }}
              <div class="period-count">
                <div class="simple">S:{{subject.singlePeriodCount}}</div>
                <div class="double">D:{{subject.doublePeriodCount}}</div>
              </div>
            </div>

            <div class="block" *ngIf="!subject.subjectId">
              <div *ngIf="isHovered && isSubjectDraggingActive; else emptyTemplate">
                {{'Drop here' | translate}}
              </div>
              <ng-template #emptyTemplate>--</ng-template>
            </div>
          </th>
        </ng-template>

        <ng-container *ngIf="selectedBlock.subjectToYearGroups[0].subjectId; else moreSubjectsTemplate">
          <th class="border body-th"
              [ngClass]="{'white-color': isColorDarkHelper(subject.subjectId | getSubjectColor: subjects )}"
              [ngStyle]="{'background-color' : subject?.subjectId | getSubjectColor: subjects}"
              [class.active]="selectedSubjectId === subject.id"
              *ngFor="let subject of (selectedBlock.subjectToYearGroups | slice:firstIndex:lastIndex);"
              (click)="subjectClicked(subject.id!)">

            <div class="block" *ngIf="subject.subjectId">
              {{subject.subjectId | getSubjectCode:subjects }}
              <div class="period-count">
                <div class="simple">S:{{subject.singlePeriodCount}}</div>
                <div class="double">D:{{subject.doublePeriodCount}}</div>
              </div>
            </div>

            <div class="block" *ngIf="!subject.subjectId">
              <div *ngIf="isHovered && isSubjectDraggingActive; else emptyTemplate">
                {{'Drop here' | translate}}
              </div>
              <ng-template #emptyTemplate>--</ng-template>
            </div>
          </th>
        </ng-container>


        <th class="border body-th"
            *ngIf="selectedBlock.subjectToYearGroups[0]?.subjectId"
            style="width: 110px"
            [class.hidden]="!(isHovered && isSubjectDraggingActive)"
            [class.dashed]="isHovered && isSubjectDraggingActive"
            cdkDropList
            [id]="'expandedEmptyLinearSubjectPlaceholder' + selectedBlock.id"
            (cdkDropListDropped)="dropSubject($event)">
          <div class="block">
            <div>
              {{'Drop here' | translate}}
            </div>
          </div>
        </th>

        <th class="action border"
            (click)="rightClick()"
            [class.disable]="selectedBlock.subjectToYearGroups && lastIndex >= selectedBlock.subjectToYearGroups.length">
          <bcm-icon class="icon"
                    icon="far fa-arrow-to-right">
          </bcm-icon>
        </th>
      </tr>
      </thead>
    </table>
  </div>

  <div class="actions" [formGroup]="form">
    <div>
      <label>
        {{'Total Period Count' | translate}}*
      </label>
      <input class="input"
             [value]="selectedBlock.subjectToYearGroups | getTotalPeriodCount"
             disabled/>
    </div>

    <div>
      <label for="periodCount">
        {{'Subject Period Count' | translate}}*
      </label>
      <input id="periodCount"
             class="input readonly"
             formControlName="periodCount"
             readonly/>
    </div>

    <div class="info-wrapper">
      <bcm-tooltip class="info-icon"
                   message="{{'It\'s automatically populated for all subjects.' | translate}}"
                   trigger="hover">
        <img src="assets/images/info-icon-input.png" alt="">
      </bcm-tooltip>

      <label>
        {{'Number of Classes' | translate}}*
      </label>
      <input class="input padding-left"
             formControlName="classCount"
             appAcceptNumbers/>
    </div>

    <div>
      <label>
        {{'Single Periods' | translate}}*
      </label>
      <input class="input"
             formControlName="singlePeriodCount"
             appAcceptNumbers/>
    </div>

    <div>
      <label>
        {{'Double Periods' | translate}}*
      </label>
      <input class="input"
             formControlName="doublePeriodCount"
             appAcceptNumbers/>
    </div>

    <div class="apply">
      <bcm-button (click)="updateAnExistingSubjectToYearGroup()"
                  [disabled]="form.invalid || !selectedBlock.subjectToYearGroups[0].subjectId">
        {{'Apply' | translate }}
      </bcm-button>
    </div>
  </div>

  <div class="error" *ngIf="(form.controls.classCount?.errors?.['required'] ||
     form.controls.periodCount?.errors?.['required'] || form.controls.singlePeriodCount?.errors?.['required'] ||
      form.controls.doublePeriodCount?.errors?.['required'])">
    <span>
        {{'All fields are required' | translate}}
    </span>
  </div>

  <div class="error" *ngIf="!(form.controls.classCount?.errors?.['required'] ||
     form.controls.periodCount?.errors?.['required'] || form.controls.singlePeriodCount?.errors?.['required'] ||
      form.controls.doublePeriodCount?.errors?.['required']) && form.controls.periodCount.errors?.['max']">
      <span>
        {{'Maximum subject period count is 99.' | translate}}
      </span>
  </div>

  <div class="error" *ngIf="!(form.controls.classCount?.errors?.['required'] ||
     form.controls.periodCount?.errors?.['required'] || form.controls.singlePeriodCount?.errors?.['required'] ||
      form.controls.doublePeriodCount?.errors?.['required']) &&form.controls.periodCount.errors?.['min']">
      <span>
        {{'Minimum subject period count is 1.' | translate}}
      </span>
  </div>

  <div class="panel">
    <bromcom-detailed-block [staffs]="staffs"
                            [projectId]="projectId"
                            [missingStaffRoomSessionId]="missingStaffRoomSessionId"
                            [staffToSessionActiveId]="staffToSessionActiveId"
                            [roomToSessionActiveId]="roomToSessionActiveId"
                            [rooms]="rooms"
                            [periodMenuRef]="periodMenuRef"
                            [sessionMenuRef]="sessionMenuRef"
                            [classMenuRef]="classMenuRef"
                            [resetSelectedPeriodIds$]="resetSelectedPeriodIds$"
                            [resetSelectedClassIds$]="resetSelectedClassIds$"
                            [setScrollToPeriodIndex$]="setScrollToPeriodIndex$"
                            (sessionMenuOpenedEvent)="sessionMenuOpened($event)"
                            (updateAnExistingSubjectToYearGroup)="updateAnExistingSubjectToYearGroup()"
                            [subjectMenuRef]="subjectMenuRef"
                            (subjectMenuOpenedEvent)="subjectMenuOpened($event)"
                            (periodMenuOpenedEvent)="periodMenuOpened($event)"
                            (classMenuOpenedEvent)="classMenuOpened($event)"
    ></bromcom-detailed-block>
  </div>

</div>
