import { customComparator, NOOP, transformToAGGridConfig } from '@bromcom/core';
import { StaffingComponent } from './staffing.component';
import { AgGridNoDataComponent, AgGridNumericComponent } from '@bromcom/ui';
import {
  AgGridStaffingActionsComponent,
  IStaffingActions
} from './components/ag-grid-staffing-actions/ag-grid-staffing-actions.component';
import { IAggFuncParams, ValueSetterParams } from 'ag-grid-community/dist/lib/entities/colDef';
import { ITooltipParams } from 'ag-grid-community/dist/lib/rendering/tooltipComponent';
import {
  GetRowIdParams,
  GridApi,
  ICellRendererParams,
  RowClassParams,
  RowHeightParams,
  RowStyle,
  SetFilterValuesFuncParams,
  ViewportChangedEvent
} from 'ag-grid-community';
import { IStaffingStaffRow } from '../../../../../_shared/models/IStaffingStaffRow';

export function staffingGridOptions(this: StaffingComponent, config: IStaffingActions) {
  const {
    onEditRow = NOOP,
    onCancelEditRow = NOOP,
    onAcceptRow = NOOP
  } = config

  return transformToAGGridConfig({
    suppressRowClickSelection: true,
    suppressCellFocus: true,
    groupDisplayType: 'groupRows',
    treeData: true,
    editType: 'fullRow',
    noRowsOverlayComponent: AgGridNoDataComponent,
    noRowsOverlayComponentParams: {
      noRowsMessageFunc: () => this.staffingSearchControl.value
        ? this.translate.instant('Sorry no results found. Please amend your search criteria.')
        : this.translate.instant('Staff have not been added to subjects.'),
      style: 'display: flex; justify-content: center; align-items: center; height: 100%; width: 100%; padding: 48px 0 100px 0'
    },
    getDataPath: (data: IStaffingStaffRow) => data.orgHierarchy,
    getRowHeight: (params: RowHeightParams) => {
      if (params.node.rowPinned) {
        return 50;
      }
      return 34;
    },
    getRowClass: (params) => params.data?.orgHierarchy?.length > 1 ? 'gray' : undefined,
    getRowStyle: (params: RowClassParams): RowStyle | undefined => {
      if (params.node.rowPinned) {
        return { 'font-weight': 'bold', 'background': 'var(--ag-header-background-color)' };
      }
      return undefined;
    },
    getRowId: (params: GetRowIdParams) => params.data.id,
    onFilterChanged: () => {
      const filterModel = this.gridApi.getFilterModel();
      const filteredColumns = Object.keys(filterModel)
        .map(colId => ['department', 'mainSubject'].includes(colId) ? this.gridApi.getColumnDef(colId) : null)
        .filter(column => column !== null && column !== undefined);
      this.expandSwitchControl.setValue(!!filteredColumns.length || this.expandSwitchControl.getRawValue());

      this.filterIds = { subjectIds: [], departmentIds: [] }
      this.filterModelValues = filteredColumns.flatMap(column => {
        const filter = this.gridApi.getFilterInstance(column as unknown as string);
        const filterModel = filter?.getModel();
        const filtersValue = filterModel?.values.filter((value: string | null) => value !== null)
        if (column?.field === 'mainSubject') {
          this.filterIds.subjectIds = filtersValue.map((value: string) => this.subjects.find(element => value === element.code)?.id);
        } else if (column?.field === 'department') {
          this.filterIds.departmentIds = filtersValue.map((value: string) => this.subjects.find(element => value === element.departmentName)?.departmentId)
        }
        return filtersValue;
      });

      this.calculateBottomPinnedRow();

      this.isFilterApplied = !!(this.filterIds.subjectIds.length || this.filterIds.departmentIds.length);
      this.gridApi.forEachNodeAfterFilter(node => {
        node.setExpanded(node.level === 0 && !!filteredColumns.length);
      });
    },
    onViewportChanged: (params: ViewportChangedEvent) => {
      const isSmallWidth = document.body.clientWidth > 1440;
      params.columnApi.setColumnVisible('firstName', isSmallWidth);
      params.columnApi.setColumnVisible('lastName', isSmallWidth);
      params.columnApi.setColumnVisible('department', isSmallWidth);
    },
    columnDefs: [
      {
        field: 'id',
        headerName: '',
        pinned: 'left',
        width: 30,
        cellRenderer: 'agGroupCellRenderer',
        cellRendererParams: {
          suppressCount: true
        }
      },
      {
        field: 'firstName',
        headerName: this.translate.instant('First Name'),
        wrapHeaderText: true,
        pinned: 'left',
        flex: 1,
        width: 135,
        comparator: customComparator,
        tooltipValueGetter: (params: ITooltipParams) => {
          return params.value && params.value.length > 14 ? params.value : null;
        },
        resizable: true,
        cellStyle: () => {
          return { borderRight: '1px solid var(--ag-row-border-color)' };
        }
      },
      {
        field: 'lastName',
        headerName: this.translate.instant('Last Name'),
        wrapHeaderText: true,
        pinned: 'left',
        flex: 1,
        width: 135,
        sort: 'asc',
        comparator: customComparator,
        tooltipValueGetter: (params: ITooltipParams) => {
          return params.value && params.value.length > 14 ? params.value : null;
        },
        resizable: true,
        cellStyle: () => {
          return { borderRight: '1px solid var(--ag-row-border-color)' };
        }
      },
      {
        field: 'staffCode',
        headerName: this.translate.instant('Staff Code'),
        wrapHeaderText: true,
        pinned: 'left',
        flex: 1,
        width: 135,
        comparator: customComparator,
        resizable: true,
        cellStyle: () => {
          return { borderRight: '1px solid var(--ag-row-border-color)' };
        }
      },
      {
        field: 'department',
        headerName: this.translate.instant('Department'),
        flex: 1.2,
        pinned: 'left',
        width: 155,
        comparator: customComparator,
        tooltipValueGetter: (params: ITooltipParams) => {
          return params.value && params.value.length > 14 ? params.value : null;
        },
        resizable: true,
        cellStyle: () => {
          return { borderRight: '1px solid var(--ag-row-border-color)' };
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          values: (params: SetFilterValuesFuncParams) => excludeBlankFilter(params, this.gridApi, 'department'),
          buttons: ['reset', 'apply'],
          closeOnApply: true
        }
      },
      {
        field: 'mainSubject',
        headerName: this.translate.instant('Subject'),
        wrapHeaderText: true,
        pinned: 'left',
        flex: 1.2,
        width: 145,
        comparator: customComparator,
        resizable: true,
        cellClass: (params) => params.node.rowPinned ? 'right-cell' : undefined,
        cellStyle: () => {
          return { borderRight: '1px solid var(--ag-row-border-color)' };
        },
        tooltipValueGetter: (params: ITooltipParams) => params.data.mainSubjectTooltip ?? null,
        filter: 'agSetColumnFilter',
        filterParams: {
          values: (params: SetFilterValuesFuncParams) => excludeBlankFilter(params, this.gridApi, 'mainSubject'),
          buttons: ['reset', 'apply'],
          closeOnApply: true
        }
      },
      {
        field: 'totalContact',
        headerName: this.translate.instant('Contact Time'),
        wrapHeaderText: true,
        pinned: 'left',
        flex: 1.2,
        width: 110,
        sortable: false,
        resizable: true,
        cellClass: (params) => {
          let classList = ''
          if (params.data.orgHierarchy?.[0]) {
            const parentNode = this.gridApi?.getRowNode(params.data.orgHierarchy[0]);
            const parentRowTotalContactSum = parentNode?.childrenAfterGroup?.reduce((accChild, currChild) => accChild + parseInt(currChild.data.totalContact), 0)
            if (params.data.orgHierarchy.length === 1 && parentRowTotalContactSum && parentNode?.data.totalContact < parentRowTotalContactSum) {
              classList += ' red'
            } else if (params.data.orgHierarchy.length === 1 && parentRowTotalContactSum === parentNode?.data.totalMain) {
              classList += ' green'
            } else if (params.data.orgHierarchy.length === 2 && params.data.totalContact === params.data.totalMain) {
              classList += ' green'
            }
          }

          if (params.node.rowPinned) {
            return 'center-cell-pinned' + classList
          }
          return 'center-cell' + classList
        },
        headerClass: 'center-cell',
        editable: true,
        cellEditor: AgGridNumericComponent,
        valueSetter: customValueSetter,
        cellStyle: () => {
          return { borderRight: '1px solid var(--ag-row-border-color)' };
        },
        onCellValueChanged: () => this.gridApi.redrawRows()
      },
      {
        field: 'totalMain',
        headerName: this.translate.instant('Total Main'),
        wrapHeaderText: true,
        pinned: 'right',
        flex: 1.2,
        width: 80,
        sortable: false,
        menuTabs: [],
        headerClass: 'center-cell',
        editable: false,
        cellEditor: AgGridNumericComponent,
        aggFunc: (params: IAggFuncParams) => {
          let sum = 0;
          params.values.forEach((value: number) => (sum += value));
          return sum;
        },
        valueSetter: customValueSetter,
        cellClass: (params) => {
          let classList = 'center-cell'
          if (params.data.orgHierarchy?.[0]) {
            if (params.data.totalContact < params.data.totalMain) {
              classList += ' red'
            } else if (params.data.totalContact === params.data.totalMain) {
              classList += ' green'
            }
          }
          return classList
        },
        cellStyle: () => {
          return { borderRight: '1px solid var(--ag-row-border-color)' };
        },
        onCellValueChanged: () => this.gridApi.redrawRows()
      },
      {
        field: 'actions',
        headerName: this.translate.instant('Actions'),
        pinned: 'right',
        flex: 1,
        width: 90,
        sortable: false,
        menuTabs: [],
        cellClass: 'actions',
        headerClass: 'center-cell',
        cellRendererSelector: (params: ICellRendererParams) => !params.node.rowPinned ? { component: AgGridStaffingActionsComponent } : undefined,
        cellRendererParams: {
          onEditRow,
          onCancelEditRow,
          onAcceptRow
        }
      }
    ]
  })
}

const excludeBlankFilter = (params: SetFilterValuesFuncParams, gridApi: GridApi, columnId: string) => {
  const allRowData: IStaffingStaffRow[] = [];
  gridApi.forEachNode(node => allRowData.push(node.data));
  const unique: (string | null)[] = [...new Set(allRowData.map(data => data[columnId] as string ?? null).filter(x => x))];
  params.success(unique);
}

export const customValueSetter = (params: ValueSetterParams) => {
  if (isNaN(params.newValue) && params.colDef.field) {
    params.data[params.colDef.field] = params.oldValue;
  } else if (params.colDef.field) {
    params.data[params.colDef.field] = params.newValue;
  }
  return true;
}
