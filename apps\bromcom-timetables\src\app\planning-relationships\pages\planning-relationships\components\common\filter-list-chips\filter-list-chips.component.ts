import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { FormArray, FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'bromcom-filter-list-chips',
  templateUrl: './filter-list-chips.component.html',
  styleUrls: ['./filter-list-chips.component.scss']
})
export class FilterListChipsComponent {
  @Input() label = '';
  searchControl = new FormControl('');
  filterGroup = new FormGroup({
    items: new FormArray<FormControl<boolean | null>>([])
  });
  _dataItems: any[] = [];
  filteredDataItems: any[] = [];
  @Input() selectedItemIds: number[] = [];
  clearFilterOptions = false;
  @ViewChild('filterPopup', { static: false }) filterPopup!: ElementRef;
  @Input() isOpen = false;
  @Input() resetFilter = false;
  @Input() selectAllOnClearedApply = true;
  @Input() showHeader = true;
  @Input() showSearchBar = true;
  @Input() appearance = 'checkbox';
  @Input() enableApplyFilterWithDefaultSelection = false;
  @Input() filterListTemplate!: string;
  @Input() placeholder = "";
  @Input() dataItems!: {
    items: any[];
    startState: boolean;
    removeFreeSessions?: boolean;
    freeSessionId?: number | null;
  };

  @Input() set selection(data: number[] | null) {
    this.selectedItemIds = data || [];
    this.chipList = this.dataItems.items?.filter((item) => data?.includes(item.id));
  }

  @Output() filterData = new EventEmitter();
  @Output() closeFilter = new EventEmitter<boolean>();

  chipList: any[] = [];
  @Input() chipTemplate = '{{text}}';

  toggleOpen() {
    this.isOpen = !this.isOpen;
  }

  onCloseFilter(event: any) {
    this.isOpen = false;
    this.closeFilter.emit(true);
  }

  onDataFiltered(event: any) {
    this.isOpen = false;
    this.filterData.emit(event);
    this.chipList = this.dataItems.items.filter((item) => event.selectedItemIds.includes(item.id));
  }

  getTemplate(item: any) {
    let templateItem = this.chipTemplate;
    Object.keys(item).forEach((key) => {
      templateItem = templateItem.replace('{{' + key + '}}', item[key]);
    });
    return templateItem;
  }
}
