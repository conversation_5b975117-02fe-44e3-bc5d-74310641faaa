import { Component } from '@angular/core';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { ICurriculumSessions } from '../../../../../../../_shared/models/ICurriculumSessions';
import { SnackbarService } from '@bromcom/ui';
import { TranslateService } from '@ngx-translate/core';
import { BaseBlockSimpleView } from '../../_shared/base-block-simple-view';
import { CurriculumPlanService } from "../../../../../../services/curriculum-plan.service";
import { BandService } from '../../../../../../services/band.service';
import { PlanningRelationshipsService } from '../../../../../../services/planning-relationships.service';
import { RelationshipsService } from '../../../../../../services/relationships.service';
import { InformationService } from '../../../../../../services/information.service';

@Component({
  selector: 'bromcom-block-option',
  templateUrl: './block-option.component.html',
  styleUrls: ['./block-option.component.scss']
})
export class BlockOptionComponent extends BaseBlockSimpleView {
  transformedSessions: ICurriculumSessions[][] = []

  constructor(
    protected override curriculumPlan: CurriculumPlanService,
    protected override curriculumPlanBlocks: CurriculumPlanBlocksService,
    protected override snackbar: SnackbarService,
    protected override translate: TranslateService,
    protected override band: BandService,
    protected override planningRelationships: PlanningRelationshipsService,
    public override relationshipsService: RelationshipsService,
    protected override informationService: InformationService
  ) {
    super(curriculumPlan, curriculumPlanBlocks, snackbar, translate, band, planningRelationships, relationshipsService, informationService)
  }

  formatSessions(): void {
    const groupedSessionsBySubject = this.block.subjectToYearGroups
      .map(subject => this.block.sessions.filter(session => session.subjectToYearGroupId === subject.id))

    const groupedSessions = groupedSessionsBySubject.map(groupedSessions => groupedSessions
      .reduce((groups: { [key: string]: ICurriculumSessions[] }, session: ICurriculumSessions) => {
        const className = session.className;
        if (groups[className]) {
          groups[className].push(session);
        } else {
          groups[className] = [session];
        }
        return groups;
      }, {}));

    this.transformedSessions = groupedSessions.map(sessions => Object.values(sessions).map(this.transformSessions));

    const droppableRoomIds = this.transformedSessions.map(sessionGroup => sessionGroup.map(session => 'room' + session.id.toString())).flat();
    const droppableStaffIds = this.transformedSessions.map(sessionGroup => sessionGroup.map(session => 'staff' + session.id.toString())).flat();
    this.curriculumPlanBlocks.droppableRoomPlaceIds$.next([...this.curriculumPlanBlocks.droppableRoomPlaceIds$.getValue(), ...droppableRoomIds]);
    this.curriculumPlanBlocks.droppableStaffPlaceIds$.next([...this.curriculumPlanBlocks.droppableStaffPlaceIds$.getValue(), ...droppableStaffIds]);
  }

  dropSubject(event: CdkDragDrop<any>) {
    if (!event.isPointerOverContainer) {
      return;
    }

    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      this.addSubjectToBlock.emit({
        ...event.item.data,
        currentIndex: this.block.subjectToYearGroups.length,
        listIndex: this.listIndex,
        blockTypeId: this.block.blockTypeId
      });
    }
  }

  dropStaff(event: CdkDragDrop<any>, subjectIndex: number) {
    const session = this.transformedSessions[subjectIndex].find(session => session.id.toString() === event.container.id.replace('staff', ''))
    if (session) {
      this.handleDropStaff(event, session);
    }
  }

  dropRoom(event: CdkDragDrop<any>, subjectIndex: number) {
    const session = this.transformedSessions[subjectIndex].find(session => session.id.toString() === event.container.id.replace('room', ''))
    if (session) {
      this.handleDropRoom(event, session);
    }
  }
}
