@use '../../../../../../styles/expanded-block' as style;
@import '../../../../../../../../assets/styles/variables';

.expanded-linear-block-container {
  background-color: $color-blue-grey-100;
  min-height: 100%;

  .info-wrapper {
    position: relative;

    .info-icon {
      display: block;
      position: absolute;
      top: 33px;
      left: 8px;
    }
  }

  .padding-left {
    padding-left: 26px !important;
  }

  .subject-selector {
    overflow: auto;

    table {
      margin-bottom: 16px;
      width: 100%;

      .body-th {
        cursor: pointer;
      }

      th {
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 20px;
        color: $color-blue-grey-600;

        &.active {
          border: 2px dashed $color-blue-grey-300;
        }

        .block {
          display: flex;
          flex-direction: column;
          box-sizing: border-box;

          .period-count {
            display: flex;

            .simple {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              padding-right: 10px;
              padding-left: 10px;
              width: 100%;
            }

            .double {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              padding-right: 10px;
              padding-left: 10px;
              width: 100%;
            }
          }
        }
      }

      td, th {
        border: 1px solid $color-blue-grey-200;

        &.class-name {
          font-style: normal;
          font-weight: 500;
          font-size: 12px;
          line-height: 20px;
          color: $color-blue-grey-600;
        }
      }
    }

    table, th, tr, td {
      border-spacing: 0;
      text-align: center;

      &.border {
        background-color: $color-blue-grey-100;
      }

      &.action {
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 20px;
        width: 32px;
        height: 40px;
        color: $color-blue-grey-600;
        cursor: pointer;
      }
    }
  }

  .disable .icon, .disable {
    color: $color-blue-grey-300 !important;
    pointer-events: none;
  }

  .disabled {
    pointer-events: none;
    color: $color-blue-grey-300;
  }

  .left-action {
    border-top-left-radius: 4px;
    pointer-events: all;
  }

  .actions {
    @include style.actions;
  }

  .error {
    @include style.error;
  }

  .white-color {
    color: $color-white-0 !important;
  }

  .dashed {
    border: 2px dashed $color-blue-800 !important;

    &:hover {
      background-color: $color-blue-grey-300 !important;
    }
  }

  .hidden {
    position: absolute;
    right: 56px;
    width: 110px;
    height: 46px;
    visibility: hidden !important;
    border: none;
    padding: 0;
  }
}

@media screen and (max-width: 1679px) {
  .expanded-linear-block-container {
    .actions {
      flex-wrap: wrap;

      div {
        width: 276px;
        max-width: 276px !important;
      }
    }
  }
}

@media screen and (max-width: 1439px) {
  .expanded-linear-block-container {
    .actions {
      flex-wrap: wrap;

      div {
        width: 252px;
        max-width: 252px !important;
      }
    }
  }
}

@media screen and (max-width: 1365px) {
  .expanded-linear-block-container {
    .actions {
      flex-wrap: wrap;

      div {
        width: 223px;
        max-width: 223px !important;
      }
    }
  }
}

@media screen and (max-width: 1279px) {
  .expanded-linear-block-container {
    .actions {
      flex-wrap: wrap;

      div {
        max-width: 215px !important;
      }
    }
  }
}
