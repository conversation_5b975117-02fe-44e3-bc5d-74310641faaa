﻿<div class="reasons-container">
  <div class="multiple-reason-container">

    <div *ngIf="(params.data.issueDetails.length > 1)">
      <div class="reasons">
        <div *ngFor="let period of periodCountMap | keyvalue">
          {{ period.key }} - {{ period.value }} issue<span *ngIf="period.value > 1">s</span>
        </div>
      </div>
    </div>

    <div *ngIf="(params.data.issueDetails.length === 1)">
      <div class="reason"
        [ngClass]="{ 'reason-cell': params.data.issueDetails[0].issueEntityType == 8 }">
          {{ params.data.issueDetails[0].reason }}
      </div>
    </div>

    <div class="show-reasons-button-container" *ngIf="(params.data.issueDetails.length > 1)">
      <bcm-button icon="far fa-rectangle-list" slot="suffix" (click)="onShowReasons()"></bcm-button>
    </div>

  </div>

</div>
