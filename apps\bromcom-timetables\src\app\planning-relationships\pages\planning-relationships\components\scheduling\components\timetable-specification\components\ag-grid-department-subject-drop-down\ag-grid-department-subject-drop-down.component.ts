import { Component, HostListener, ElementRef, OnInit, ViewChild } from '@angular/core';
import { BaseModalComponent } from '../../../../../../../../../_shared/components/BaseModalComponent';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs';
import { NestedTreeControl } from '@angular/cdk/tree';
import { MatTreeNestedDataSource } from '@angular/material/tree';
import { ISubject } from '../../../../../../../../../_shared/models/ISubject';
import { emit } from 'process';

interface Node {
  name: string;
  id?: number;
  children?: Node[];
  isSelected?: FormControl;
  isDepartment?: boolean;
  isExpanded?: boolean;
  isHide?:boolean;
}

@Component({
  selector: 'bromcom-ag-grid-department-subject-drop-down',
  templateUrl: './ag-grid-department-subject-drop-down.component.html',
  styleUrls: ['./ag-grid-department-subject-drop-down.component.scss'],
})
export class AgGridDepartmentSubjectDropDownComponent {
  @ViewChild('agGridDepartmentSubjectMultiselectDropdown') agGridMultiselectDropdown: ElementRef = {} as ElementRef;
  
  constructor(private elementRef: ElementRef) {}
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    if (!this.elementRef.nativeElement.contains(event.target)) {
      this.panelOpenState = false;
    }
  }
  
  subjectListWithDepartment: any[] = [];
  selectAllSubjectsForm = new FormControl(false);
  params!: any;
  gridApi!: any;
  originalData!: any[];
  data!: any[];
  checkboxes = false;
  value!: string;
  color = '';
  treeControl = new NestedTreeControl<Node>((node) => node.children);
  dataSource = new MatTreeNestedDataSource<Node>();
  changedDepartment: number | null = null;
  current: boolean[] = [];
  subjectsForm = new FormGroup({
    subjectList: new FormArray<FormControl<boolean>>([])
  });
  subjectList: ISubject[] = [];
  placeholder = '';
  panelOpenState = false;

  searchControl = new FormControl('');
  filterGroup = new FormGroup({
    items: new FormArray<FormControl<boolean | null>>([])
  });
  selectedItemIds: number[] = [];
  selectedItemTexts: string[] = [];
  oldSelectedItemTexts: string[] = [];
  allSubjectTitle: string = "All Subjects";

  agInit(params: any): void {
    this.params = params;
    this.gridApi = params.gridApi;

    this.checkboxes = params.checkboxes ?? false;
    this.originalData = params.values;
    this.data = params.values;
    this.value = params.value;
    this.placeholder = params.placeholder;
    this.selectedItemIds = params.value ?? [];
    this.color = params.color;

    this.initializeFormControls();
    this.onApply();
    this.subjectList = params.values.sort((a: ISubject) => a);

    const departments: Node[] = [];
    let subjectWithoutDepartment: Node[] = [];
    var updateSubjectIds = params.data.subjectsIds;

    this.subjectList.forEach((subject) => {
      if (subject.departmentId) {
          const departmentName = subject.departmentName || 'No Department';
          const departmentId = subject.departmentId || -1;
          if (departmentName === 'No Department' || departmentName === null) {
            const staffMember: Node = {
              id: subject.id,
              name: subject.name,
              isSelected: new FormControl(updateSubjectIds.includes(subject.id)),
              isDepartment: false,
              isExpanded: false
            };
            subjectWithoutDepartment.push(staffMember);
          } else {
            let existingDepartment = departments.find((dep) => dep.name === departmentName);

            if (!existingDepartment) {
              const isSelected = new FormControl(updateSubjectIds.includes(subject.id));
              existingDepartment = {
                id: departmentId,
                name: departmentName,
                isSelected,
                children: [],
                isDepartment: true,
                isExpanded: true
              };
              departments.push(existingDepartment);
            }

            const staffMember: Node = {
              id: subject.id,
              name: subject.name,
              isSelected: new FormControl(updateSubjectIds.includes(subject.id)),
              isDepartment: false,
              isExpanded: false
            };
            const isDuplicate = existingDepartment.children?.some((staff) => staff.id === staffMember.id);
            if (!isDuplicate) {
              existingDepartment.children?.push(staffMember);
            }
          }
        
      } else {
        const staffMember: Node = {
          id: subject.id,
          name: subject.name,
          isSelected: new FormControl(updateSubjectIds.includes(subject.id)),
          isDepartment: false,
          isExpanded: false
        };
        subjectWithoutDepartment.push(staffMember);
      }
    });

    this.dataSource.data = this.originalData = [...departments, ...subjectWithoutDepartment];
    this.dataSource.data.forEach(item =>{
      if(item.isDepartment) {
        item.isSelected?.setValue(item.children?.every(node => node.isSelected?.value))
      }
    });

    if(this.dataSource.data.every(item => item.isSelected?.value)){
      this.checkAllSelected();
    }

    //this.expandAllNodes(this.dataSource.data);
    this.createStaffListFormControls(this.dataSource.data);
  }

  initializeFormControls() {
    const itemsArray = this.filterGroup.controls.items as FormArray;
    if (itemsArray && this.originalData?.length) {
      itemsArray.clear();
      this.originalData?.forEach((data) => {
        itemsArray.push(new FormControl(this.selectedItemIds?.includes(data.id)));
      })
    }
  }

  ngOnInit() {
    this.searchControl.valueChanges
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe(async () => {
        this.resetFilter();
        setTimeout(() => {
          this.searchFilter();
        }, 0);
      });
  }

  getValue(): number[] {
    return this.selectedItemIds;
  }

  updateValue(): void {
    if (this.params.node.rowPinned === 'bottom') {
      this.gridApi.getPinnedBottomRow(0).setDataValue([this.params.colDef.field], this.selectedItemIds);
    } else {
      this.gridApi.getRowNode(this.params.data.id).setDataValue([this.params.colDef.field], this.selectedItemIds);
    }
  }

  selectAll(): void {
    if(this.selectAllSubjectsForm.value){
      this.selectAllSubjects(this.dataSource.data.filter(d => !d.isHide));
      this.dataSource.data.forEach(node => {
        if(!node.isHide){
          if(node.isSelected && !node.isDepartment){
            this.selectedItemIds.push(node.id!);       
            this.selectedItemTexts.push(node.name!);
          }    
          this.toggleDepartmentSelection(node);
        }
      })
    }
    else{
      this.clearAll();
    }
  }

  uncheckItem(text:string): void {
    if(text == this.allSubjectTitle){
      this.clearAll();
    }
    else{
      this.dataSource.data.forEach(item =>{
        if(!item.isDepartment && item.name == text) {
          item.isSelected?.setValue(false);
          this.toggleSubject(item);
          return;
        }
        else{
          item.children?.forEach(node =>{
            if(node.name == text) {
              node.isSelected?.setValue(false);
              this.toggleSubject(node);
              return;
            }
          });
        }
      });
    }
  }

  clearAll() {
    const textIndex = this.selectedItemTexts.indexOf(this.allSubjectTitle);
      if (textIndex !== -1) {
        this.selectedItemTexts.splice(textIndex, 1);
      }
    this.clearSelection(this.dataSource.data)
    this.dataSource.data.forEach(item =>{
      if(item.isDepartment)
      this.clearSelection(item.children!)
    });
    this.selectAllSubjectsForm.setValue(false);
  }

  resetFilter() {
    this.data = this.originalData;
  }

  searchFilter() {
    const searchTerm = this.searchControl?.value?.toLowerCase() || '';
    this.dataSource.data.forEach(item => {
      const itemName = item.name ? item.name.toLowerCase() : '';
      item.isHide = searchTerm ? !itemName.includes(searchTerm) : false;

      if(item.isHide){
        if (item.children && item.children.length > 0) {
          item.children.forEach(child => {
              const childName = child.name ? child.name.toLowerCase() : '';
              child.isHide = searchTerm ? !childName.includes(searchTerm) : false;
              if(!child.isHide){
                item.isHide = false;     
                this.disableAllSelected();
              }
          });
        }
      }
      else{
        if (item.children && item.children.length > 0) {
          item.children.forEach(child => {
              child.isHide = false;
              this.disableAllSelected();
          });
        }
      }  
    });
    this.searchedCheckAllSelected();
  }

  searchedCheckAllSelected(){
    var isSelect = !this.dataSource.data.every(node => node.isHide) 
      && this.dataSource.data.every(node => node.isHide 
        || (node.isDepartment && node.children?.every(child => child.isHide || child.isSelected?.value))
        || (!node.isDepartment && node.isSelected?.value))
      
    this.selectAllSubjectsForm.setValue(isSelect, { emitEvent: false});
  }

  onApply() {
    const selectedItemIds: number[] = [];
    const selectedItemTexts: string[] = [];
    this.filterGroup.getRawValue().items?.forEach((isSelected, index) => {
      if (isSelected) {
        const selectedItem = this.originalData[index];
        selectedItemIds.push(selectedItem.id);
        selectedItemTexts.push(selectedItem.text);
      }
    });
    this.selectedItemIds = selectedItemIds;
    this.selectedItemTexts = selectedItemTexts;

    this.searchControl.setValue('');
    this.updateValue();
    this.panelOpenState = false
  }

  onCancel() {
    this.panelOpenState = false;
    this.clearAll();
    this.searchControl.setValue('');
    this.initializeFormControls();
  }

  
  hasChild = (_: number, node: Node) => !!node.children && node.children.length > 0;

  toggleDepartmentSelection(node: Node): void {
    this.changedDepartment = node.id ?? null;
    node.isSelected?.setValue(node.isSelected?.value);

    if (node.isSelected?.value) {
      this.selectAllSubjects(node.children?.filter(d => !d.isHide)!);
      this.checkAllSelected();
    } else {
      this.clearSelection(node.children!);
      this.updateSubjectEvent(this.selectedItemIds);
      this.disableAllSelected();
    }
  }

  toggleSubject(subject: Node): void {
    if (subject.isSelected?.value) {
      if (!this.selectedItemIds.includes(subject.id!)) {
        this.selectedItemIds.push(subject.id!);       
        this.selectedItemTexts.push(subject.name!);
        this.updateSubjectEvent(this.selectedItemIds);
      } 
      this.checkAllSelected();
    } else {
      const index = this.selectedItemIds.indexOf(subject.id!);
      if (index !== -1) {
        this.selectedItemIds.splice(index, 1); 
        this.selectedItemTexts.splice(index, 1);
        this.updateSubjectEvent(this.selectedItemIds);
        this.disableAllSelected();
      }
    }
  }

  checkAllSelected(){
    this.dataSource.data.forEach(node => {
      if(!node.isHide && node.isDepartment && node.children?.every(child => child.isHide || child.isSelected?.value)){
        node.isSelected?.setValue(true, {emitEvent: false, onlySelf: true});
      }
    })
    if(this.dataSource.data.every(node => {
      return !node.isHide && node.isSelected?.value && (node.children?.every(child => child.isHide || child.isSelected?.value) ?? true);
    })){
      this.selectedItemTexts = [];
      this.selectedItemTexts.push(this.allSubjectTitle);
      this.selectAllSubjectsForm.setValue(true, { emitEvent: false});
    } 
  }

  disableAllSelected(){
    this.dataSource.data.forEach(node => {
      if(node.isDepartment && node.isSelected?.value && !node.children?.every(child => child.isSelected?.value)){
        node.isSelected?.setValue(false, {emitEvent: false, onlySelf: true});
      }
    })
    if (this.selectedItemTexts[0] == this.allSubjectTitle) {
      this.selectAllSubjectsForm.setValue(false, { emitEvent: false});
      this.selectedItemIds = [];
      this.selectedItemTexts = [];
      this.dataSource.data.forEach(d => {
        d.children?.forEach(node => {  
          if(node.isSelected?.value){
            this.selectedItemIds.push(node.id!);       
            this.selectedItemTexts.push(node.name!);
          }
        });
        if(d.isSelected?.value && !d.isDepartment){
          this.selectedItemIds.push(d.id!);       
          this.selectedItemTexts.push(d.name!);
        }
      })
    }   
  }

  selectAllSubjects(subjects: Node[]): void {
    subjects?.forEach((subject) => {
      subject.isSelected?.setValue(true);
      if (!this.selectedItemIds.includes(subject.id!) && !subject.isDepartment) {
        this.selectedItemIds.push(subject.id!);       
        this.selectedItemTexts.push(subject.name!);
        this.updateSubjectEvent(this.selectedItemIds);
      }
    });
  }

  clearSelection(subjects: Node[]): void {
    subjects?.forEach((subject) => {         
      subject.isSelected?.setValue(false);
      const indices: number[] = [];

      for (let i = 0; i < this.selectedItemIds.length; i++) {
        if (this.selectedItemIds[i] === subject.id) {
          indices.push(i);
        }
      }
    
      for (let i = indices.length - 1; i >= 0; i--) {
        const index = indices[i];
        if (index !== -1) {
          this.selectedItemIds.splice(index, 1); 
          const textIndex = this.selectedItemTexts.indexOf(subject.name);
          if (textIndex !== -1) {
            this.selectedItemTexts.splice(textIndex, 1);
          }
          this.updateSubjectEvent(this.selectedItemIds);
        }
      }
    });
  }

  updateSubjectEvent(subjectIds: number[]): void {
    this.params.api.dispatchEvent({
      type: 'cellValueChanged',
      node: this.params.node,
      data: this.params.node.data,
      colDef: this.params.column.getColDef(),
      column: this.params.column,
      oldValue: this.params.data.subjectIds,
      newValue: subjectIds,
      rowIndex: this.params.node.rowIndex,
      api: this.params.api,
      columnApi: this.params.columnApi,
      context: this.params.context
    });
  }

  private expandAllNodes(nodes: Node[]): void {
    nodes.forEach((node) => {
      this.treeControl.expand(node);

      if (node.children) {
        this.expandAllNodes(node.children);
      }
    });
  }

  private createStaffListFormControls(nodes: Node[]): void {
    const subjectListControls = this.subjectsForm.get('subjectList') as FormArray;

    nodes.forEach((node) => {
      if (node.isSelected) {
        subjectListControls.push(node.isSelected);
        this.subjectListWithDepartment.push(node);
        this.current.push(false);
      }

      if (node.children) {
        this.createStaffListFormControls(node.children);
      }
    });
  }
}
