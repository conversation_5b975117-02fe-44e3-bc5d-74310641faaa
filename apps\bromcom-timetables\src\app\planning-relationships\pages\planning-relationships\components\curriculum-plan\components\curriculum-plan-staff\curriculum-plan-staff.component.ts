import {
  ChangeDetector<PERSON>ef,
  Component,
  EventEmitter, Input,
  OnDestroy,
  OnInit,
  Output
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { RelationshipsService } from '../../../../../../services/relationships.service';
import { LoadingSpinnerService } from '@bromcom/ui';
import { ICurriculumPlanStaff } from '../../../../../../../_shared/models/ICurriculumPlanStaff';
import { CurriculumPlanService } from '../../../../../../services/curriculum-plan.service';
import { BaseCurriculumPlanSidePanel } from '../../_shared/base-curriculum-plan-side-panel';
import { debounceTime, distinctUntilChanged, forkJoin, takeUntil } from 'rxjs';
import { IRelationshipsStaff } from '../../../../../../../_shared/models/IRelationshipsStaff';
import { filterByValueOnColumns, sortDataByColumn } from '@bromcom/core';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';

@Component({
  selector: 'bromcom-curriculum-plan-staff',
  templateUrl: './curriculum-plan-staff.component.html',
  styleUrls: ['./curriculum-plan-staff.component.scss']
})
export class CurriculumPlanStaffComponent extends BaseCurriculumPlanSidePanel<ICurriculumPlanStaff> implements OnInit, OnDestroy {
  @Input() showActions = false;
  @Output() editStaffs: EventEmitter<boolean> = new EventEmitter();
  @Output() displayTimetable: EventEmitter<ICurriculumPlanStaff> = new EventEmitter();
  @Output() swapStaff: EventEmitter<ICurriculumPlanStaff> = new EventEmitter();
  @Output() substituteStaff: EventEmitter<ICurriculumPlanStaff> = new EventEmitter();

  originalStaffData: IRelationshipsStaff[] = [];
  showAllSwitchControl = new FormControl(true);
  staffTypeControl = new FormControl(1);
  staffTypes: { id: number, text: string }[] = [];
  originalDataByYear: ICurriculumPlanStaff[] = [];
  droppableStaffPlaceIds: string[] = [];
  yearGroupId: number | null = null;
  sumOfAllAssignedContactTimes: { [key: string]: number } = {}
  staffToSessionActive: number | null = null;

  constructor(
    protected override relationships: RelationshipsService,
    protected override curriculumPlan: CurriculumPlanService,
    private curriculumPlanBlocks: CurriculumPlanBlocksService,
    protected override loading: LoadingSpinnerService,
    protected override changeDetectorRef: ChangeDetectorRef
  ) {
    super(relationships, curriculumPlan, loading, changeDetectorRef)
  }

  override ngOnInit(): void {
    this.filterGroup.addControl('code', new FormControl(false));
    this.filterGroup.addControl('lastNameFirstName', new FormControl(false));
    this.filterGroup.addControl('subject', new FormControl(false));

    this.sorting = {
      code: 'default',
      lastNameFirstName: 'asc',
      subject: 'default',
      assigned: 'default',
      totalContactTime: 'default'
    }
    this.relationships.staffToSessionActiveId$.next(null);
    this.relationships.staffToSessionActiveId$
      .pipe(
        takeUntil(this.unsubscribe$),
        distinctUntilChanged())
      .subscribe(staffToSessionActive => {
        this.staffToSessionActive = staffToSessionActive;
        staffToSessionActive
          ? this.relationships.getStaffsListForSession(staffToSessionActive).subscribe(() => {
            setTimeout(() => this.setVisibleData(true), 0)
          })
          : this.relationships.getStaffsList(this.projectId).subscribe(() => {
            setTimeout(() => this.setVisibleData(true), 0)
          })
        if (this.yearGroupId && !this.showAllSwitchControl.value) {
          this.curriculumPlan.getAllAssignedContactTimeForStaffByYearGroup(this.timetableId, this.yearGroupId).subscribe();
        } else {
          this.curriculumPlan.getAllAssignedContactTimeForStaff(this.timetableId).subscribe();
        }
      })

    this.curriculumPlanBlocks.droppableStaffPlaceIds$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(ids => {
        this.droppableStaffPlaceIds = ids.concat('timetable-canvas');
        this.changeDetectorRef.detectChanges();
      });

    this.staffTypeControl.valueChanges
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(() => this.setVisibleData(true))

    this.showAllSwitchControl.valueChanges
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(() => this.setVisibleData(true))

    this.relationships.originalStaffsData$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(originalStaffData => {
        this.originalStaffData = originalStaffData;
        this.changeDetectorRef.detectChanges();
      })

    this.curriculumPlan.staffTypeOptions$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(staffTypes => {
        this.staffTypes = staffTypes.map(staff => ({ id: staff.id, text: staff.description }));
        this.staffTypeControl.setValue(staffTypes[0]?.id);
      })

    this.curriculumPlan.assignedContactTimesStaff$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(contactTimes => {
        this.originalData = this.initSort(contactTimes, 'lastNameFirstName');
        this.setVisibleData();
      })

    this.curriculumPlan.assignedContactTimesStaffByYearGroup$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(contactTimes => {
        this.originalDataByYear = contactTimes;
        this.setVisibleData();
      })

    this.curriculumPlan.selectedYearGroup$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(yearGroupId => {
        if (yearGroupId) {
          this.yearGroupId = yearGroupId;
          this.curriculumPlan.getAllAssignedContactTimeForStaffByYearGroup(this.timetableId, yearGroupId).subscribe()
        }
      })

    this.curriculumPlanBlocks.stateChanged$
      .pipe(
        takeUntil(this.unsubscribe$))
      .subscribe(() => {
        if (this.yearGroupId && !this.showAllSwitchControl.value) {
          this.curriculumPlan.getAllAssignedContactTimeForStaffByYearGroup(this.timetableId, this.yearGroupId).subscribe();
        } else {
          this.curriculumPlan.getAllAssignedContactTimeForStaff(this.timetableId).subscribe();
        }
      })

    this.searchControl.valueChanges
      .pipe(
        debounceTime(500),
        takeUntil(this.unsubscribe$))
      .subscribe((searchValue) => {
        if (searchValue && this.filterControl.value) {
          this.filterControl.setValue('', { emitEvent: false });
        }

        if ((searchValue === null || searchValue) && !this.filterControl.value) {
          const staffToSessionActiveId = this.relationships.staffToSessionActiveId$.getValue();
          this.relationships.staffToSessionActiveId$.next(staffToSessionActiveId);
          this.setVisibleData(true);
        }
      });

    this.filterControl.valueChanges
      .pipe(
        debounceTime(500),
        takeUntil(this.unsubscribe$))
      .subscribe((filterValue) => {
        this.isFilterApplied = !!filterValue;

        if (filterValue && this.searchControl.value) {
          this.searchControl.setValue('', { emitEvent: false });
        }

        if ((filterValue === null || filterValue) && !this.searchControl.value) {
          const staffToSessionActiveId = this.relationships.staffToSessionActiveId$.getValue();
          this.relationships.staffToSessionActiveId$.next(staffToSessionActiveId);
          this.setVisibleData(true);
        }
      });

    this.filterGroup.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(() => {
        this.setVisibleData(true);
        const staffToSessionActiveId = this.relationships.staffToSessionActiveId$.getValue();
        this.relationships.staffToSessionActiveId$.next(staffToSessionActiveId);
      });

    forkJoin([
      this.curriculumPlan.getTypeOptions(),
      this.curriculumPlan.getAllAssignedContactTimeForStaff(this.timetableId)
    ]).subscribe(() => this.setVisibleData(true))
  }

  onEditStaffs(): void {
    this.editStaffs.emit(true);
  }

  override sort(column: keyof ICurriculumPlanStaff): void {
    setTimeout(() => {
      Object.keys(this.sorting).forEach(key => {
        if (Object.prototype.hasOwnProperty.call(this.sorting, key)) {
          if (key !== column) {
            this.sorting[key] = 'default';
          }
        }
      });

      this.sorting[column as string] = this.sorting[column as string] === 'asc'
        ? 'desc'
        : this.sorting[column as string] === 'desc'
          ? 'default'
          : 'asc';

      if (this.sorting[column as string] === 'default') {
        this.setVisibleData(true);
      } else {
        this.visibleData = [...sortDataByColumn([...this.visibleData], this.sorting[column as string], column)];
      }
    }, 100);
  }

  onDisplayTimetableClick(data: ICurriculumPlanStaff) {
    this.displayTimetable.emit(data);
  }

  onSwapStaff(data: ICurriculumPlanStaff) {
    this.swapStaff.emit(data);
  }

  onSubstituteStaff(data: ICurriculumPlanStaff) {
    this.substituteStaff.emit(data);
  }

  private setVisibleData(isScroll = false): void {
    const searchValue = this.searchControl.value ?? '';
    const filterValue = this.filterControl.value ?? '';
    const filterValues = this.filterGroup.getRawValue();
    const columnsToSearchIn = Object.keys(filterValues);
    const columnsToFilterIn = Object.keys(filterValues).filter(key => filterValues[key]);
    const staffTypeId = this.staffTypeControl.value;
    const dataToFilter = this.showAllSwitchControl.value ? this.originalData : this.originalDataByYear;
    const searched = filterByValueOnColumns<ICurriculumPlanStaff>(searchValue, [...dataToFilter], columnsToSearchIn);
    const filtered = filterByValueOnColumns<ICurriculumPlanStaff>(filterValue, searched, columnsToFilterIn);
    const visibleData = staffTypeId ? this.filterByStaffType(filtered, staffTypeId) : filtered;
    const sortingColumn = Object.entries(this.sorting).find(([key, value]) => value !== 'default') ?? ['lastNameFirstName', 'default'];
    this.visibleData = [...sortDataByColumn(visibleData, sortingColumn[1], sortingColumn[0] as keyof ICurriculumPlanStaff)];

    this.sumOfAllAssignedContactTimes = {};
    this.visibleData.forEach((obj) => {
      const { staffId, assigned } = obj;
      if (this.sumOfAllAssignedContactTimes[staffId]) {
        this.sumOfAllAssignedContactTimes[staffId] += assigned;
      } else {
        this.sumOfAllAssignedContactTimes[staffId] = assigned;
      }
    });

    if (isScroll) {
      this.scrollTop();
    }
    this.changeDetectorRef.detectChanges();
  }

  private filterByStaffType(list: ICurriculumPlanStaff[], staffTypeId: number): ICurriculumPlanStaff[] {
    return list.filter(staff => {
      return this.originalStaffData.find(originalStaff => originalStaff.id === staff.staffId)?.typeId === staffTypeId;
    })
  }
}
