@import 'apps/bromcom-timetables/src/assets/styles/variables';
@import 'apps/bromcom-timetables/src/app/planning-relationships/pages/planning-relationships/components/scheduling/components/timetable-specification/styles/tts-ag-grid';

.tts-limitation-container {
  padding: 24px 0 0;

  .info-msg {
    width: 100%;
    color: $color-blue-100;
    align-content: center;
    height: 40px;
    border-radius: 8px;
    margin-bottom: 26px;
  }

  .lbl-info {
    color: $color-blue-900;
    font-weight: 500;
    line-height: 40px;
    size: 16px;
    padding-left: 10px;
  }

  .spn-info {
    padding-left: 10px;
  }

  .action-bar {
    @include action-bar;
  }

  .table-container {
    @include table-container;
  }


  ::ng-deep .mat-expansion-panel-header-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    .title-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  ::ng-deep .bcm-input {
    height: 30px !important;
    line-height: 20px !important;
    font-size: 14px !important;
    padding: 0 !important; 
    border-color: var(--bcm-new-ds-color-slate-300) !important;
  }

  ::ng-deep .bcm-input__container {
    border-color: var(--bcm-new-ds-color-slate-300) !important;
  }

  ::ng-deep .tw-text-red-600 {
    display: none;
  }

  ::ng-deep .ag-header-cell.left-padding {
    padding-right: 0;
  }

  ::ng-deep .ag-popup-editor {
    background-color: transparent;
    z-index: 10000;
    min-width: 142px;
    // -48 for grid checkbox width
    // 4.8 for gridoption columns flex sum
    // -34 for inner padding
    width: calc(((100% - 48px) / 3.8 * 1.5) - 34px);
    margin: -44px 16px 0;
  }

  ::ng-deep .mat-expansion-panel.new-row {
    margin-bottom: -32px;
  }
}
