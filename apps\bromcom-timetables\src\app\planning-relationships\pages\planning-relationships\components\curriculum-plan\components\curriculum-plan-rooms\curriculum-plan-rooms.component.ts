import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { BaseCurriculumPlanSidePanel } from '../../_shared/base-curriculum-plan-side-panel';
import { FormControl } from '@angular/forms';
import { ICurriculumPlanRoom } from '../../../../../../../_shared/models/ICurriculumPlanRoom';
import { RelationshipsService } from '../../../../../../services/relationships.service';
import { CurriculumPlanService } from '../../../../../../services/curriculum-plan.service';
import { LoadingSpinnerService } from '@bromcom/ui';
import { distinctUntilChanged, switchMap, takeUntil } from 'rxjs';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';

@Component({
  selector: 'bromcom-curriculum-plan-rooms',
  templateUrl: './curriculum-plan-rooms.component.html',
  styleUrls: ['./curriculum-plan-rooms.component.scss']
})
export class CurriculumPlanRoomsComponent extends BaseCurriculumPlanSidePanel<ICurriculumPlanRoom> implements OnInit, OnDestroy {
  @ViewChild('editRoomsModal') editRoomsModal!: ElementRef;
  @Output() editRooms: EventEmitter<boolean> = new EventEmitter();
  @Output() displayTimetable: EventEmitter<ICurriculumPlanRoom> = new EventEmitter();
  @Output() swapRoom: EventEmitter<ICurriculumPlanRoom> = new EventEmitter();
  @Input() showActions = false;
  @Input() disableDisplayTimetable = false;

  droppableRoomPlaceIds: string[] = [];
  roomToSessionActive: number | null = null;

  constructor(
    protected override relationships: RelationshipsService,
    protected override curriculumPlan: CurriculumPlanService,
    private curriculumPlanBlocks: CurriculumPlanBlocksService,
    protected override loading: LoadingSpinnerService,
    protected override changeDetectorRef: ChangeDetectorRef
  ) {
    super(relationships, curriculumPlan, loading, changeDetectorRef);
  }

  override ngOnInit(): void {
    super.ngOnInit()

    this.filterGroup.addControl('code', new FormControl(false));
    this.filterGroup.addControl('teacher', new FormControl(false));
    this.filterGroup.addControl('type', new FormControl(false));
    this.filterGroup.addControl('site', new FormControl(false));
    this.filterGroup.addControl('assignedContactTime', new FormControl(false));

    this.sorting = {
      code: 'asc',
      teacher: 'default',
      type: 'default',
      site: 'default',
      assignedContactTime: 'default'
    }

    this.curriculumPlanBlocks.droppableRoomPlaceIds$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(ids => {
        this.droppableRoomPlaceIds = ids.concat('timetable-canvas');
        this.changeDetectorRef.detectChanges();
      });

    this.curriculumPlan.assignedContactTimesRooms$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(rooms => {
        this.originalData = rooms;
        this.setData(false);
      });

    this.curriculumPlan.getListOfSitesOptions(this.projectId)
      .subscribe(sites => this.curriculumPlan.sitesOptions$.next(sites));
    this.curriculumPlan.getListOfTypesOptions(this.projectId)
      .subscribe(types => this.curriculumPlan.typesOptions$.next(types));
    this.curriculumPlan.getStaffRoomRelationToSubject(this.timetableId)
      .subscribe(types => this.curriculumPlan.allRelationshipRelationsData$.next(types));

    this.relationships.roomsData$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$),
        switchMap(() => this.curriculumPlan.getAllAssignedContactTimeForRoom(this.timetableId)))
      .subscribe(() => {
        this.changeDetectorRef.detectChanges();
      });

    this.relationships.roomToSessionActiveId$.next(null);
    this.relationships.roomToSessionActiveId$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(roomToSessionActive => {
        this.roomToSessionActive = roomToSessionActive;
        roomToSessionActive
          ? this.relationships.getRoomsListForSession(roomToSessionActive).subscribe(() => {
          })
          : this.relationships.getRoomsList(this.projectId).subscribe(() => {
          })
      })
      
    this.relationships.roomToNccActiveId$
    .pipe(
      distinctUntilChanged(),
      takeUntil(this.unsubscribe$)
    ).subscribe(roomToNccActive => {
      this.relationships.getRoomsList(this.projectId).subscribe(() => {})
    }) 

    this.curriculumPlan.getAllAssignedContactTimeForRoom(this.timetableId).subscribe()

    this.curriculumPlanBlocks.stateChanged$
      .pipe(
        takeUntil(this.unsubscribe$))
      .subscribe(() => {
        this.curriculumPlan.getAllAssignedContactTimeForRoom(this.timetableId).subscribe()
      })
  }

  onEditRooms(): void {
    this.editRooms.emit(true);
  }

  onDisplayTimetableClick(data: ICurriculumPlanRoom) {
    this.displayTimetable.emit(data);
  }
  
  onSwapRoom(data: ICurriculumPlanRoom) {
    this.swapRoom.emit(data);
  }
}
