<div class="block-vertical filter-popup" id="defaultFilterItems" #defaultFilterPopup>
    <div class="vertical" [formGroup]="listGroup">
        <ng-container formArrayName="items">
            <span class="switch-container">
                <span *ngFor="let item of _dataItems; let i = index" class="switch-container-inner">
                    <bromcom-switch class="data-item switch-bg" [label]="_dataItems[i].text" [formControlName]="i"></bromcom-switch>
                    <bcm-tooltip [message]="_dataItems[i].description" trigger="hover">
                        <i class="far fa-info-circle"></i>
                    </bcm-tooltip>
                </span>
            </span>
        </ng-container>
    </div>
</div>