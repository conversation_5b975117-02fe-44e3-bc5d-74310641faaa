import { ICellRendererComp, ICellRendererParams } from 'ag-grid-community';

export class ScheduledPeriodsCellRenderer implements ICellRendererComp {
  eGui: HTMLSpanElement;
  scheduledPeriods!: number;
  allPeriods!: number;
  params!: ICellRendererParams & { onClick: () => {} };

  constructor() {
    this.eGui = document.createElement('div');
  }

  init(params: ICellRendererParams & { onClick: () => {} }) {
    this.scheduledPeriods = params.data.scheduledPeriodsCount;
    this.allPeriods = params.data.sessionsTotal;
    this.params = params;
    this.updateSubject();
  }

  updateSubject(): void {
    this.eGui.style.display = 'flex';
    this.eGui.style.alignItems = 'center';
    this.eGui.style.width = '100%';
    this.eGui.innerHTML = `<div class="scheduled-periods">${this.scheduledPeriods}/${this.allPeriods}</div>`;
    this.eGui.addEventListener('click', this.params.onClick.bind(this));
  }

  getGui() {
    return this.eGui;
  }

  refresh(params: ICellRendererParams) {
    this.scheduledPeriods = params.data.scheduledPeriodsCount;
    this.allPeriods = params.data.sessionsTotal;
    this.eGui.innerHTML = '';
    this.updateSubject();

    return true;
  }
}
