import { Component, ElementRef, ViewChild } from '@angular/core';

@Component({
  selector: 'bromcom-ag-grid-multiselect-dropdown',
  templateUrl: './ag-grid-multiselect-dropdown.component.html',
  styleUrls: ['./ag-grid-multiselect-dropdown.component.scss']
})
export class AgGridMultiselectDropdownComponent {
  @ViewChild('agGridMultiselectDropdown') agGridMultiselectDropdown: ElementRef = {} as ElementRef;
  params!: any;
  gridApi!: any;
  data!: any[];
  checkboxes = false;
  value: any[] = [];
  placeholder = '';
  template = `<div style="display: flex; align-items: center; justify-content: space-between; width: 100%">
              <span style="display: flex; align-items: center; justify-content: flex-start; width: 100%">
                <span class="{{class}}" style="width:16px; display: none; margin-right: 10px;">
                    <bcm-icon icon="{{icon}}" slot="suffix"></bcm-icon>
                </span>
                <span class="{{class}}">{{text}}</span>
              </span>
             </div>`;

  agInit(params: any): void {
    this.params = params;
    this.gridApi = params.gridApi;

    this.checkboxes = params.checkboxes ?? false;
    this.data = params.values.map((value: { id: number, name: string }) => ({ ...value, text: value.name }));
    this.value = params.value.length ? params.value : [];
    this.placeholder = params.placeholder;
  }

  ngAfterViewInit(): void {
    if (this.value) {
      this.agGridMultiselectDropdown.nativeElement.set(this.value)
    }
  }

  getValue(): number[] {
    return this.value;
  }

  updateValue(): void {
    this.agGridMultiselectDropdown.nativeElement.get().then((data: number[]) => {
      this.value = data ?? [];

      if (this.params.node.rowPinned === 'bottom') {
        this.gridApi.getPinnedBottomRow(0).setDataValue([this.params.colDef.field], data);
      } else {
        this.gridApi.getRowNode(this.params.data.id).setDataValue([this.params.colDef.field], data);
      }
    })
  }
}
