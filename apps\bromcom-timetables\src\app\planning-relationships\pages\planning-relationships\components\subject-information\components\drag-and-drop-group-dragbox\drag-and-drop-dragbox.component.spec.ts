import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DragAndDropDragboxComponent } from './drag-and-drop-dragbox.component';

describe('GroupBoxDragComponent', () => {
  let component: DragAndDropDragboxComponent;
  let fixture: ComponentFixture<DragAndDropDragboxComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DragAndDropDragboxComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(DragAndDropDragboxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
