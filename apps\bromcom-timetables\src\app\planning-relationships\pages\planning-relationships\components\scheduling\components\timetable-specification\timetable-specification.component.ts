import { Component, ElementRef, ViewChild } from '@angular/core';
import { BaseModalComponent } from '../../../../../../../_shared/components/BaseModalComponent';
import { MatTabChangeEvent } from '@angular/material/tabs';

@Component({
  selector: 'bromcom-timetable-specification',
  templateUrl: './timetable-specification.component.html',
  styleUrls: ['./timetable-specification.component.scss']
})
export class TimetableSpecificationComponent extends BaseModalComponent {
  @ViewChild('timetableSpecificationModal', { static: false }) timetableSpecificationModal!: ElementRef;
  timetableId!: number;
  projectId!: number;
  tabIndex = 0;

  show(timetableId: number, projectId: number, tabIndex: number = 0): void {
    this.timetableId = timetableId;
    this.projectId = projectId;
    this.isOpen = true;
    this.tabIndex = tabIndex;
    setTimeout(() => {
      this.timetableSpecificationModal.nativeElement.show();
    }, 100);
  }

  onTabChange(event: MatTabChangeEvent) {
    this.tabIndex = event.index;
  }
}
