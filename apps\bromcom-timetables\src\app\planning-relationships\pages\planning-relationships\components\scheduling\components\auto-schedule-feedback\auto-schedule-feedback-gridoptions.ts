import { customComparator, transformToAGGridConfig } from '@bromcom/core';
import { AutoScheduleFeedbackComponent } from './auto-schedule-feedback.component';
import { SuccessRateCellRenderer } from './success-rate-cell-renderer';
import { ScheduledPeriodsCellRenderer } from './scheduled-periods-cell-renderer';
import { ICellRendererParams } from 'ag-grid-community';

export function gridOptions(this: AutoScheduleFeedbackComponent) {
  return transformToAGGridConfig({
    getRowId: params => params.data.blockId,
    animateRows: false,
    suppressRowClickSelection: true,
    suppressClickEdit: true,
    suppressCellFocus: true,
    domLayout: 'autoHeight',
    tooltipShowDelay: 500,
    rowHeight: 48,
    defaultColDef: {
      comparator: customComparator
    },
    columnDefs: [
      {
        field: 'block',
        headerName: this.translate.instant('Block Name'),
        wrapHeaderText: true,
        minWidth: 160,
        flex: 1,
        editable: true,
        menuTabs: ['filterMenuTab']
      },
      {
        field: 'yearGroup',
        headerName: this.translate.instant('Year Group'),
        wrapHeaderText: true,
        minWidth: 140,
        flex: 0.5,
        editable: true,
        menuTabs: ['filterMenuTab']
      },
      {
        field: 'subjects',
        headerName: this.translate.instant('Subjects'),
        minWidth: 160,
        flex: 1,
        editable: true,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: params => params.data.subjects.join(', '),
        cellStyle: {
          display: 'block',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          lineHeight: '24px'
        }
      },
      {
        field: 'departments',
        headerName: this.translate.instant('Departments'),
        minWidth: 160,
        flex: 1,
        editable: true,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: params => params.data.departments.join(', '),
        cellStyle: {
          display: 'block',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          lineHeight: '24px'
        }
      },
      {
        field: 'scheduledPeriods',
        headerName: this.translate.instant('Scheduled Sessions'),
        wrapHeaderText: true,
        minWidth: 160,
        flex: 1,
        editable: true,
        menuTabs: ['filterMenuTab'],
        comparator: customComparator,
        valueGetter: params => {
          return params.data.scheduledPeriodsCount+'/'+params.data.sessionsTotal;
        },
        cellRenderer: ScheduledPeriodsCellRenderer,
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            onClick: () => this.openExpandedView(params.data.blockId)
          }
        },
        cellStyle: () => {
          return {
            color: '#3B82F6',
            fontWeight: '700'
          }
        }
      },
      {
        field: 'successRate',
        headerName: this.translate.instant('Success Rate'),
        wrapHeaderText: true,
        minWidth: 160,
        flex: 1,
        editable: true,
        menuTabs: ['filterMenuTab'],
        valueGetter: params => {
          return params.data.sessionsTotal === 0 ? 0 : Math.round(params.data.scheduledPeriodsCount / params.data.sessionsTotal * 100);
        },
        cellRenderer: SuccessRateCellRenderer
      }
    ]
  })
}
