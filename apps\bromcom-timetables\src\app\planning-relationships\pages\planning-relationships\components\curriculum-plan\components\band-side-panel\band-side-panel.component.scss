@import '../../../../../../../../assets/styles/variables';

.band-selector-container {
  width: 40px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 18px 14px;
  background: $color-blue-grey-100;
  margin-right: 8px;

  border: 1px solid $color-blue-200;
  border-radius: 4px;

  ::ng-deep .band-prev,
  ::ng-deep .band-next,
  ::ng-deep .band-actions {
    text-align: center;

    .icon {
      cursor: pointer;
      color: $color-blue-grey-700;

      i.fa-arrow-to-bottom,
      i.fa-arrow-to-top,
      i.far fa-pen,
      i.fa-trash-alt,
      i.far fa-check,
      i.fa-times {
        font-size: 16px;
      }
    }
  }

  .band-main-content {
    height: 106px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .band-name {
      line-height: 32px;
      font-size: 16px;
      text-align: center;
    }

    .band-name-input {
      width: 40px;
      max-height: 32px;

      ::ng-deep .bcm-input__container {
        padding: 0;
      }

      ::ng-deep .bcm-input-element.bcm-input__container--medium {
        padding: 0;
        font-size: 16px;
        text-align: center !important;
      }

      ::ng-deep .bcm-caption-area {
        display: none;
      }
    }

    .band-actions {
      height: auto;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;

      .icon {
        margin-top: 16px;
      }
    }
  }
}

.disable .icon, .disable {
  color: $color-blue-grey-300 !important;
  pointer-events: none;
}

.div-period-total-count {
  display: inline-block;
  margin-top: -35px;
}

.div-period-total-count span {
  background-color: #475569;
  color: #f3f4f6;
  padding: 10px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 25px;
  height: 25px;
  text-align: center;
  font-size: 12px;
}

.div-period-total-count-red {
  display: inline-block;
  margin-top: -35px;
}

.div-period-total-count-red span {
  background-color: $color-red-tertiary-600;
  color: $color-white-0;
  padding: 10px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 25px;
  height: 25px;
  text-align: center;
  font-size: 12px;
}
