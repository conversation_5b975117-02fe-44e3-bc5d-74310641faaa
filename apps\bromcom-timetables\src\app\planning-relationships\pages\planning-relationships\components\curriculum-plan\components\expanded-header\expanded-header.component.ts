import { ChangeDetectorRef, Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AsyncBlockNameValidator } from "../../../../validators/async-block-name-validator";
import { BLOCK_TYPE_SHORT } from "../../../../../../../_shared/enums/BlockTypeShort";
import { FormControl, Validators } from "@angular/forms";
import { ICurriculumPlanBlock } from "../../../../../../../_shared/models/ICurriculumPlanBlock";
import { CurriculumPlanService } from "../../../../../../services/curriculum-plan.service";
import { CurriculumPlanBlocksService } from "../../../../../../services/curriculum-plan-blocks";
import { TranslateService } from "@ngx-translate/core";
import { SnackbarService } from "@bromcom/ui";
import { BaseBlockSimpleViewActions } from '../../_shared/base-block-simple-view-actions';
import { Subject, takeUntil } from 'rxjs';
import { InformationService } from '../../../../../../services/information.service';
import { PlanningRelationshipsService } from '../../../../../../services/planning-relationships.service';
import { BandService } from '../../../../../../services/band.service';
import { RelationshipsService } from '../../../../../../services/relationships.service';
import { getLinkedBlockName } from '../../_shared/linkedBlockName';

@Component({
  selector: 'bromcom-expanded-header',
  templateUrl: './expanded-header.component.html',
  styleUrls: ['./expanded-header.component.scss']
})
export class ExpandedHeaderComponent extends BaseBlockSimpleViewActions implements OnInit, OnDestroy {
  @Input() selectedBlock: ICurriculumPlanBlock | null | undefined;
  @Input() timetableId!: number;
  @Input() selectedYearGroupId!: number | null | undefined;

  BLOCK_TYPE = BLOCK_TYPE_SHORT;
  editBlockCode = false;
  editBlockName = false;
  openBlockActionsMenu$ = new Subject<number>();
  bandNames = '';

  editCode = new FormControl('',
    [
      Validators.required,
      Validators.maxLength(3)
    ]
  );

  editName = new FormControl('',
    [
      Validators.required,
      Validators.maxLength(20)
    ]
  );
  readonly unsubscribe$: Subject<void> = new Subject();

  constructor(
    private planningRelationshipsService: PlanningRelationshipsService,
    private curriculumPlanService: CurriculumPlanService,
    protected override curriculumPlanBlocksService: CurriculumPlanBlocksService,
    private bandService: BandService,
    private translateService: TranslateService,
    protected override snackbarService: SnackbarService,
    private changeDetectorRef: ChangeDetectorRef,
    protected override informationService: InformationService,
    private relationships: RelationshipsService
  ) {
    super(curriculumPlanService, curriculumPlanBlocksService, snackbarService, translateService, informationService);
  }

  ngOnInit(): void {
    this.curriculumPlanService.currentSelectedBlock$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((block) => {
        this.selectedBlock = block;
        this.changeDetectorRef.detectChanges();
      })

    if (this.selectedBlock && this.selectedBlock.bandIds.length > 1) {
      const selectedYearGroupName = this.planningRelationshipsService.yearGroupsData$
        .getValue()?.find(yearGroup => yearGroup.id === this.curriculumPlan.selectedYearGroup$.getValue())?.description;
      const bands = this.bandService.fetchedBands$.getValue();
      this.bandNames = selectedYearGroupName + this.selectedBlock.bandIds.map(bandId => {
        const bandName = bands?.find(band => band.id === bandId)?.bandName;
        return `${bandName}`
      }).join('');
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  openBlockActionsMenu(): void {
    if (this.selectedBlock?.id) {
      this.openBlockActionsMenu$.next(this.selectedBlock.id);
    }
  }

  onEditBlockCode(): void {
    this.editCode.setValue(this.selectedBlock?.blockCode.toString() || '');
    this.editBlockCode = true;
  }

  onEditBlockName(): void {
    if (this.selectedBlock && this.selectedYearGroupId) {
      this.editName.setValue(this.selectedBlock?.blockName.toString() || '');
      this.editName.addAsyncValidators([
        AsyncBlockNameValidator.createValidator(
          this.curriculumPlanService,
          this.translateService,
          'There is a Block name with the same name. This has to be unique.',
          this.selectedYearGroupId,
          this.timetableId,
          this.selectedBlock.id)
      ]);
      this.editBlockName = true;
    }
  }

  saveCode(): void {
    const id = this.selectedBlock?.id;
    if (id) {
      this.curriculumPlanService
        .updateBlock(id, { blockCode: this.editCode.value as string })
        .subscribe({
          next: () => {
            this.snackbarService.saved();

            if (this.selectedBlock?.blockCode) {
              this.selectedBlock.blockCode = this.editCode.value as string;
            }

            this.curriculumPlanBlocksService.blockSideStateChanged$.next();
            this.editBlockCode = false;
          },
          error: () => {
            this.snackbarService.error(this.translateService.instant('Block code is not unique'));
          }
        });
    }
  }

  saveName(): void {
    const id = this.selectedBlock?.id;
    if (id) {
      this.curriculumPlanService
        .updateBlock(id, { blockName: this.editName.value as string })
        .subscribe({
          next: () => {
            this.snackbarService.saved();

            if (this.selectedBlock?.blockName) {
              this.selectedBlock.blockName = this.editName.value as string;
            }

            this.curriculumPlanBlocksService.blockSideStateChanged$.next();
            this.curriculumPlanBlocks.stateChanged$.next();
            this.editBlockName = false;
          }, error: (err) => {
            const blockNameError = err.error.validationErrors.find((error: any) => error.propertyName === 'BlockName');
            if (blockNameError) {
              this.snackbarService.error(this.translateService.instant(blockNameError.errorMessage));
            } else {
              this.snackbarService.error(this.translateService.instant('Block name is not unique'));
            }
          }
        });
    }
  }

  cancelCode(): void {
    this.editBlockCode = false;
  }

  cancelName(): void {
    this.editBlockName = false;
  }

  compressBlock() {
    this.relationships.staffToSessionActiveId$.next(null);
    this.relationships.roomToSessionActiveId$.next(null);

    this.curriculumPlanService.isExpanded$.next({ isExpanded: false, sessionId: null });
    this.curriculumPlanBlocksService.missingStaffRoomSessionId$.next(null);
  }

  getLinkedBlockName(): void {
    getLinkedBlockName(this.selectedBlock!, this.linkBlocksService, this.linkedBlockName$);
  }
}
