import { ICellRendererComp, ICellRendererParams } from 'ag-grid-community';

export class SubjectsCellRenderer implements ICellRendererComp {
  eGui: HTMLSpanElement;
  value: any;
  subjects!: string[];
  showTooltip = false;
  isAllSubjects = false;

  constructor() {
    this.eGui = document.createElement('div');
  }

  init(params: any) {
    this.value = params.value;
    this.subjects = params.subjects;
    this.showTooltip = params.showTooltip;
    this.isAllSubjects = params.value == "All subjects";
    this.updateSubjects();
  }

  updateSubjects(): void {
    this.eGui.style.width = '100%';
    let innerHTML = ''
    if(this.isAllSubjects){
      const element = `<bcm-chip color="blue" style="margin-right: 8px">All subjects</bcm-chip>`
      innerHTML += element;
    }
    else{
      this.subjects.forEach((subject) => {
        const element = `<bcm-chip color="blue" style="margin-right: 8px">${subject}</bcm-chip>`
        innerHTML += element;
      });
    }
    if (this.showTooltip) {
      this.eGui.innerHTML = `<div style="width: 100%; display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap"><bcm-tooltip style="width: 100%; display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap" message="${this.subjects.join(', ')}" 
      trigger="hover" placement="bottom">${innerHTML}</bcm-tooltip></div>`;
    } else {
      this.eGui.innerHTML = `<div style="width: 100%; display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">${innerHTML}</div>`;
    }
  }

  getGui() {
    return this.eGui;
  }

  refresh(params: ICellRendererParams) {
    this.value = params.value;

    this.eGui.innerHTML = '';
    this.updateSubjects();

    return true;
  }
}
