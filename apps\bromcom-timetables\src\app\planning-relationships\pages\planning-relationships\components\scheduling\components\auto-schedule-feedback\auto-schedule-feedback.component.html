<div class="auto-schedule-feedback-container">
  <div class="action-bar">
    <bromcom-input-field class="search-field"
                         [formControl]="searchControl"
                         [icon]="'fal fa-search'"
                         [placeholder]="'Search' | translate "
    ></bromcom-input-field>
  </div>

  <div class="table-container">
    <ag-grid-angular style="width: 100%; height:100%;"
                     class="ag-theme-alpine"
                     domLayout="normal"
                     [gridOptions]="gridOptions"
                     (gridReady)="onGridReady($event)">
    </ag-grid-angular>
  </div>
</div>

<ng-container *ngIf="isOpen">
  <bcm-modal *ngIf="block"
             size="{{block.periodCount && block.periodCount < 7
                ? 'medium'
                : block.periodCount && block.periodCount < 10
                  ? 'large'
                  : 'xxxlarge'}}"
             class="auto-schedule-feedback-expanded-modal"
             (bcm-modal-before-close)="onClose()"
             #autoScheduleFeedbackBlockPeriodsModal>
    <bcm-modal-header>
      <div *ngIf="block">{{block.blockName}}</div>
    </bcm-modal-header>

    <div class="modal-body">
      <div class="expanded-container">
        <bromcom-expanded-simple-feedback *ngIf="block?.blockTypeId === 1"
                                          [block]="block"
                                          [projectId]="projectId"></bromcom-expanded-simple-feedback>

        <bromcom-expanded-linear-feedback *ngIf="block?.blockTypeId === 2"
                                          [block]="block"
                                          [projectId]="projectId"></bromcom-expanded-linear-feedback>

        <bromcom-expanded-option-feedback *ngIf="block?.blockTypeId === 3"
                                          [block]="block"
                                          [projectId]="projectId"></bromcom-expanded-option-feedback>

        <bromcom-expanded-complex-feedback *ngIf="block?.blockTypeId === 4"
                                           [block]="block"
                                           [projectId]="projectId"></bromcom-expanded-complex-feedback>
      </div>
    </div>
  </bcm-modal>
</ng-container>
