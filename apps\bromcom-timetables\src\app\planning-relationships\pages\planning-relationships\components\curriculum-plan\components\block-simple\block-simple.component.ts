import { Component } from '@angular/core';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { CurriculumPlanBlocksService } from '../../../../../../services/curriculum-plan-blocks';
import { CurriculumPlanService } from "../../../../../../services/curriculum-plan.service";
import { SnackbarService } from '@bromcom/ui';
import { TranslateService } from '@ngx-translate/core';
import { ICurriculumSessions } from '../../../../../../../_shared/models/ICurriculumSessions';
import { BaseBlockSimpleView } from '../../_shared/base-block-simple-view';
import { BandService } from '../../../../../../services/band.service';
import { PlanningRelationshipsService } from '../../../../../../services/planning-relationships.service';
import { RelationshipsService } from '../../../../../../services/relationships.service';
import { InformationService } from '../../../../../../services/information.service';

@Component({
  selector: 'bromcom-block-simple',
  templateUrl: './block-simple.component.html',
  styleUrls: ['./block-simple.component.scss']
})
export class BlockSimpleComponent extends BaseBlockSimpleView {
  groupedSessions: ICurriculumSessions[][] = [];
  transformedSessions: ICurriculumSessions[] = [];

  constructor(
    protected override curriculumPlan: CurriculumPlanService,
    protected override curriculumPlanBlocks: CurriculumPlanBlocksService,
    protected override snackbar: SnackbarService,
    protected override translate: TranslateService,
    protected override band: BandService,
    protected override planningRelationships: PlanningRelationshipsService,
    public override relationshipsService: RelationshipsService,
    protected override informationService: InformationService
  ) {
    super(curriculumPlan, curriculumPlanBlocks, snackbar, translate, band, planningRelationships, relationshipsService, informationService)
  }

  formatSessions(): void {
    const groupedSessions = this.block.sessions
      .reduce((groups: { [key: string]: ICurriculumSessions[] }, session: ICurriculumSessions) => {
        const className = session.className;
        if (groups[className]) {
          groups[className].push(session);
        } else {
          groups[className] = [session];
        }
        return groups;
      }, {});

    this.groupedSessions = Object.values(groupedSessions);
    this.transformedSessions = this.groupedSessions.map(this.transformSessions);

    const droppableRoomIds = this.transformedSessions.map(session => 'room' + session.id.toString());
    const droppableStaffIds = this.transformedSessions.map(session => 'staff' + session.id.toString());
    this.curriculumPlanBlocks.droppableRoomPlaceIds$.next([...this.curriculumPlanBlocks.droppableRoomPlaceIds$.getValue(), ...droppableRoomIds]);
    this.curriculumPlanBlocks.droppableStaffPlaceIds$.next([...this.curriculumPlanBlocks.droppableStaffPlaceIds$.getValue(), ...droppableStaffIds]);
  }

  dropSubject(event: CdkDragDrop<any>) {
    if (!event.isPointerOverContainer) {
      return;
    }

    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      this.addSubjectToBlock.emit({
        ...event.item.data,
        currentIndex: event.currentIndex,
        listIndex: this.listIndex
      });
    }
  }

  dropStaff(event: CdkDragDrop<any>) {
    const session = this.transformedSessions.find(session => session.id.toString() === event.container.id.replace('staff', ''))
    if (session) {
      this.handleDropStaff(event, session);
    }
  }

  dropRoom(event: CdkDragDrop<any>) {
    const session = this.transformedSessions.find(session => session.id.toString() === event.container.id.replace('room', ''))
    if (session) {
      this.handleDropRoom(event, session);
    }
  }
}
