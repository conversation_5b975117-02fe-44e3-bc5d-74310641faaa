import { ChangeDetectorRef, Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { GridOptions, GridReadyEvent } from 'ag-grid-community';
import { GridApi } from 'ag-grid-community/dist/lib/gridApi';
import { gridOptions } from './auto-schedule-feedback-gridoptions';
import { TranslateService } from '@ngx-translate/core';
import { BaseModalComponent } from '../../../../../../../_shared/components/BaseModalComponent';
import { CurriculumPlanService } from '../../../../../../services/curriculum-plan.service';
import { ICurriculumPlanBlock } from '../../../../../../../_shared/models/ICurriculumPlanBlock';
import { AutoScheduleService } from '../../../../../../services/auto-schedule.service';

@Component({
  selector: 'bromcom-auto-schedule-feedback',
  templateUrl: './auto-schedule-feedback.component.html',
  styleUrls: ['./auto-schedule-feedback.component.scss']
})
export class AutoScheduleFeedbackComponent extends BaseModalComponent implements OnInit, OnDestroy {
  @ViewChild('autoScheduleFeedbackBlockPeriodsModal') autoScheduleFeedbackBlockPeriodsModal!: ElementRef;
  @Input() projectId!: number;
  @Input() timetableId!: number;

  params!: GridReadyEvent;
  gridApi!: GridApi;
  gridOptions!: GridOptions;
  gridData: any[] = [];
  searchControl = new FormControl(null);
  block!: ICurriculumPlanBlock;
  readonly unsubscribe$: Subject<void> = new Subject();

  constructor(
    private curriculumPlanService: CurriculumPlanService,
    private autoScheduleService: AutoScheduleService,
    protected translate: TranslateService,
    private cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.searchControl.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(searchValue => {
        this.gridApi.setQuickFilter(searchValue ?? '');
      })

    this.gridOptions = gridOptions.call(this);
    this.autoScheduleService.getFeedbacks(this.timetableId)
      .subscribe(res => {
        this.gridData = res;
        this.setRowData();
      })
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  onGridReady(params: GridReadyEvent): void {
    this.params = params;
    this.gridApi = params.api;
    this.gridApi.setDomLayout('normal');
  }

  setRowData(): void {
    this.gridApi?.setRowData(this.gridData);
  }

  openExpandedView(blockId: number): void {
    this.isOpen = true;
    this.autoScheduleService.getFeedbacksBlock(this.timetableId, blockId).subscribe(res => {
      this.block = res;
      this.cdr.detectChanges();
      setTimeout(() => {
        this.autoScheduleFeedbackBlockPeriodsModal.nativeElement.show();
        this.cdr.detectChanges();
      }, 50)
    })
  }
}
