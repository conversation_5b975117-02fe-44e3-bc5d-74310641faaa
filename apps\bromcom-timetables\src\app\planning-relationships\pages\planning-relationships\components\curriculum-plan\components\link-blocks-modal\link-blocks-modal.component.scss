@import '../../../../../../../../assets/styles/variables';

@mixin period() {
  color: $color-blue-600;

  .period-name {
    width: 96px !important;
    background-color: $color-white-0;
    border-bottom: 1px solid $color-blue-200;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
    border-top: 1px solid $color-blue-grey-200;
    border-right: 1px solid $color-blue-grey-200;
    border-left: 1px solid $color-blue-grey-200;
  }

  .subject {
    background-color: $color-white-0;
    border-bottom: 1px solid $color-blue-grey-200;
    border-right: 1px solid $color-blue-grey-200;
    border-left: 1px solid $color-blue-grey-200;
  }

  .subject:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  .row {
    height: 24px;
    display: block;
    text-align: center;
    font-size: 13px;
    font-weight: 500;
    width: 94px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &.border-top {
      border-top: 1px solid $color-blue-200;
    }

    &.white-color {
      color: $color-white-0;
    }
  }

  &.isSingle {
    min-width: 96px;
    width: 96px;
    margin: 4px 12px 4px 4px;
    background-color: transparent;
  }

  &.isDouble {
    .double {
      display: flex;

      .isDoubleLeft {
        min-width: 108px;
        width: 108px;
        padding: 4px 8px 4px 4px;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        background-color: $color-blue-grey-300;
      }

      .isDoubleRight {
        min-width: 108px;
        width: 108px;
        padding: 4px 4px 4px 8px;
        margin: 0 8px 0 0;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        background-color: $color-blue-grey-300;
      }
    }
  }
}

.link-blocks-modal {
  &.bcm-modal__show {
    z-index: 10;
  }

  .modal-body-step-one {
    color: $color-blue-600;
    padding: 24px;

    .actions {
      width: 256px;
    }

    .table-header,
    .table-footer {
      height: 32px;
      background-color: $color-blue-grey-200;
    }

    .table-header {
      border-radius: 4px 4px 0 0;
    }

    .table-footer {
      border-radius: 0 0 4px 4px;
    }

    .table-center {
      border: 1px solid $color-blue-grey-200;
      max-height: 50vh;
      overflow: auto;

      .no-data {
        min-height: 160px;
      }
    }

    .tree-invisible {
      display: none;
    }

    .tree ul,
    .tree li {
      margin-top: 0;
      margin-bottom: 0;
      list-style-type: none;
    }

    .tree .mat-nested-tree-node div[role=group] {
      padding-left: 16px;
    }

    .tree div[role=group] > .mat-tree-node {
      padding-left: 16px;

      &:hover {
        background-color: $color-blue-300;
        cursor: pointer;
      }
    }

    .mat-tree-node {
      min-height: 32px !important;
      border-bottom: 1px solid $color-blue-grey-200;

      &.disabled {
        color: $color-blue-grey-300;
        pointer-events: none;
      }

      &.selected {
        background-color: $color-blue-300;
      }
    }

    .expand-icons {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      cursor: pointer;
    }
  }

  .modal-body-step-two {
    color: $color-blue-600;
    padding: 24px;

    .information-box {
      min-height: 54px;
      padding: 16px 24px;
      background-color: $color-blue-50;
      color: $color-primary-blue-600;
      display: flex;
      align-items: center;
      border-radius: 8px;
      margin-bottom: 24px;

      .information-icon {
        margin-right: 16px;
      }
    }

    .block-names {
      display: flex;
      margin-bottom: 8px;
      width: calc(66% - 17px);

      .icon {
        margin: 11px 16px;
      }

      .block-name {
        width: 50%;

        .bcm-input {
          margin-bottom: 0;
        }
      }
    }

    .drag-to-match {
      margin-bottom: 8px;
      display: flex;

      .left {
        width: 66%;
      }

      .right {
        padding: 0 7px;
      }

    }

    .drag-containers {
      display: flex;

      .drag-area-container {
        background-color: $color-blue-grey-100;
        padding: 24px;
        border-radius: 8px;
        width: 66%;
        margin-right: 24px;

        .drag-area {
          overflow: auto;

          .container {
            display: flex;

            .period {
              @include period;
            }
          }
        }
      }

      .drag-area-exclude-container {
        background-color: $color-blue-grey-100;
        padding: 24px;
        border-radius: 8px;
        width: 34%;

        .drag-area {
          overflow: auto;
          height: 100%;

          .container {
            display: flex;
            height: 100%;

            .period {
              @include period;
              height: fit-content;
            }
          }
        }
      }
    }
  }
}

.check-container {
  margin: 24px 0 24px 4px;
  display: flex;

  .check-box {
    min-width: 96px;
    width: 96px;
    height: 32px;
    padding: 8px 10px;
    border-radius: 4px;
    background-color: $color-white-0;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 16px 0 0;

    .icon {
      &.check {
        color: $color-emerald-500;
      }

      &.times {
        color: $color-red-tertiary-500;
      }
    }
  }
}

.cdk-drag-preview {
  z-index: 12000;

  &.period {
    @include period;
  }
}
