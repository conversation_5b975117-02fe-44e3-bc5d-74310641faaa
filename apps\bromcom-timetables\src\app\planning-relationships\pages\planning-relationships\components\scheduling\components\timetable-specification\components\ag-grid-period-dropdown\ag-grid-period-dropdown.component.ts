import {AfterViewInit, ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import { BaseModalComponent } from '../../../../../../../../../_shared/components/BaseModalComponent';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs';

@Component({
  selector: 'bromcom-ag-grid-period-dropdown',
  templateUrl: './ag-grid-period-dropdown.component.html',
  styleUrls: ['./ag-grid-period-dropdown.component.scss']
})
export class AgGridPeriodDropdownComponent extends BaseModalComponent implements OnInit, AfterViewInit {
  @ViewChild('accordion', { static: false }) accordion!: ElementRef;
  params!: any;
  gridApi!: any;
  originalData!: any[];
  data!: any[];
  checkboxes = false;
  value!: string;
  color = '';

  placeholder = '';
  panelOpenState = false;

  searchControl = new FormControl('');
  filterGroup = new FormGroup({
    items: new FormArray<FormControl<boolean | null>>([])
  });
  selectedItemIds: number[] = [];
  selectedItemTexts: string[] = [];

  dynamicStyles: { marginTop: string; marginLeft: string } = { marginTop: '0px', marginLeft: '0px' };

  constructor(private cdr: ChangeDetectorRef) {
    super();
  }

  ngAfterViewInit(): void {
    this.adjustPopupPosition();
  }

  adjustPopupPosition(): void {
    this.cdr.detectChanges();
    setTimeout(() => {}, 0);

    if (!this.accordion) return;

    setTimeout(() => {
      const rect = this.accordion.nativeElement.getBoundingClientRect();
      const screenHeight = window.innerHeight;
      if (rect.top < screenHeight / 2) {
        this.dynamicStyles = {
          marginTop: '0',
          marginLeft: '0',
        };
      } else {
        this.dynamicStyles = {
          marginTop: '-383px',
          marginLeft: '0px',
        };
      }
    });
  }

  agInit(params: any): void {
    this.params = params;
    this.gridApi = params.gridApi;

    this.checkboxes = params.checkboxes ?? false;
    this.originalData = params.values;
    this.data = params.values;
    this.value = params.value;
    this.placeholder = params.placeholder;
    this.selectedItemIds = params.value ?? [];
    this.color = params.color;

    this.initializeFormControls();
    this.onApply();
  }

  initializeFormControls() {
    const itemsArray = this.filterGroup.controls.items as FormArray;
    if (itemsArray && this.originalData?.length) {
      itemsArray.clear();
      this.originalData?.forEach((data) => {
        itemsArray.push(new FormControl(this.selectedItemIds?.includes(data.id)));
      })
    }
  }

  ngOnInit() {
    this.searchControl.valueChanges
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe(async () => {
        this.resetFilter();
        setTimeout(() => {
          this.searchFilter();
        }, 0);
      });
  }

  getValue(): number[] {
    return this.selectedItemIds;
  }

  updateValue(): void {
    if (this.params.node.rowPinned === 'bottom') {
      this.gridApi.getPinnedBottomRow(0).setDataValue([this.params.colDef.field], this.selectedItemIds);
    } else {
      this.gridApi.getRowNode(this.params.data.id).setDataValue([this.params.colDef.field], this.selectedItemIds);
    }
  }

  selectAll(): void {
    this.data.forEach((item) => {
      const index = this.originalData.findIndex((data) => data.id === item.id);
      this.filterGroup.controls.items.controls[index]?.setValue(true);
    });
  }

  clearAll() {
    this.data?.forEach((item) => {
      const index = this.originalData.findIndex((data) => data.id === item.id);
      this.filterGroup.controls.items.controls[index]?.setValue(false);
    });
  }

  resetFilter() {
    this.data = this.originalData;
  }

  searchFilter() {
    const searchTerm = this.searchControl?.value?.toLowerCase();
    this.data = searchTerm ? this.originalData.filter(
      (item) => item.text.toLowerCase().includes(searchTerm)
    ) : this.originalData;
  }

  onApply() {
    const selectedItemIds: number[] = [];
    const selectedItemTexts: string[] = [];
    this.filterGroup.getRawValue().items?.forEach((isSelected, index) => {
      if (isSelected) {
        const selectedItem = this.originalData[index];
        selectedItemIds.push(selectedItem.id);
        selectedItemTexts.push(selectedItem.text);
      }
    });
    this.selectedItemIds = selectedItemIds;
    this.selectedItemTexts = selectedItemTexts;

    this.searchControl.setValue('');
    this.updateValue();
    this.panelOpenState = false
  }

  onCancel() {
    this.panelOpenState = false;
    this.clearAll();
    this.searchControl.setValue('');
    this.initializeFormControls();
  }
}
