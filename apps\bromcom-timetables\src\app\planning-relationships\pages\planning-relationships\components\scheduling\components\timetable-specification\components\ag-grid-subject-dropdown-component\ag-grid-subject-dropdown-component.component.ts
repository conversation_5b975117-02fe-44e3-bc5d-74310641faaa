import { Component, ElementRef, ViewChild } from '@angular/core';
import { ITTSSubjectOnDays } from '../../../../../../../../../_shared/models/ITTSSubjectOnDays';
import { ISubject } from '../../../../../../../../../_shared/models/ISubject';
import { CellValueChangedEvent } from 'ag-grid-community';

@Component({
  selector: 'bromcom-ag-grid-subject-dropdown-component',
  templateUrl: './ag-grid-subject-dropdown-component.component.html',
  styleUrls: ['./ag-grid-subject-dropdown-component.component.scss']
})
export class AgGridSubjectDropdownComponentComponent {
  @ViewChild('agGridSubjectSelectDropdown') agGridSubjectSelectDropdown: ElementRef = {} as ElementRef;
  params!: any;
  gridApi!: any;
  data!: any[];
  checkboxes = false;
  value!: number;
  allowDisableSubjectOptions = false;
  gridData: ITTSSubjectOnDays[] = [];
  values: Partial<ISubject>[] = [];
  paramsData!: ITTSSubjectOnDays;
  placeholder = '';
  template = `<div style="display: flex; align-items: center; justify-content: space-between; width: 100%">
                <span style="display: flex; align-items: center; justify-content: flex-start; width: 100%">
                  <div *ngIf="listWithCircle"
                     class="custom-template">
                      <span style="display: flex; align-items: center; justify-content: flex-start; width: 100%">
                      <div>
                          <span class="list-circle" style="background-color: {{color}}"></span>
                      </div>
                      <span>{{text}}</span>
                      </span>
                  </div>
                </span>
             </div>`;

  agInit(params: any): void {
    this.params = params;
    this.gridApi = params.gridApi;
    this.allowDisableSubjectOptions = params.allowDisableSubjectOptions;
    this.gridData = params.gridData;
    this.values = params.values;
    this.paramsData = params.data;

    this.data = params.values.map((value: Partial<ISubject>) => ({ ...value, disabled: false }));

    this.checkboxes = params.checkboxes ?? false;
    this.value = params.value;
    this.placeholder = params.placeholder;

    this.setData({ colDef: { field: 'yearGroupIds' } });
    this.gridApi.addEventListener('cellValueChanged', this.setData.bind(this))

  }

  ngAfterViewInit(): void {
    this.agGridSubjectSelectDropdown.nativeElement.set(this.value)
  }

  setData(params?: Partial<CellValueChangedEvent>): void {
    if (this.allowDisableSubjectOptions && params?.colDef?.field === 'yearGroupIds') {
      // Collect all occurred year groups to all subjects in an object. Key is subjectId, value yearGroupId[]
      const subjectIds = this.gridData.reduce((acc: { [key: string]: number[] }, data: ITTSSubjectOnDays) => {
        data.subjectId
        const subjectId = data.subjectId;
        if (acc[subjectId]) {
          acc[subjectId] = Array.from(new Set([...acc[subjectId], ...data.yearGroupIds]));
        } else {
          acc[subjectId] = data.yearGroupIds;
        }
        return acc;
      }, {});

      // Set disabled state if we select an already used YG for an already added subject.
      this.data = (this.data ? this.data : this.values).map((value: Partial<ISubject>) => {
        return {
          ...value,
          disabled: subjectIds[value.id as number]?.some((yearGroup: number) => this.paramsData.yearGroupIds?.includes(yearGroup)) ?? false
        }
      })
    }
  }

  getValue(): number {
    return this.value;
  }

  updateValue(): void {
    this.agGridSubjectSelectDropdown.nativeElement.get().then((data: number) => {
      this.value = data;

      if (this.params.node.rowPinned === 'bottom') {
        this.gridApi.getPinnedBottomRow(0).setDataValue([this.params.colDef.field], data);
      } else {
        this.gridApi.getRowNode(this.params.data.id).setDataValue([this.params.colDef.field], data);
      }
    })
  }
}
