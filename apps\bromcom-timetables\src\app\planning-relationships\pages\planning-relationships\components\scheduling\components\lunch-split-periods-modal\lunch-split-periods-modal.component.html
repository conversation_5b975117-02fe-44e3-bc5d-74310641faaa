<ng-container *ngIf="isOpen">
  <bcm-modal size="xxlarge" backdrop class="lunch-split-periods-modal"
             (bcm-modal-before-close)="onClose()"
             #lunchSplitPeriodsModal>
    <bcm-modal-header>{{ 'Lunch/Split Periods' | translate }}</bcm-modal-header>

    <div class="tw-p-4 body">
      <div class="info-msg">
        <label class="lbl-info">
          <i class="far fa-info-circle" aria-hidden="true"></i>
          <span
            class="spn-info">{{alertText}}</span>
        </label>
      </div>

      <div class="period-containers">
        <div *ngFor="let period of splitLunchData" class="period">
          <div class="header">{{ period.periodName }}</div>

          <div
            class="body"
            cdkDropList
            [id]="period.periodName"
            [cdkDropListData]="period.yearGroups"
            [cdkDropListConnectedTo]="periodNames"
            (cdkDropListDropped)="drop($event)"
            (cdkDropListEntered)="onEnter(period.periodName)"
            (cdkDropListExited)="onExit(period.periodName)">
            <bcm-empty *ngIf="!period.yearGroups.length && hoveredPeriod !== period.periodName" class="no-data"
                       icon="fad fa-list">
              <div>
                <div class="no-data-header">{{ 'Drag Year Groups Here.' | translate }}</div>
                <div>{{ 'No Data Available.' | translate }}</div>
              </div>
            </bcm-empty>

            <div
              *ngFor="let card of period.yearGroups"
              class="card"
              cdkDrag>
              <img src="assets/images/drag-icon.png" alt="">
              {{ 'YG' | translate }} {{ card.yearGroupName }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <bcm-modal-footer class="footer">
      <bcm-button icon="far fa-times" data-dismiss kind="ghost">{{'Cancel' | translate}}</bcm-button>
      <bcm-button icon="far fa-save" [disabled]="isSaveDisabled" (click)="onSaveSplitLunchClicked()">{{'Save' | translate }}</bcm-button>
    </bcm-modal-footer>
  </bcm-modal>
</ng-container>

<bromcom-general-modal #saveSplitLunchModal
                       icon="far fa-exclamation-triangle"
                       type="warningAmber"
                       [header]="'Are you sure you want to apply these changes?' | translate"
                       [cancelText]="'Cancel' | translate"
                       [nextText]="'Confirm' | translate"
                       [description]="'Please note that modifying lunch configurations for Year Groups will automatically remove all scheduled sessions within the affected lunch periods.' | translate"
                       (nextEvent)="onSaveSplitLunch()">
</bromcom-general-modal>
