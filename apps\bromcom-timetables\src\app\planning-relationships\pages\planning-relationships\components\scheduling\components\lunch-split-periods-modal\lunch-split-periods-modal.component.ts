import { CdkDragDrop, transferArrayItem } from '@angular/cdk/drag-drop';
import {Component, ElementRef, EventEmitter, Output, ViewChild} from '@angular/core';
import { SnackbarService } from '@bromcom/ui';
import { TranslateService } from '@ngx-translate/core';
import { BaseModalComponent } from '../../../../../../../_shared/components/BaseModalComponent';
import { GeneralModalComponent } from '../../../../../../../_shared/components/general-modal/general-modal.component';
import { IListOfBlocksResponse } from '../../../../../../../_shared/models/IListOfBlocksResponse';
import { SplitLunchPeriod } from '../../../../../../../_shared/models/ISplitLunch';
import { LunchPeriodService } from '../../../../../../services/lunch-period.service';
import { SchedulingService } from '../../../../../../services/scheduling.service';

@Component({
  selector: 'bromcom-lunch-split-periods-modal',
  templateUrl: './lunch-split-periods-modal.component.html',
  styleUrls: ['./lunch-split-periods-modal.component.scss']
})
export class LunchSplitPeriodsModalComponent extends BaseModalComponent {
  @ViewChild('lunchSplitPeriodsModal') lunchSplitPeriodsModalRef!: ElementRef;
  @ViewChild('saveSplitLunchModal') saveSplitLunchModal!: GeneralModalComponent;
  @Output() updateBlockLayout = new EventEmitter<IListOfBlocksResponse>();
  timetableId!: number;
  splitLunchData: SplitLunchPeriod[] = [];
  periodNames: string[] = [];
  hoveredPeriod: string | null = null;
  alertText = '';
  isSaveDisabled = true;

  onEnter(period: string) {
    this.hoveredPeriod = period;
  }

  onExit(period: string) {
    if (this.hoveredPeriod === period) {
      this.hoveredPeriod = null;
    }
  }

  constructor(
    private lunchPeriodService: LunchPeriodService,
    private schedulingService: SchedulingService,
    private translate: TranslateService,
    private snackbar: SnackbarService,
  ) {
    super();
  }

  show(timetableId: number): void {
    this.isSaveDisabled = true;
    this.timetableId = timetableId;
    this.lunchPeriodService.getSplitLunchYearGroups(timetableId).subscribe(res => {
      this.splitLunchData = res;
      this.splitLunchData.forEach(period => this.sortByYearGroupNamePriority(period.yearGroups));
      this.periodNames = res.map(period => period.periodName);
      this.alertText = this.translate.instant(
        'This timetable\'s period structure is configured for split lunch containing the periods below and cannot be deleted. Drag the year groups to change the desired period from the default setting.');
      this.isOpen = true;
      setTimeout(() => {
        this.lunchSplitPeriodsModalRef.nativeElement.show();
      }, 100);
    })
  }

  onSaveSplitLunchClicked(): void {
    this.saveSplitLunchModal.show();
  }

  onSaveSplitLunch(): void {
    this.saveSplitLunchModal.hide();
    this.lunchPeriodService.postSplitLunchYearGroups(this.timetableId, this.splitLunchData)
      .subscribe(({sessionIds}) => {
        let blockData = JSON.parse(JSON.stringify(this.schedulingService.fetchedBlocks$.getValue())) as IListOfBlocksResponse[];
        blockData = blockData?.map((block) => {
          return {
            ...block,
            sessions: block.sessions?.map((s) => {
              return (sessionIds).includes(s.id) ? {
                ...s,
                periodId: null,
                isLocked: false
              } : s;
            })
          };
        });
        this.updateBlockLayout.emit(undefined);
        this.schedulingService.fetchedBlocks$.next(blockData);

        this.schedulingService.nccChanges$.next();
        this.snackbar.success(this.translate.instant('Saved successfully'));
      });
  }

  drop(event: CdkDragDrop<any[]>) {
    if (event.previousContainer === event.container) return;

    transferArrayItem(
      event.previousContainer.data,
      event.container.data,
      event.previousIndex,
      event.currentIndex
    );

    this.sortByYearGroupNamePriority(event.previousContainer.data);
    this.sortByYearGroupNamePriority(event.container.data);
    this.isSaveDisabled = false;
  }

  sortByYearGroupNamePriority = (items: any[]) => {
    return items.sort((a, b) => {
      const getPriority = (value: string): [number, number | string, string] => {
        const trimmed = value?.trim() || '';
        const firstChar = trimmed.charAt(0);

        if (!/[a-zA-Z0-9]/.test(firstChar)) {
          return [0, 0, trimmed];
        }

        const numberMatch = trimmed.match(/^(\d+)/);
        if (numberMatch) {
          const num = parseInt(numberMatch[1], 10);
          return [1, -num, trimmed];
        }

        return [2, trimmed.toLowerCase(), trimmed];
      };

      const [prioA, subA, valA] = getPriority(a.yearGroupName);
      const [prioB, subB, valB] = getPriority(b.yearGroupName);

      if (prioA !== prioB) return prioA - prioB;

      if (typeof subA === 'number' && typeof subB === 'number') {
        return subA - subB;
      }

      if (typeof subA === 'string' && typeof subB === 'string') {
        return subA.localeCompare(subB);
      }

      return valA.localeCompare(valB);
    });
  };
}
