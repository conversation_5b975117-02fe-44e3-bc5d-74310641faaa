import { IListOption, NOOP, transformToAGGridConfig } from '@bromcom/core';
import { AgGridNoDataComponent } from '@bromcom/ui';
import {
  CheckboxSelectionCallbackParams,
  ICellEditorParams,
  ICellRendererParams,
  IsFullWidthRowParams
} from 'ag-grid-community';
import {
  AddNewRowComponent
} from '../../../../../../../../../projects/pages/new-project-wizard/components/add-new-row/add-new-row.component';
import { TtsGridActionsComponent } from '../tts-grid-actions/tts-grid-actions.component';
import { TtsBlocksOnDaysComponent } from './tts-blocks-on-days.component';
import { AgGridSelectDropdownComponent } from '../ag-grid-select-dropdown/ag-grid-select-dropdown.component';
import {
  AgGridYearGroupSelectDropdownComponent
} from '../ag-grid-year-group-select-dropdown/ag-grid-year-group-select-dropdown.component';
import {
  AgGridYearGroupsMultiselectComponent
} from '../ag-grid-year-groups-multiselect/ag-grid-year-groups-multiselect.component';
import { BlocksCellRenderer } from '../../cell-renderers/blocks-cell-renderer';

export function gridOptions(this: TtsBlocksOnDaysComponent, config: any) {
  const {
    onAddNewRow = NOOP,
    onAcceptNewRow = NOOP,
    onCancelAddingNewRow = NOOP,
    onEditRow = NOOP,
    onDeleteRow = NOOP,
    onAcceptRow = NOOP,
    onCancelEditRow = NOOP,
    onExcludeRow = NOOP,
    onIncludeRow = NOOP
  } = config;

  return transformToAGGridConfig({
    getRowId: (params) => params.data.id,
    animateRows: false,
    suppressRowClickSelection: true,
    suppressClickEdit: true,
    suppressCellFocus: true,
    domLayout: 'autoHeight',
    editType: 'fullRow',
    rowSelection: 'multiple',
    noRowsOverlayComponent: AgGridNoDataComponent,
    noRowsOverlayComponentParams: {
      style:
        'display: flex; justify-content: center; align-items: center; height: 100%; width: 100%; min-height: 162px; padding: 48px 0 0 0',
      noRowsMessageFunc: () => this.translate.instant(`No data available! No Blocks on days were found.`)
    },
    pinnedBottomRowData: [{}],
    fullWidthCellRenderer: AddNewRowComponent,
    fullWidthCellRendererParams: {
      onAddNewRow,
      label: this.translate.instant('Add New Row'),
      message: this.translate.instant('Note, that the rule will apply for one subject per class for linear blocks. Other block types will follow the standard.')
    },
    tooltipShowDelay: 500,
    isFullWidthRow: (params: IsFullWidthRowParams) => {
      return !params.rowNode.data.id;
    },
    onSelectionChanged: () => {
      this.isRemoveBulkDisabled = !this.gridApi.getSelectedRows().length;
    },
    rowHeight: 56,
    columnDefs: [
      {
        minWidth: 48,
        width: 48,
        headerCheckboxSelection: true,
        checkboxSelection: (params: CheckboxSelectionCallbackParams<IListOption>) => {
          return !!params.data;
        }
      },
      {
        field: 'yearGroupId',
        headerName: this.translate.instant('Year Group'),
        maxWidth: 250,
        flex: 1,
        editable: true,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: (params) =>
          this.yearGroups.find((yearGroup) => yearGroup.id === params.data.yearGroupId)?.name,
        valueGetter: (params) => {
          return this.yearGroups.find((yg) => yg.id === params.data.yearGroupId)?.name;
        },
        cellEditor: AgGridYearGroupSelectDropdownComponent,
        cellEditorParams: (params: ICellEditorParams) => {
          return {
            placeholder: this.translate.instant('Year Group'),
            checkboxes: false,
            values: this.yearGroups,
            value: params.data.yearGroupId,
            valueChanged: (newValue: number) => {
              this.updateBlocksData(newValue);
              setTimeout(() => {
                this.disableBlockIds(newValue)
              }, 100);
            },
            gridApi: this.gridApi,
            allowDisabling: false,
            params
          };
        },
        onCellValueChanged: (params) => {
          if (params.data.id !== "newRowId") {
            const currentDataIndex = this.gridData.findIndex(d => params.data.id === d.id);
            this.gridDataClone[currentDataIndex].yearGroupId = params.data.yearGroupId;
          }
        },
      },
      {
        field: 'blockIds',
        headerName: this.translate.instant('Block'),
        maxWidth: 400,
        flex: 1,
        editable: true,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: (params) =>
          this.blocksData
            .filter((blockData) => params.data.blockIds?.includes(blockData.id))
            .map((blockData) => blockData.blockName)
            .join(', '),
        valueGetter: (params) => {
          return this.blocksData
            .filter((blockData) => params.data.blockIds?.includes(blockData.id))
            .map((blockData) => blockData.blockName)
            .join(', ');
        },
        cellRenderer: BlocksCellRenderer,
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            blocksData: this.blocksData
              .filter((blockData) => params.data.blockIds?.includes(blockData.id))
              .map((blockData) => blockData.blockName)
          };
        },
        cellEditor: AgGridYearGroupsMultiselectComponent,
        cellEditorParams: (params: ICellEditorParams) => {
          return {
            placeholder: this.translate.instant('Blocks'),
            checkboxes: true,
            keyString: 'blockIds',
            values: this.availableBlockData$,
            value: params.data.blockIds,
            gridApi: this.gridApi,
            allowDisabling: true,
            isDisabled: this.disableBlockIds$,
            params
          };
        },
        onCellValueChanged: (params) => {
          if (params.data.id !== "newRowId") {
            const currentDataIndex = this.gridData.findIndex(d => params.data.id === d.id);
            this.gridDataClone[currentDataIndex].blockIds = params.data.blockIds;
          }
        },
        filter: 'agSetColumnFilter'
      },
      {
        field: 'blockOnDaysType',
        headerName: this.translate.instant('Rule'),
        minWidth: 170,
        flex: 1,
        editable: true,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: (params) => this.ruleOptions.find((rule) => rule.id === params.data.blockOnDaysType)?.name,
        valueGetter: (params) => {
          return this.ruleOptions.find((rule) => rule.id === params.data.blockOnDaysType)?.name;
        },
        cellEditor: AgGridSelectDropdownComponent,
        cellEditorParams: (params: ICellEditorParams) => {
          return {
            placeholder: this.translate.instant('Rule'),
            values: this.ruleOptions,
            value: params.data.blockOnDaysType,
            gridApi: this.gridApi,
            params
          };
        },
        onCellValueChanged: (params) => {
          this.disableField(params.newValue);
          if (params.data.id !== "newRowId") {
            const currentDataIndex = this.gridData.findIndex(d => params.data.id === d.id);
            this.gridDataClone[currentDataIndex].blockOnDaysType = params.data.blockOnDaysType;
          }
        },
        cellStyle: {
          fontWeight: '400',
          display: 'block',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }
      },
      {
        field: 'periodLimit',
        headerName: this.translate.instant('Period Limit'),
        maxWidth: 300,
        flex: 1,
        editable: true,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: (params) =>
          this.periodLimitOptions.find((rule) => rule.id === params.data.periodLimit)?.name,
        valueGetter: (params) => {
          return this.periodLimitOptions.find((limit) => limit.id === params.data.periodLimit)?.name;
        },
        cellEditor: AgGridYearGroupSelectDropdownComponent,
        cellEditorParams: (params: ICellEditorParams) => {
          return {
            placeholder: this.translate.instant('Period Limit'),
            values: this.periodLimitOptions,
            value: params.data.periodLimit,
            gridApi: this.gridApi,
            allowDisabling: true,
            isDisabled: this.disableField$,
            params
          };
        },
        cellStyle: {
          fontWeight: '400'
        },
        onCellValueChanged: (params) => {
          if (params.data.id !== "newRowId") {
            const currentDataIndex = this.gridData.findIndex(d => params.data.id === d.id);
            this.gridDataClone[currentDataIndex].periodLimit = params.data.periodLimit;
          }
        },
      },
      {
        field: 'actions',
        headerName: this.translate.instant('Actions'),
        maxWidth: 180,
        filter: false,
        sortable: false,
        menuTabs: [],
        flex: 0.8,
        headerClass: 'text-center',
        cellStyle: { display: 'flex', justifyContent: 'center' },
        cellRenderer: TtsGridActionsComponent,
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            gridId: 'tts-ag-grid',
            type: 'blockOnDays',
            parent: this,
            blockToYearData: this.blocksData.map((block) => ({
              blockId: block.id,
              yearGroupId: block.subjectToYearGroups[0].yearGroupId
            })),
            allowMultipleRuleId: this.ruleOptions.find((rule) => rule.name === this.allowMultipleRule)?.id,
            onAcceptNewRow,
            onCancelAddingNewRow,
            onEditRow,
            onDeleteRow,
            onAcceptRow,
            onCancelEditRow,
            onExcludeRow,
            onIncludeRow
          };
        },
        editable: false
      }
    ]
  });
}
