@use '../../../../../../styles/expanded-detailed-block' as style;
@import '../../../../../../../../assets/styles/variables';

.detailed-container {
  @include style.container;

  .block {
    @include style.block;

    .no-padding {
      padding: 0 !important;
    }

    .title {
      @include style.title;
      flex-direction: column;
      height: 48px;
    }

    .footer {
      @include style.footer;
    }

    table {
      @include style.table;
    }

    .session-td {
      @include style.session-td;
    }

    table, th, tr, td {
      @include style.table-th-tr-td;
    }
  }

  .session-room-icon {
    @include style.session-room-icon;
  }

  .subject-periods {
    display: flex;
    width: 100%;

    .single-period {
      width: 50%;
      text-align: center;
    }

    .double-period {
      width: 50%;
      text-align: center;
    }
  }

  .disable .icon, .disable {
    color: $color-blue-grey-300 !important;
    pointer-events: none;
  }

  .white-color {
    color: $color-white-0 !important;
  }

  .class-name.dashed,
  .staff-side.dashed,
  .room-side.dashed {
    border: 2px dashed $color-blue-800 !important;

    &:hover {
      background-color: $color-blue-grey-300 !important;
    }
  }

  .room-side.room-filter-active-border {
    border: 2px dashed $color-blue-500;
    margin: 0 -2px;
  }

  .staff-side.staff-filter-active-border {
    border: 2px dashed $color-blue-500;
    margin: 0 -2px;
  }

  .left-action {
    border-top-left-radius: 4px;
    pointer-events: all;
  }

  .right-action {
    border-top-right-radius: 4px;
  }

  .selected {
    border: 1px dashed $color-blue-800 !important;
  }

  .dashed {
    border: 2px dashed $color-blue-800 !important;

    &:hover {
      background-color: $color-blue-grey-300 !important;
    }
  }

  .hidden {
    position: absolute;
    right: -20px;
    height: 50px;
    visibility: hidden !important;
    border: none;
    padding: 0;
  }

  .hidden-action {
    position: absolute;
    width: 30px;
    height: 50px;
  }

  .hidden-bottom {
    position: absolute;
    height: 50px;
    visibility: hidden !important;
    border: none;
    padding: 0;
  }

  .hidden-row,
  .hidden-column {
    display: none;
  }

  .hide-th {
    border: none !important;
    padding: 0;
  }
}

thead {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: $color-blue-grey-100;
}
