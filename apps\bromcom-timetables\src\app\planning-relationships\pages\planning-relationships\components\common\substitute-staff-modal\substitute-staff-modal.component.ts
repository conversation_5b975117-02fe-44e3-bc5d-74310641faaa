import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { GridApi, GridOptions, GridReadyEvent } from 'ag-grid-community';
import { Subject, takeUntil } from 'rxjs';
import { SnackbarService } from '@bromcom/ui';
import { BaseModalComponent } from '../../../../../../_shared/components/BaseModalComponent';
import { filterByValue } from '@bromcom/core';
import { substituteStaffGridOptions } from './substitute-staff-modal.helper';
import { ISwapStaffDataEntity } from '../../../../../../_shared/models/ISwapStaffDataEntity';
import { SubstituteStaffService } from '../../../../../services/substitute-staff.service';

@Component({
  selector: 'bromcom-substitute-staff-modal',
  templateUrl: './substitute-staff-modal.component.html',
  styleUrls: ['./substitute-staff-modal.component.scss'],
})
export class SubstituteStaffModalComponent extends BaseModalComponent implements OnInit {
  @ViewChild('substitueStaffModal', { static: false }) substitueStaffModal!: ElementRef;

  @Input() set rowData(data: ISwapStaffDataEntity[]) {
    this.originalRowData = data || [];
    this.setRowData('');
  }

  get rowData() {
    return this.originalRowData;
  }

  @Input() selectedData!: ISwapStaffDataEntity;
  @Input() timetableId!: number;
  @Output() updateData = new EventEmitter<any>();
  gridApi!: GridApi;
  gridOptions!: GridOptions;
  originalRowData: ISwapStaffDataEntity[] = [];
  selectedStaffData!: ISwapStaffDataEntity | null;
  searchControl: FormControl = new FormControl<string>('');
  confirmationMessage!: string;
  showConfirmationView = false;
  readonly unsubscribe$: Subject<void> = new Subject();

  get showHeader() {
    return !this.showConfirmationView;
  }

  constructor(
    protected translate: TranslateService,
    private substituteStaffService: SubstituteStaffService,
    private snackbar: SnackbarService
  ) {
    super();
  }

  ngOnInit(): void {
    this.gridOptions = substituteStaffGridOptions.call(this) as GridOptions;
    this.searchControl.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(searchValue => {
        this.setRowData(searchValue || '');
      });
  }

  show(): void {
    this.gridOptions = substituteStaffGridOptions.call(this) as GridOptions;
    this.isOpen = true;
    this.showConfirmationView = false;
    this.searchControl.setValue('');
    setTimeout(() => {
      this.gridApi.setRowData(this.originalRowData);
      this.setRowData(this.searchControl.getRawValue());
      this.substitueStaffModal.nativeElement.show();
    }, 100);
  }

  close(): void {
    this.isOpen = false;
    this.showConfirmationView = false;
    this.substitueStaffModal.nativeElement.hide();
  }

  onGridReady(params: GridReadyEvent): void {
    this.gridApi = params.api;
    this.gridApi.setDomLayout('normal');
  }

  setRowData(searchValue: string): void {
    let data: ISwapStaffDataEntity[] = [];
    data = filterByValue<ISwapStaffDataEntity>(
      searchValue || '',
      this.originalRowData as ISwapStaffDataEntity[],
      ['firstName', 'lastName', 'code', 'totalContactTime', 'assigned', 'remaining']);


    setTimeout(() => {
      this.gridApi?.setRowData(data);
    }, 0);
  }

  substituteStaff() {
    if (!this.selectedStaffData) {
      return;
    }
    const staffIds: number[] = [];
    staffIds.push(this.selectedData.id, this.selectedStaffData.id);
    this.substituteStaffService.substituteStaff(this.timetableId, staffIds).subscribe({
      next: () => {
        this.updateData.emit();
        this.close();
        this.snackbar.success(this.translate.instant('Substituted successfully'));
      },
      error: ({ error }) => {
        this.close();
        if (error?.validationErrors && error?.validationErrors[0]) {
          this.snackbar.error(this.translate.instant(error.validationErrors[0].errorMessage));
        } else {
          this.snackbar.error(this.translate.instant('An error occurred, data has not been substituted!'));
        }
      }
    });
  }

  onSubstituteStaff() {
    if (!this.selectedStaffData) {
      return;
    }
    this.showConfirmationView = true;
    this.confirmationMessage = this.translate.instant('This operation will assign ') + this.selectedData.code.toUpperCase()
        + this.translate.instant('\'s assignments to ') + this.selectedStaffData.code.toUpperCase() + this.translate.instant(' and clear ')
        + this.selectedData.code.toUpperCase() + this.translate.instant('\'s assignments.');
  }

  onCancel() {
    this.showConfirmationView = false;
    this.selectedStaffData = null;
    this.gridApi.deselectAll();
  }

  onBackClick() {
    this.showConfirmationView = false;
    this.selectedStaffData = null;
    this.gridOptions = substituteStaffGridOptions.call(this) as GridOptions;
    this.setRowData('');
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
