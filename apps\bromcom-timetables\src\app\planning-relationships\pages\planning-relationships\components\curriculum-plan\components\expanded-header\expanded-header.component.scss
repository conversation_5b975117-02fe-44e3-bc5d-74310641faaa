@import '../../../../../../../../assets/styles/variables';

.expanded-header {
  .disabled {
    pointer-events: none;
    color: $color-blue-grey-300;

    .bcm-button__container {
      color: $color-blue-grey-400;
    }
  }

  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    width: 100%;
    height: 24px;
    background: $color-blue-grey-200;

    .circle {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 18px;
      height: 18px;
      background: $color-blue-grey-50;
      border: 1px solid $color-white-0;
      border-radius: 24px;
      font-style: normal;
      font-weight: 500;
      font-size: 10px;
      line-height: 18px;
      color: $color-blue-grey-600;
    }

    .code {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      padding-left: 43px;

      .icon-wrapper {
        width: 30px;
      }

      .input {
        display: flex;
        align-items: center;
        text-align: center;
        justify-content: center;
        width: 48px;
        height: 24px;
        margin-right: 10px;
        border: 1px solid $color-blue-grey-300;
        border-radius: 4px;
      }
    }
  }

  .icon {
    margin-left: 16px;
    cursor: pointer;

    &:first-child {
      font-size: 18px;
    }

    &.error {
      color: $color-red-tertiary-600;
      margin-right: 16px;
    }
  }


  .title {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0 8px;
    width: 100%;
    height: 24px;
    border-bottom: 1px solid $color-blue-grey-200;
    background: $color-blue-grey-100;

    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    text-align: center;
    color: $color-grey-700;
    position: relative;

    .linked-icon {
      width: 18px;
      height: 18px;
      position: absolute;
      left: 6px;

      .icon {
        margin-left: 0 !important;
      }
    }

    .cross-band-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 1px;
      border: 1px solid $color-grey-700;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      background-color: $color-emerald-500;
      color: $color-white-0;
      position: absolute;
      right: 6px;
    }

    .icon-wrapper {
      width: 30px;
    }

    .ng-invalid {
      border-color: $color-red-tertiary-500 !important;
    }

    .input {
      display: flex;
      align-items: center;
      text-align: center;
      justify-content: center;
      width: 160px;
      height: 24px;
      margin-right: 10px;
      border: 1px solid $color-blue-grey-300;
      border-radius: 4px;
    }
  }
}
