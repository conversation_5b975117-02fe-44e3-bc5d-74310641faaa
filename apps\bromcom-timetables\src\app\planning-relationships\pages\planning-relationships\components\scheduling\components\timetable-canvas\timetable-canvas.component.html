<div class="placeholder" #placeholderEl></div>
<div class="diagram-container"
     #diagramEl
>
  <bromcom-context-menu
    [trigger]="{}"
    [data]="menuList"
    [isRootNode]="true"
    (menuClosed)="onMenuClosed()"
    #menuEl
  ></bromcom-context-menu>

  <gojs-diagram
    #mainDiagramEl
    divClassName="diagram"
    [initDiagram]="initDiagram"
    (modelChange)="change($event)"
    [nodeDataArray]="_data"
    (drop)="drop($event)"
    (dragover)="dragOver($event)"
    (dragenter)="dragEnter($event)"
    (dragstart)="dragStart($event)"
    (drag)="drag($event)"
    [draggable]="true"
  >
  </gojs-diagram>
</div>
