@import "apps/bromcom-timetables/src/assets/styles/variables";

.selection-modal-container {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 16px;
  height: 37vh;
  border-radius: 4px;
  border: 1px solid $color-blue-200;

  .tree-list {
    overflow-y: auto;
  }

  ul {
    padding-left: 0;
    list-style: none;
  }

  .header-menu {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid $color-blue-grey-900;
  }

  .line {
    display: flex;
    align-items: center;

    .circle {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      width: 20px;
      height: 20px;
      border-radius: 100px;
      margin-right: 8px;
      border: 1px solid $color-black-0;
    }

    .block-type-circle {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      width: 20px;
      height: 20px;
      border-radius: 100px;
      margin-right: 8px;
      background-color: $color-blue-300;
      color: $color-blue-500
    }

    div {
      margin-right: 16px;
    }

    .input {
      display: flex;
      align-items: center;
      width: 256px;
      height: 32px;
      border: 1px solid $color-blue-grey-300;
      border-radius: 4px;

      &.error {
        color: $color-red-tertiary-600;
        border: 1px solid $color-red-tertiary-600 !important;
        background-color: $color-red-tertiary-50;
      }
    }
  }

  .expand-icons {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
  }

  .tree-invisible {
    display: none;
  }

  .tree ul,
  .tree li {
    margin-top: 0;
    margin-bottom: 0;
    list-style-type: none;
  }

  .tree div[role=group]>.mat-tree-node {
    padding-left: 40px;
    min-height: unset;
  }

  ::ng-deep .mdc-checkbox__ripple,
  ::ng-deep .mat-mdc-checkbox-ripple {
    display: none;
  }

  ::ng-deep .mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,
  .mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,
  .mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background {
    background-color: $color-primary-blue-600 !important;
    border-color: $color-primary-blue-600 !important;
  }
}

.list {
  width: 348px;
}

.hidden {
  display: none !important;
}

.selector-field-holder {
  display: flex;
  gap: 24px;
  justify-content: space-between;

  div {
    width: 100%;
  }
}

.selector-title-holder {
  margin-top: 24px;

  display: flex;
  gap: 24px;
  justify-content: space-between;

  div {
    width: 100%;
  }
}

.selector-holder {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.icon {
  cursor: pointer;
}

.info-msg {
  display: flex;
  align-items: center;
  width: 100%;
  background-color: #fcf3cc;
  border-radius: 8px;
  margin-top: 15px;
  padding: 10px;
}

.lbl-info {
  display: flex;
  align-items: flex-start;
  color: #a85822;
  font-weight: 500;
  font-size: 16px;
}

.lbl-info i {
  align-self: flex-start;
  margin-top: 3px;
}

.spn-info {
  padding-left: 10px;
}

.radio-group-container {
  display: flex;
  align-items: center;
  margin-right: 16px;

  .radio-group-title {
    margin: 0 8px 0 0;
    font-weight: 500;
    font-size: 16px;
  }

  .radio-group {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 25px;
    margin-top: 14px;
  }

  ::ng-deep label {
    font-size: 16px;
    font-weight: 500;
  }
}
