@import 'apps/bromcom-timetables/src/assets/styles/variables';

.wrapper {
  height: calc(100% - 4px);

  ::ng-deep .staff-cards-container {
    height: calc(100% - 32px - 32px - 50px - 32px - 55px) !important;
  }

  ::ng-deep .room-cards-container,
  ::ng-deep .subject-cards-container {
    height: calc(100% - 32px - 32px - 12px - 32px - 55px) !important;
  }
}

.wrapper, .header {
  display: flex;
  justify-content: center;
  font-size: 14px;
  user-select: none;
  color: $color-blue-grey-600;

  .left {
    flex-basis: 30%;
    min-width: 410px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .year-list {
      height: 40px;
    }

    .item-container {
      height: 100%;
      flex: 1;
      border: 1px solid $color-blue-grey-200;
      border-radius: 8px;
      padding: 16px;

      ::ng-deep .button-group {
        width: 100%;
        display: flex;
        justify-content: center;

        .bcm-button {
          width: 25%;

          .bcm-button__container {
            width: 100%;
          }
        }
      }

      .hidden {
        display: none;
      }
    }

    > span {
      display: block;
      width: 200px;
    }

    bromcom-list {
      width: 256px;
      display: block;
    }
  }

  .slider-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    min-width: 46px;
    max-width: 46px;
    margin-top: -40px;
    height: calc(100% + 4px);

    .slider {
      display: flex;
      justify-content: space-around;
      align-items: center;
      background: $color-blue-500;
      width: 30px;
      min-width: 30px;
      height: 30px;
      border-radius: 50%;
      cursor: ew-resize;
      margin: 0 8px;

      ::ng-deep .icon {
        color: white;
        display: flex;
        justify-content: center;
        align-items: center;

        i.fa-arrows-alt-h {
          font-size: 14px;
          margin-top: 2px;
        }

        i.fa-arrow-alt-to-left,
        i.fa-arrow-alt-to-right {
          font-size: 14px;
          margin-top: 2px;
        }
      }
    }

    .hide-menu {
      cursor: pointer;
      margin-bottom: 10px;
    }
  }

  .right {
    height: 100%;
    flex-basis: 70%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    > span {
      border: 1px solid $color-blue-grey-200;
      border-radius: 8px;
      display: block;
      height: 100%;
    }

    .curriculum-plan-container {
      flex: 1;
      overflow: hidden;
      height: 100%;
    }

    .action-button-container {
      flex-basis: 70%;
      height: 40px;
      display: flex;
      justify-content: space-between;

      .bcm-dropdown {
        width: 104px;
      }

      .action-button-block {
        ::ng-deep .action-button.bcm-button {
          height: 30px;

          .bcm-button__container-size-medium {
            min-height: 0;
          }
        }
      }

      .upload-button {
        padding-right: 8px;
      }
    }
  }

  .hide {
    display: none !important;
    width: 0 !important;
  }

  .hidden-side-panels {
    height: 100%;
    border: 1px solid $color-blue-grey-200;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .transformed-text {
      writing-mode: vertical-lr;
      text-orientation: mixed;
      transform: rotate(180deg);
      padding: 4px;

      ::ng-deep .bcm-label__size-large {
        font-size: 18px;
      }
    }
  }

  .full-screen {
    flex-basis: 95% !important;
  }
}

::ng-deep .edit-subject-modal,
::ng-deep .edit-staff-modal {
  z-index: 10;
}

::ng-deep [target-id="schedule-action-list-button"] .bcm-menu__item-link-text {
  overflow: unset;
  white-space: unset;
}

.block-vertical {
  ::ng-deep .bcm-button__container {
    justify-content: left !important;
  }
}

.dot {
  display: flex;
  width: 8px;
  height: 8px;
  padding: 0px 4px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 24px;
  background: $color-primary-blue-600;
}

.mat-button {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;

  & > div {
    margin-right: 6px;
  }
}
