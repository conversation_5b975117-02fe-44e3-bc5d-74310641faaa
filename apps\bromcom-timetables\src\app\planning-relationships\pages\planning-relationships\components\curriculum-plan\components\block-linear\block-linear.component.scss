@import '../../../../../../../../assets/styles/variables';
@import '../../../../../../styles/blocks';

.block-container {
  margin: 8px;
  min-height: 168px;
  background: $color-blue-grey-100;
  border-radius: 4px;

  &:not(.active-block) {
    border: 2px solid $color-white-0;
  }

  .icon {
    font-size: 14px;
    cursor: pointer;
  }

  .header {
    @include header;
    display: flex;
    justify-content: space-between;

    .menu-container {
      @include flex;

      .menu {
        @include flex;
        width: 32px;
        height: 24px;
      }

      .full-size {
        @include flex;

        width: 30px;
        height: 24px;
      }
    }
  }

  .subject-code-color-header {
    @include text-style;
    @include flex;
    flex-wrap: wrap;
    border-bottom: 1px solid $color-blue-grey-200;
    width: 100%;
    overflow: hidden;
    max-width: 376px;

    .code-color-header {
      @include flex;
      min-width: 50px;
      height: 24px;

      flex: 1 1 0;

      .subject-code {
        padding: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .block-type-full-name,
  .period-count {
    @include flex;
    @include text-style;
    @include full-name-style;
  }

  .linear-body-container {
    display: flex;
    flex-direction: row;

    ::ng-deep .subject-info-container-side {
      @include flex;
      flex-direction: column;
      width: 32px;

      .icon {
        @include flex;
        width: 32px;
        height: 40px;
        cursor: pointer;
        color: $color-blue-grey-700;
        border-bottom: 1px solid $color-blue-grey-200;

        i.fa-arrow-to-left,
        i.fa-arrow-to-right {
          margin-top: 4px;
          font-size: 16px;
        }
      }

      .class-name {
        @include flex;
        @include text-style;
      }

      .remove-class {
        @include remove-class;
      }
    }

    .display-none {
      display: none !important;
    }

    .subject-info-container {
      @include subject-info-container;
      flex-direction: row;
      width: 104px;
    }

    .session-info-container-side {
      width: 32px;
      min-height: 40px;
      border-bottom: 1px solid $color-blue-grey-200;
      background: $color-blue-grey-100;
    }
  }

  .session-info-container {
    @include session-info-container;
  }

  .bottom {
    width: 100%;
    height: 16px;
    border-radius: 0 0 4px 4px;
  }
}

.hide-add-new-block {
  width: 0;
  visibility: hidden;
}

.hide-add-new-block-three-subject {
  width: 104px;
  visibility: hidden;

  margin-left: -104px;
}

.set-visible {
  visibility: visible;
}

.disable {
  color: $color-blue-grey-300 !important;
  pointer-events: none;
}
