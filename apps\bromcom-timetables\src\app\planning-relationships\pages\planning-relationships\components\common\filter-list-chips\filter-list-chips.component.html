<span class="filter-list" (click)="toggleOpen()">
  <div class="bcm-list__label__" *ngIf="label">{{ label }}</div>
  <div class="bcm-list__input__">
    <span class="bcm-list__input-container__">
      <span *ngIf="!chipList || chipList?.length === 0" class="placeholder">{{placeholder}}</span>
      <mat-chip-set>
        <ng-container *ngFor="let chip of chipList">
          <mat-chip [innerHTML]="getTemplate(chip) | safe : 'html'"> </mat-chip>
        </ng-container>
      </mat-chip-set>
    </span>
    <span class="bcm-list__input-buttons">
      <div class="bcm-list__input-buttons-button">
          <i
            class="far fa-angle-down"
            [ngClass]="{ 'fa-angle-down': !isOpen, 'fa-angle-up': isOpen }"
            aria-hidden="true"
          ></i>
      </div>
    </span>
  </div>
  </span>
<bromcom-filter
  class="overview-filter"
  [ngStyle]="{'display': isOpen ? 'unset' : 'none'}"
  [dataItems]="dataItems"
  [selection]="selectedItemIds"
  [isOpen]="isOpen"
  [resetFilter]="resetFilter"
  (filterData)="onDataFiltered($event)"
  (closeFilter)="onCloseFilter($event)"
  [appearance]="appearance"
  [enableApplyFilterWithDefaultSelection]="enableApplyFilterWithDefaultSelection"
  [selectAllOnClearedApply]="selectAllOnClearedApply"
></bromcom-filter>
