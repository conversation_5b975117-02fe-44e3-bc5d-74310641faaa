<ng-container>
  <div>
    <div class="selector-field-holder">
      <div>
        <label>{{ 'Select Year Groups' | translate }}</label><br>
        <bromcom-list class="list" [placeholder]="'Select Year Group' | translate" [checkboxes]="true" [checkAll]="true"
                      [data]="selectYearGroups" [formControl]="selectYearGroupsForm"></bromcom-list>
      </div>

      <div>
        <label>{{ 'List Subjects by' | translate }}</label><br>
        <bromcom-list [placeholder]="'List Subjects by' | translate" [data]="listBy"
                      [formControl]="listByForm"></bromcom-list>
      </div>
    </div>

    <div class="selector-title-holder">
      <div>{{ 'Select Subjects / Departments' | translate }}</div>

      <div>{{ 'Select Blocks' | translate }}</div>
    </div>

    <div class="selector-holder">
      <div class="selection-modal-container">
        <bromcom-input-field [formControl]="searchControl" [icon]="'fal fa-search'" style="width: 100%"
                             [iconPosition]="'prefix'" [placeholder]="'Search' | translate"></bromcom-input-field>

        <div class="header-menu">
          <mat-checkbox disableRipple [formControl]="selectAllSubjects">{{ 'Select All' | translate }}
          </mat-checkbox>
          <bcm-button kind="link" (click)="clearAll()">
            {{ 'Clear All' | translate }}
          </bcm-button>
        </div>

        <div [formGroup]="subjectsForm" class="tree-list">
          <ul formArrayName="subjectList">
            <ng-container *ngIf="listByForm.value && +listByForm.value === 1">
              <li *ngFor="let subject of subjectList; let i = index">
                <div class="line" [ngClass]="{
                      'hidden': searchControl.value &&
                        (!subject.name.toLowerCase().includes(searchControl.value.toLowerCase()) &&
                          !subject.code.toLowerCase().includes(searchControl.value.toLowerCase()))}">
                  <mat-checkbox disableRipple [formControlName]="i" (click)="onChange()"></mat-checkbox>
                  <div class="circle" [style]="{'background-color': '#' + subject?.color}"></div>
                  {{ subject.code }} - {{ subject.name }}
                </div>
              </li>
            </ng-container>

            <ng-container *ngIf="listByForm.value && +listByForm.value === 2">
              <li>
                <mat-tree [dataSource]="dataSource" [treeControl]="treeControl" class="tree">
                  <mat-tree-node *matTreeNodeDef="let node" matTreeNodeToggle [ngClass]="{
                                  'hidden': searchControl.value &&
                                    (!node.name.toLowerCase().includes(searchControl.value.toLowerCase()) &&
                                      !node.code.toLowerCase().includes(searchControl.value.toLowerCase()))}">
                    <div class="line">
                      <mat-checkbox [formControlName]="node.name | getSubjectIndex:subjectList"
                                    (click)="onChange()"></mat-checkbox>
                      <div class="circle" [style]="{'background-color': '#' + node?.color}"></div>
                      {{ node.code }} - {{ node.name }}
                    </div>
                  </mat-tree-node>

                  <mat-nested-tree-node *matTreeNodeDef="let node; when: hasChild">
                    <div class="mat-tree-node">
                      <div matTreeNodeToggle [attr.aria-label]="'Toggle ' + node.name">
                        <div class="expand-icons">
                          <bcm-icon class="icon" *ngIf="!treeControl.isExpanded(node) && !isSubjectSearched(node)"
                                    icon="far fa-chevron-right">
                          </bcm-icon>

                          <bcm-icon class="icon" *ngIf="treeControl.isExpanded(node) || isSubjectSearched(node)"
                                    icon="far fa-chevron-down">
                          </bcm-icon>
                        </div>
                      </div>
                      {{ node.name }}
                    </div>

                    <div [class.tree-invisible]="!treeControl.isExpanded(node) && !isSubjectSearched(node)"
                         role="group">
                      <ng-container matTreeNodeOutlet></ng-container>
                    </div>
                  </mat-nested-tree-node>
                </mat-tree>
              </li>
            </ng-container>
          </ul>
        </div>
      </div>

      <div class="selection-modal-container">
        <bromcom-input-field [formControl]="blocksSearchControl" [icon]="'fal fa-search'" style="width: 100%"
                             [iconPosition]="'prefix'" [placeholder]="'Search' | translate"></bromcom-input-field>

        <div class="header-menu">
          <mat-checkbox disableRipple [formControl]="selectAllBlocks">{{ 'Select All' | translate }}
          </mat-checkbox>
          <bcm-button kind="link" (click)="clearAllBlocks()" [disabled]="!blocksList.length">
            {{ 'Clear All' | translate }}
          </bcm-button>
        </div>

        <div *ngIf="!blocksList.length"
             style="display: flex; justify-content: center; align-items: center; height: inherit; width: 100%;">
          <bcm-empty icon="fas fa-block">No blocks available</bcm-empty>
        </div>

        <div *ngIf="blocksList.length" [formGroup]="blocksForm" class="tree-list">
          <ul formArrayName="blockList">
            <li>
              <mat-tree [dataSource]="blockDataSource" [treeControl]="blockTreeControl" class="tree">
                <mat-tree-node *matTreeNodeDef="let node" matTreeNodeToggle [ngClass]="{
                                  'hidden': blocksSearchControl.value &&
                                    (!node.name.toLowerCase().includes(blocksSearchControl.value.toLowerCase()))}">
                  <div *ngIf="!node.id" class="expand-icons">
                    <bcm-icon class="icon" *ngIf="!blockTreeControl.isExpanded(node) && !isBlockSearched(node)"
                              icon="far fa-chevron-right">
                    </bcm-icon>

                    <bcm-icon class="icon" *ngIf="blockTreeControl.isExpanded(node) || isBlockSearched(node)"
                              icon="far fa-chevron-down">
                    </bcm-icon>
                  </div>
                  <div class="line">
                    <mat-checkbox *ngIf="node.id" [formControlName]="node.id | getBlockIndex:blocksList"></mat-checkbox>
                    <div *ngIf="node.id" class="block-type-circle">{{ node.blockTypeId | blockTypeLetter }}</div>
                    {{ node.name }}
                  </div>
                </mat-tree-node>

                <mat-nested-tree-node *matTreeNodeDef="let node; when: hasChild">
                  <div class="mat-tree-node">
                    <div matTreeNodeToggle [attr.aria-label]="'Toggle ' + node.name">
                      <div class="expand-icons">
                        <bcm-icon class="icon" *ngIf="!blockTreeControl.isExpanded(node) && !isBlockSearched(node)"
                                  icon="far fa-chevron-right">
                        </bcm-icon>

                        <bcm-icon class="icon" *ngIf="blockTreeControl.isExpanded(node) || isBlockSearched(node)"
                                  icon="far fa-chevron-down">
                        </bcm-icon>
                      </div>
                    </div>
                    {{ node.name }}
                  </div>

                  <div [class.tree-invisible]="!blockTreeControl.isExpanded(node) && !isBlockSearched(node)"
                       role="group">
                    <ng-container matTreeNodeOutlet></ng-container>
                  </div>
                </mat-nested-tree-node>
              </mat-tree>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <div class="info-msg">
    <label class="lbl-info">
      <i class="far fa-exclamation-triangle" aria-hidden="true"></i>
      <span
        class="spn-info">{{ 'Auto-scheduling will ignore all locked sessions and not selected entities, including those which include conflicts for Staff, Rooms and Timetable Specifications.' | translate }}</span>
    </label>
  </div>

  <div class="selector-field-holder">
    <div class="radio-group-container">
      <span class="radio-group-title">{{ 'Keep existing scheduled sessions?' | translate }}</span>
      <bcm-tooltip
        [message]="'If &quot;Yes&quot;, auto-scheduling will only fill empty slots without modifying your current timetable.' | translate"
        trigger="hover" color="slate">
        <bcm-icon class="icon info-icon" icon="fas fa-info-circle"></bcm-icon>
      </bcm-tooltip>
      <bcm-radio-group class="radio-group" name="radiogroup" full-width #keepExistingScheduleRadioGroup no-caption
                       (bcm-radio-change)="keepExistingScheduleRadioChange()">
        <bcm-radio value="yes">{{ 'Yes' | translate }}</bcm-radio>
        <bcm-radio value="no">{{ 'No' | translate }}</bcm-radio>
      </bcm-radio-group>
    </div>

    <div class="radio-group-container">
      <span class="radio-group-title">{{ 'Include Draft Non-Contact Codes?' | translate }}</span>
      <bcm-radio-group class="radio-group" name="radiogroup" full-width #includeDraftNccRadioGroup no-caption
                       (bcm-radio-change)="includeDraftNccRadioChange()">
        <bcm-radio value="yes">{{ 'Yes' | translate }}</bcm-radio>
        <bcm-radio value="no">{{ 'No' | translate }}</bcm-radio>
      </bcm-radio-group>
    </div>
  </div>
</ng-container>
