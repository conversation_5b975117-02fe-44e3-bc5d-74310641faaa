@import '../../../../../../../../assets/styles/variables';
@import '../../../../../../styles/blocks';

@mixin flex() {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin text-style() {
  font-family: 'Inter', serif;
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
}

.subject-info-container {
  @include subject-info-container;
}

.session-info-container {
  @include session-info-container;
  @include subject-info-container;
}

.hide-add-new-block {
  height: 0;
  visibility: hidden;
}
