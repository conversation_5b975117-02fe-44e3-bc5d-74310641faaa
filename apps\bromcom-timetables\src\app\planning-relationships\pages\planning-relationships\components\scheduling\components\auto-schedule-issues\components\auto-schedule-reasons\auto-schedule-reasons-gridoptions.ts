﻿import {AutoScheduleIssuesComponent} from "../../auto-schedule-issues.component";
import {transformToAGGridConfig} from "@bromcom/core";
import {AgGridNoDataComponent} from "@bromcom/ui";

export function reasonsGridOptions(this: AutoScheduleIssuesComponent) {

  return transformToAGGridConfig({
    animateRows: false,
    suppressRowClickSelection: true,
    suppressClickEdit: true,
    suppressCellFocus: true,
    tooltipShowDelay: 500,
    rowHeight: 48,
    noRowsOverlayComponent: AgGridNoDataComponent,
    noRowsOverlayComponentParams: {
      style: 'display: flex; justify-content: center; align-items: center; height: 100%; width: 100%; min-height: 162px; padding: 48px 0 0 0',
      noRowsMessageFunc: () => this.translate.instant(`No data available!`)
    },
    columnDefs: [
      {
        field: 'period',
        headerName: this.translate.instant('Period'),
        minWidth: 180,
        flex: 1,
        menuTabs: ['filterMenuTab'],
      },
      {
        field: 'reason',
        headerName: this.translate.instant('Reason'),
        minWidth: 150,
        flex: 5,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: params => {
          if (params.data.issueEntityType == 8) {
            return params.value;
          }
        },
      }
    ]
  })
}
