<span [matMenuTriggerFor]="menu"></span>
<mat-menu #menu="matMenu"
          class="block-action-modal">
  <div class="block-vertical">
    <button mat-menu-item
            [matMenuTriggerFor]="copyBlockRef">
      {{'Copy Block' | translate }}
    </button>

    <button *ngIf="isSidePanel"
            mat-menu-item
            (click)="expandBlock()">
      {{'Edit Block' | translate }}
    </button>

    <button mat-menu-item
            (click)="openEditClassCodesModal()">
      {{'Edit classcodes' | translate }}
    </button>

    <button mat-menu-item
            (click)="showDeleteBlock()">
      {{'Delete Block' | translate }}
    </button>

    <button *ngIf="!showBasicActions"
            mat-menu-item
            [matMenuTriggerFor]="transformBlockRef"
            [disabled]="noSubjectAdded || disableSimpleBlock && disableLinearBlock && disableOptionBlock && disableComplexBlock">
      {{'Transform Block' | translate }}
    </button>

    <button mat-menu-item
            [disabled]="!hasScheduled || isAllLocked"
            (click)="onUnScheduleClicked()">
      {{'Unschedule Block' | translate }}
    </button>

    <button mat-menu-item
            [matMenuTriggerFor]="spreadToBandsRef"
            [disabled]="spreadToBandsOptions.length === 0">
      {{'Spread to bands' | translate }}
    </button>

    <button mat-menu-item
            [disabled]="block.bandIds.length === 1"
            (click)="onSplitToBands()">
      {{'Split to bands' | translate }}
    </button>

    <button *ngIf="block.blockTypeId !== BLOCK_TYPE.Linear && !isSidePanel"
            mat-menu-item
            [disabled]="!!block.linkedPairID"
            (click)="onLinkBlocks()">
      {{'Link Blocks' | translate }}
    </button>

    <button *ngIf="block.blockTypeId !== BLOCK_TYPE.Linear && !isSidePanel"
            mat-menu-item
            [disabled]="!block.linkedPairID"
            (click)="onUnlinkBlocks()">
      {{'Unlink Blocks' | translate }}
    </button>
  </div>
</mat-menu>

<bromcom-general-modal #linkBlocksWarningModal
                       icon="far fa-exclamation-triangle"
                       type="warningAmber"
                       [header]="'Do you want to continue?' | translate"
                       [cancelText]="'Cancel' | translate"
                       [nextText]="'Continue' | translate"
                       [description]="'This operation will unschedule the block.' | translate"
                       (nextEvent)="onOpenLinkBlocks()">
</bromcom-general-modal>

<mat-menu class="block-action-modal"
          #transformBlockRef>
  <div class="block-vertical">
    <button mat-menu-item
            [disabled]="disableSimpleBlock"
            (click)="onTransformClicked(BLOCK_TYPE.Simple)">
      {{'Simple Block' | translate }}
    </button>

    <button mat-menu-item
            [disabled]="disableLinearBlock"
            (click)="onTransformClicked(BLOCK_TYPE.Linear)">
      {{'Linear Block' | translate }}
    </button>

    <button mat-menu-item
            [disabled]="disableOptionBlock"
            (click)="onTransformClicked(BLOCK_TYPE.Options)">
      {{'Option Block' | translate }}
    </button>

    <button mat-menu-item
            [disabled]="disableComplexBlock"
            (click)="onTransformClicked(BLOCK_TYPE.Complex)">
      {{'Complex Block' | translate }}
    </button>
  </div>
</mat-menu>

<mat-menu class="block-action-modal"
          #copyBlockRef>
  <div class="block-vertical">
    <button *ngFor="let band of bands"
            mat-menu-item
            (click)="onCopyClicked(band.id)">
      {{band.bandName}}
    </button>
  </div>
</mat-menu>

<mat-menu class="block-action-modal"
          #spreadToBandsRef>
  <div class="block-vertical">
    <button *ngFor="let option of spreadToBandsOptions, index as i"
            mat-menu-item
            [disabled]="spreadToBandsValidCombinations[i] | alreadySpreadToBand: block.bandIds"
            (click)="onSpreadToBandsClicked(i)">
      {{option}}
    </button>
  </div>
</mat-menu>

<bromcom-delete-confirmation #blockDeleteConfirmationComponent
                             [headerText]="'Are you sure you want to delete the whole block?' | translate"
                             [mainText]="'If you proceed all the block\'s settings will be lost.' | translate"
                             (deleteClicked)="onDeleteClicked($event)"
></bromcom-delete-confirmation>
