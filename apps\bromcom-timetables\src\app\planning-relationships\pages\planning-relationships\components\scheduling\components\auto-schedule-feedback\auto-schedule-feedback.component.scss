@import 'apps/bromcom-timetables/src/assets/styles/variables';
@import 'apps/bromcom-timetables/src/app/planning-relationships/pages/planning-relationships/components/scheduling/components/timetable-specification/styles/tts-ag-grid';

.auto-schedule-feedback-container {
  .action-bar {
    @include action-bar;
  }

  .table-container {
    @include table-container;

    ::ng-deep .ag-cell {
      height: 48px !important;
      line-height: unset;
    }

    ::ng-deep .ag-header-cell {
      border-right: none;
    }

    ::ng-deep .scheduled-periods {
      padding: 8px 12px;
      border-radius: 4px;

      &:hover {
        background-color: $color-blue-grey-100;
        cursor: pointer;
      }
    }
  }
}

.auto-schedule-feedback-expanded-modal {
  .modal-body {
    max-height: 59vh;
    overflow: auto;
    margin: 24px;

    .expanded-container {
      height: 100%;
    }
  }
}
