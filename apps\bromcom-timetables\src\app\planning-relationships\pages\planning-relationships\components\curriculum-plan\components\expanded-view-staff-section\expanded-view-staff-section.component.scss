@use '../../../../../../styles/expanded-detailed-block' as style;
@import '../../../../../../../../assets/styles/variables';

.staff-side {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 30px;

  div {
    display: flex;
    text-align: center;
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .session-staff-icon {
    display: flex;
    flex-direction: column;
    position: relative;
    text-align: left;

    .plus-icon {
      position: absolute;
      top: -5px;
      right: -8px;
    }

    .icon {
      font-size: 12px;
      color: $color-grey-600;

      &.pointer {
        cursor: pointer;
      }

      &.staff-conflict {
        color: $color-red-tertiary-500;
      }

      &.staff-filter-active {
        color: $color-blue-500;
      }
    }
  }

  .text {
    margin-left: 10px;
    width: 55px;
    justify-content: initial;

    .session-staff,
    .session-main-staff {
      display: inline-block;
      width: 55px;
      text-align: initial;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
