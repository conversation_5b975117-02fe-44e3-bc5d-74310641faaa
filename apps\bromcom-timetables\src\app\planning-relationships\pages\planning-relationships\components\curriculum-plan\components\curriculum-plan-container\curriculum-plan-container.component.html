<div class="curriculum-plan-container">
  <div class="main-actions">
    <div class="add-actions">
      <bromcom-dropdown-menu
        [data]="bandOptionsMenu"
        icon="far fa-plus"
        id="band-list"
        [label]="'Band' | translate"
      >
      </bromcom-dropdown-menu>
      <bromcom-dropdown-menu
        [data]="blockOptionsMenu"
        icon="far fa-plus"
        id="block-list"
        [label]="'Block' | translate"
      >
      </bromcom-dropdown-menu>
    </div>

    <div class="select-actions">
      <bromcom-list class="list block-type-list"
                    placeholder="{{'Blocks Type' | translate}}"
                    [formControl]="displayBlocksFormControl"
                    [checkboxes]="true"
                    [checkAll]="false"
                    [data]="blockOptions"></bromcom-list>

      <bromcom-list class="list" placeholder="{{'Subjects' | translate}}"
                    [formControl]="displaySubjectsFormControl"
                    [data]="subjectOptions"
                    [checkboxes]="true"
                    [listWithCircle]="true">
      </bromcom-list>
    </div>
  </div>

  <div class="planning-area">
    <bromcom-band-side-panel class="band-side-panel" #bandSidePanel
                             [timetableId]="timetableId"
                             [disableRemoveByYear]="blocksByYearGroupLength === 0"
                             [disableRemoveByBand]="blocksByBandLength === 0"></bromcom-band-side-panel>

    <div class="blocks-container" #blocksContainer>
      <div class="block-row" id="simpleBlockRow" *ngIf="isInitialized && simpleBlockList.length"
           [class.hidden]="!isSubjectDraggingActive && !simpleBlockList.length">
        <bromcom-block-simple *ngFor="let simpleBlock of simpleBlockList, index as i"
                              [classContextMenuRef]="classContextMenuRef"
                              [id]="simpleBlock.id.toString()"
                              [isSubjectDraggingActive]="isSubjectDraggingActive"
                              [isStaffDraggingActive]="isStaffDraggingActive"
                              [isRoomDraggingActive]="isRoomDraggingActive"
                              [freeSubjectId]="freeSubjectId"
                              [isActiveBlock]="selectedBlockId === simpleBlock.id"
                              [block]="simpleBlock"
                              [listIndex]="i"
                              (addSubjectToBlock)="addSubjectToBlock($event, BLOCK_TYPE.Simple)"
                              (addStaffToBlock)="addStaffToBlock($event)"
                              [sessionMenuRef]="sessionMenuRef"
                              [subjectMenuRef]="subjectMenuRef"
                              (sessionMenuOpenedEvent)="sessionMenuOpened($event)"
                              (subjectMenuOpenedEvent)="subjectMenuOpened($event)"
                              (periodMenuOpenedEvent)="periodMenuOpened($event)"
                              (openEditClassCodesModalEvent)="openEditClassCodesModal($event)"
                              (openLinkBlocksModalComponentEvent)="openLinkBlocksModal($event)"
                              (addRoomToBlock)="addRoomToBlock($event)"></bromcom-block-simple>
      </div>

      <div class="block-row" id="linearBlockRow" *ngIf="isInitialized && linearBlockList.length">
        <bromcom-block-linear *ngFor="let linearBlock of linearBlockList, index as i"
                              [classContextMenuRef]="classContextMenuRef"
                              [id]="linearBlock.id.toString()"
                              [isSubjectDraggingActive]="isSubjectDraggingActive"
                              [isStaffDraggingActive]="isStaffDraggingActive"
                              [isRoomDraggingActive]="isRoomDraggingActive"
                              [freeSubjectId]="freeSubjectId"
                              [isActiveBlock]="selectedBlockId === linearBlock.id"
                              [block]="linearBlock"
                              [listIndex]="i"
                              (addSubjectToLinearBlock)="addSubjectToBlock($event, BLOCK_TYPE.Linear)"
                              (addStaffToBlock)="addStaffToBlock($event)"
                              [sessionMenuRef]="sessionMenuRef"
                              (sessionMenuOpenedEvent)="sessionMenuOpened($event)"
                              (addRoomToBlock)="addRoomToBlock($event)"
                              [subjectMenuRef]="subjectMenuRef"
                              (openEditClassCodesModalEvent)="openEditClassCodesModal($event)"
                              (openLinkBlocksModalComponentEvent)="openLinkBlocksModal($event)"
                              (periodMenuOpenedEvent)="periodMenuOpened($event)"
                              (subjectMenuOpenedEvent)="subjectMenuOpened($event)"></bromcom-block-linear>
      </div>

      <div class="block-row" id="optionBlockRow" *ngIf="isInitialized && optionBlockList.length">
        <bromcom-block-option *ngFor="let optionBlock of optionBlockList, index as i"
                              class="option-block"
                              [classContextMenuRef]="classContextMenuRef"
                              [id]="optionBlock.id.toString()"
                              [isSubjectDraggingActive]="isSubjectDraggingActive"
                              [isStaffDraggingActive]="isStaffDraggingActive"
                              [isRoomDraggingActive]="isRoomDraggingActive"
                              [freeSubjectId]="freeSubjectId"
                              [isActiveBlock]="selectedBlockId === optionBlock.id"
                              [block]="optionBlock"
                              [listIndex]="i"
                              (addSubjectToBlock)="addSubjectToBlock($event, BLOCK_TYPE.Options)"
                              (addStaffToBlock)="addStaffToBlock($event)"
                              [sessionMenuRef]="sessionMenuRef"
                              (sessionMenuOpenedEvent)="sessionMenuOpened($event)"
                              (addRoomToBlock)="addRoomToBlock($event)"
                              [subjectMenuRef]="subjectMenuRef"
                              (openEditClassCodesModalEvent)="openEditClassCodesModal($event)"
                              (openLinkBlocksModalComponentEvent)="openLinkBlocksModal($event)"
                              (periodMenuOpenedEvent)="periodMenuOpened($event)"
                              (subjectMenuOpenedEvent)="subjectMenuOpened($event)"></bromcom-block-option>
      </div>

      <div class="block-row" id="complexBlockRow" *ngIf="isInitialized && complexBlockList.length">
        <bromcom-block-complex *ngFor="let complexBlock of complexBlockList, index as i"
                               class="complex-block"
                               [classContextMenuRef]="classContextMenuRef"
                               [id]="complexBlock.id.toString()"
                               [isSubjectDraggingActive]="isSubjectDraggingActive"
                               [isStaffDraggingActive]="isStaffDraggingActive"
                               [isRoomDraggingActive]="isRoomDraggingActive"
                               [freeSubjectId]="freeSubjectId"
                               [isActiveBlock]="selectedBlockId === complexBlock.id"
                               [block]="complexBlock"
                               [complexBlock]="complexBlock"
                               [listIndex]="i"
                               (addSubjectToComplexBlock)="addSubjectToBlock($event, BLOCK_TYPE.Complex)"
                               (addStaffToBlock)="addStaffToBlock($event)"
                               [sessionMenuRef]="sessionMenuRef"
                               (sessionMenuOpenedEvent)="sessionMenuOpened($event)"
                               (addRoomToBlock)="addRoomToBlock($event)"
                               [subjectMenuRef]="subjectMenuRef"
                               (openEditClassCodesModalEvent)="openEditClassCodesModal($event)"
                               (openLinkBlocksModalComponentEvent)="openLinkBlocksModal($event)"
                               (periodMenuOpenedEvent)="periodMenuOpened($event)"
                               (subjectMenuOpenedEvent)="subjectMenuOpened($event)"></bromcom-block-complex>
      </div>
    </div>
  </div>
</div>
