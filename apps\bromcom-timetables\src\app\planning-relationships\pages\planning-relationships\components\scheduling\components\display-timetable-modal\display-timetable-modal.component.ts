import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { IStaff } from '../../../../../../../_shared/models/IStaff';
import { IRooms } from '../../../../../../../_shared/models/IRooms';
import { Subject, takeUntil } from 'rxjs';
import { IWithPeriodStructureResponse } from '../../../../../../../_shared/models/IWithPeriodStructureResponse';
import { ITimetableWeek } from '../../../../../../../_shared/models/ITimetableWeek';
import { ITimetableDay } from '../../../../../../../_shared/models/ITimetableDay';
import { ITimetableDayPeriod } from '../../../../../../../_shared/models/ITimetableDayPeriod';
import { IListOfBlocksResponse } from '../../../../../../../_shared/models/IListOfBlocksResponse';
import { ISessionWithBlocks } from '../../../../../../../_shared/models/ISessionWithBlocks';
import { ISubject } from '../../../../../../../_shared/models/ISubject';
import { MenuComponent, isColorDark } from '@bromcom/ui';
import { isBreak } from '../../scheduling.helper';
import { INonContactCodes } from '../../../../../../../_shared/models/INonContactCodes';
import { INonContactCodesAssociation } from '../../../../../../../_shared/models/INonContactCodesAssociation';
import { IMenuItem, IMenuItemCommandEvent } from '@bromcom/ui';
import { TranslateService } from '@ngx-translate/core';
import { IRemoveStaffFromSession } from '../../../../../../../_shared/models/IRemoveStaffFromSession';
import { IRemoveRoomFromSession } from '../../../../../../../_shared/models/IRemoveRoomFromSession';
import { IDisplayTimetableData } from '../../../../../../../_shared/models/IDisplayTimetableData';
import { BaseModalComponent } from '../../../../../../../_shared/components/BaseModalComponent';
import { MatMenuTrigger } from '@angular/material/menu';

const defaultBackground = 'f8fafc';

@Component({
  selector: 'bromcom-display-timetable-modal',
  templateUrl: './display-timetable-modal.component.html',
  styleUrls: ['./display-timetable-modal.component.scss']
})
export class DisplayTimetableModalComponent extends BaseModalComponent implements OnInit, OnChanges, OnDestroy {
  @ViewChild(MatMenuTrigger) menuTrigger!: MatMenuTrigger;

  @Input() isTeacherTimetable!: boolean;

  @Input() set data(data: IRooms[] | IStaff[]) {
    if (this.isTeacherTimetable) {
      data = (data as IStaff[]).filter(data => !data.isExcluded).map((d: IStaff) => {
        return { ...d, text: d.firstName + ' ' + d.lastName };
      });
    } else {
      data = (data as IRooms[]).filter(data => !data.isExcluded).map((d: IRooms) => {
        return { ...d, text: d.name };
      });
    }
    this.dropDownData = data;
  }

  @Input() set selectedData(displayData: IDisplayTimetableData) {
    const data = displayData?.selectedData;
    if (displayData?.selectedId) {
      const id = displayData.selectedId;
      this.selectedTeacherOrRoomControl.setValue(id);
    }
    if (this.isTeacherTimetable) {
      this.selectedTeacher = (data as IStaff)?.firstName + ' ' + (data as IStaff)?.lastName;
    } else {
      this.selectedRoom = (data as IRooms)?.name;
    }
  }

  @Input() set periods(periods: IWithPeriodStructureResponse) {
    this.weeks = periods?.periodStructure.weeks;
  }

  private _blocks!: IListOfBlocksResponse[];
  @Input() set blocks(blocks: IListOfBlocksResponse[] | null) {
    this._blocks = blocks || [];
  }

  get blocks() {
    return this._blocks;
  }

  private _subjects!: ISubject[];
  @Input() set subjects(subjects: ISubject[] | null) {
    this._subjects = subjects || [];
  }

  get subjects() {
    return this._subjects;
  }

  private _rooms!: IRooms[];
  @Input() set rooms(rooms: IRooms[] | null) {
    this._rooms = rooms || [];
  }

  get rooms() {
    return this._rooms;
  }

  private _staffs!: IStaff[];
  @Input() set staffs(staffs: IStaff[] | null) {
    this._staffs = staffs || [];
  }

  get staffs() {
    return this._staffs;
  }

  private _nonContactCodes!: INonContactCodes[] | null;
  @Input() set nonContactCodes(nonContactCodes: INonContactCodes[] | null) {
    this._nonContactCodes = nonContactCodes ? nonContactCodes.filter(ncc => !ncc.isExcluded) : [];
  }

  get nonContactCodes(): INonContactCodes[] {
    return this._nonContactCodes!;
  }

  private _nonContactCodesAssociations!: INonContactCodesAssociation[];
  @Input() set nonContactCodesAssociations(nonContactCodes: INonContactCodesAssociation[]) {
    this._nonContactCodesAssociations = nonContactCodes;
  }

  get nonContactCodesAssociations(): INonContactCodesAssociation[] {
    return this._nonContactCodesAssociations!;
  }

  @ViewChild('displayTimetableModal', { static: false }) displayTimetableModal!: ElementRef;
  @ViewChild('displayTimetable') displayTimetable!: ElementRef;
  @ViewChild('menuEl', { read: ElementRef }) menuEl!: ElementRef;
  @ViewChild('menuEl', { read: MenuComponent }) menu!: MenuComponent;
  @Output() removeStaffClick: EventEmitter<IRemoveStaffFromSession> = new EventEmitter();
  @Output() removeRoomClick: EventEmitter<IRemoveRoomFromSession> = new EventEmitter();
  @Output() nonContactCodeClick: EventEmitter<INonContactCodesAssociation[]> = new EventEmitter();
  @Output() nonContactCodeRemoveClick: EventEmitter<number[]> = new EventEmitter();
  @Output() nonContactCodeGroupRemoveClick: EventEmitter<number> = new EventEmitter();
  selectedTeacher!: string;
  selectedRoom!: string;
  dropDownData: any[] = [];
  selectedTeacherOrRoomControl = new FormControl<number | null>(null);
  readonly unsubscribe$: Subject<void> = new Subject();
  weeks: ITimetableWeek[] = [];
  sessions: ISessionWithBlocks[] = [];
  textColor!: string;
  selection: any[] = [];
  selectedIds: number[] = [];
  menuList: IMenuItem[] = [];
  dayWithMaxPeriods!: ITimetableDay;

  constructor(
    private translateService: TranslateService,
    private cdr: ChangeDetectorRef
  ) {
    super();
  }

  eventHandler(event: MouseEvent) {
    event.preventDefault();
  }

  ngOnInit(): void {
    this.selectedTeacherOrRoomControl.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(selectedTeacherOrRoom => {
        if (selectedTeacherOrRoom) {
          if (this.isTeacherTimetable) {
            this.selectedTeacher = this.dropDownData.find(data => data.id === selectedTeacherOrRoom)?.text;
          } else {
            this.selectedRoom = this.dropDownData.find(data => data.id === selectedTeacherOrRoom)?.text;
          }
          this.setSessions(selectedTeacherOrRoom);
          this.selection = [];
          this.selectedIds = [];
          this.cdr.markForCheck();
        }
      });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['blocks'] && this.selectedTeacherOrRoomControl.value) {
      this.setSessions(this.selectedTeacherOrRoomControl.value);
    }
  }

  show(): void {
    this.isOpen = true;
    setTimeout(() => {
      this.displayTimetableModal.nativeElement.show();
    }, 100);

  }

  close(): void {
    this.isOpen = false;
    this.displayTimetableModal.nativeElement.hide();
  }

  getRoomName(roomId: number) {
    return this._rooms.find(room => room.id === roomId)?.code || '--';
  }

  setSessions(selectedId: number) {
    this.sessions = this._blocks
      .map((block) => block.sessions)
      .flat()
      .map((session) => {
        const block = this._blocks.find(
          (block) => !!block.sessions?.find((blockSession) => blockSession.id === session?.id)
        );
        return {
          ...session,
          block: block
        };
      })
      .filter(s => {
        if (this.isTeacherTimetable) {
          return (
            (!!s.mainStaffId || !!s.additionalStaffIds?.length) &&
            (s.mainStaffId === selectedId || !!s.additionalStaffIds?.includes(selectedId)) &&
            !!s.periodId
          );
        } else {
          return !!s.roomId && s.roomId === selectedId && !!s.periodId;
        }
      }) as ISessionWithBlocks[];
  }

  isNonContactCodeHolder(period: ITimetableDayPeriod) {
    return !!this._nonContactCodesAssociations.filter((n) => {
      if (this.isTeacherTimetable) {
        return n.staffId === this.selectedTeacherOrRoomControl.value && n.periodId === period.id;
      } else {
        return n.roomId === this.selectedTeacherOrRoomControl.value && n.periodId === period.id;
      }
    })?.length;
  }

  openContextMenuForSession(event: MouseEvent, session: ISessionWithBlocks, period: ITimetableDayPeriod, menuEl: any) {
    if (isBreak(period.periodCode)) {
      return;
    }

    this.menuList = [
      {
        label: this.translateService.instant(`Remove`),
        command: (event: IMenuItemCommandEvent) => {
          if (this.isTeacherTimetable) {
            this.removeStaffClick.emit({ staffId: session.mainStaffId, sessionIds: [session.id], isMainStaff: true });
            this.sessions = this.sessions.map(s => {
              return { ...s, mainStaffId: s.id === session.id ? null : s.mainStaffId }
            });
          } else {
            this.removeRoomClick.emit({ roomId: session.roomId, sessionIds: [session.id] });
            this.sessions = this.sessions.map(s => {
              return { ...s, roomId: s.id === session.id ? null : s.roomId }
            });
          }
          this.getSessions(period);
        }
      },
      {
        label: this.isTeacherTimetable ? this.translateService.instant(`Remove room from this session`) :
          this.translateService.instant(`Remove teacher from this session`),
        disabled: this.isTeacherTimetable ? !session.roomId : !session.mainStaffId,
        command: (event: IMenuItemCommandEvent) => {
          if (this.isTeacherTimetable) {
            this.removeRoomClick.emit({ roomId: session.roomId, sessionIds: [session.id] });
          } else {
            this.removeStaffClick.emit({ staffId: session.mainStaffId, sessionIds: [session.id], isMainStaff: true });
          }

        }
      }
    ]

    menuEl.openMenu();
  }

  openContextMenuForEmptyArea(event: MouseEvent, period: ITimetableDayPeriod, menuEl: any, nonContactCode?: any) {
    event.preventDefault();
    event.stopPropagation();
    const nonContactCodeAssociationId = nonContactCode?.nonContactCodeAssociationId ?? null;
    const groupId = nonContactCodeAssociationId ? nonContactCode.nonContactCodeGroupId : null;
    const staffCode = groupId ? this._staffs.find(s => s.id === nonContactCode?.staffId)?.code : null;
    if (isBreak(period.periodCode)) {
      return;
    }
    if (event.button === 2) {
      if (this.selectedIds.includes(period.id)) {
        const selectionIndex = this.selection.findIndex(p => p.id === period.id);
        const selectedIdIndex = this.selectedIds.findIndex(p => p === period.id);
        this.selection.splice(selectionIndex, 1);
        this.selectedIds.splice(selectedIdIndex, 1);
      } else {
        this.selection.push(period);
        this.selectedIds.push(period.id);
      }
    } else {
      this.menuList = [
        {
          label: groupId ? this.translateService.instant('Remove for ') + staffCode : this.translateService.instant('Remove'),
          disabled: !nonContactCode,
          command: (event: IMenuItemCommandEvent) => {
            this.nonContactCodeRemoveClick.emit([nonContactCode.nonContactCodeAssociationId]);
          }
        },
        {
          label: this.translateService.instant('Non-Contact Codes'),
          children: this.nonContactCodes.filter(nc => this.isTeacherTimetable ? nc.isTeacher : nc.isRoom).map(nc => {
            return {
              label: nc.codeName,
              command: (event: IMenuItemCommandEvent) => {
                const nonContactCodeList: INonContactCodesAssociation[] = this.selection.map(s => {
                  return {
                    nonContactCodeId: nc.id,
                    periodId: s.id,
                    roomId: (!this.isTeacherTimetable && this.selectedTeacherOrRoomControl.value) ? this.selectedTeacherOrRoomControl.value : undefined,
                    staffId: (this.isTeacherTimetable && this.selectedTeacherOrRoomControl.value) ? this.selectedTeacherOrRoomControl.value : undefined,
                    nonContactCodeAssociationId: undefined,
                    isLunch: false
                  }
                })
                const selectionIndex = this.selection.findIndex(s => s.id == period.id);
                if (selectionIndex < 0) {
                  nonContactCodeList.push({
                    nonContactCodeId: nc.id,
                    periodId: period.id,
                    roomId: (!this.isTeacherTimetable && this.selectedTeacherOrRoomControl.value) ? this.selectedTeacherOrRoomControl.value : undefined,
                    staffId: (this.isTeacherTimetable && this.selectedTeacherOrRoomControl.value) ? this.selectedTeacherOrRoomControl.value : undefined,
                    nonContactCodeAssociationId: nonContactCode?.nonContactCodeAssociationId ? nonContactCode.nonContactCodeAssociationId : undefined,
                    isLunch: false
                  })
                }
                this.nonContactCodeClick.emit(nonContactCodeList);
                this.selection = [];
                this.selectedIds = [];
              }
            }
          })

        }
      ];

      if (groupId) {
        this.menuList.push({
          label: this.translateService.instant('Remove for all assigned staff'),
          disabled: !nonContactCode,
          command: (event: IMenuItemCommandEvent) => {
          this.nonContactCodeGroupRemoveClick.emit(groupId);
          }
        });
      }

      menuEl.openMenu();
    }
  }

  openContextMenuForDay(event: MouseEvent, day: ITimetableDay, menuEl: any) {
    const selection: any[] = [];
    const periodsForDay = this.getPeriods(day).filter(p => !isBreak(p.periodCode) && p.id > 1);
    const periodIds = periodsForDay.map(p => p.id);
    const selectedId = this.selectedTeacherOrRoomControl.value;
    const sessions = this.sessions.filter(s => periodIds.includes(s.periodId!));
    const sessionIds = sessions.map(s => s.id);
    const nonContactCodeIds: number[] = this._nonContactCodesAssociations
      .filter(nc => periodIds.includes(nc.periodId!) && (this.isTeacherTimetable ? nc.staffId === selectedId : nc.roomId === selectedId))
      .map((n: any) => n.nonContactCodeAssociationId);
    selection.push(...periodsForDay.filter(p => p.isEmpty));

    this.menuList = [
      {
        label: this.translateService.instant('Remove'),
        children: [
          {
            label: this.translateService.instant('Session'),
            disabled: !sessionIds.length,
            command: (event: IMenuItemCommandEvent) => {
              if (this.isTeacherTimetable) {
                this.removeStaffClick.emit({ staffId: selectedId, sessionIds, sessions });
              } else {
                this.removeRoomClick.emit({ roomId: selectedId, sessionIds });
              }

            }
          },
          {
            label: this.translateService.instant('Non-Contact Code'),
            command: (event: IMenuItemCommandEvent) => {
              this.nonContactCodeRemoveClick.emit(nonContactCodeIds);
            }
          },
          {
            label: this.translateService.instant('All'),
            command: (event: IMenuItemCommandEvent) => {
              if (this.isTeacherTimetable) {
                this.removeStaffClick.emit({ staffId: selectedId, sessionIds, sessions });
              } else {
                this.removeRoomClick.emit({ roomId: selectedId, sessionIds });
              }
              this.nonContactCodeRemoveClick.emit(nonContactCodeIds);
            }
          }
        ]
      },
      {
        label: this.translateService.instant('Non-Contact Codes'),
        children: this.nonContactCodes!.filter(nc => this.isTeacherTimetable ? nc.isTeacher : nc.isRoom).map(nc => {
          return {
            label: nc.codeName,
            command: (event: IMenuItemCommandEvent) => {
              const nonContactCodeList: INonContactCodesAssociation[] = selection.map(s => {
                return {
                  nonContactCodeId: nc.id,
                  periodId: s.id,
                  roomId: (!this.isTeacherTimetable && selectedId) ? selectedId : undefined,
                  staffId: (this.isTeacherTimetable && selectedId) ? selectedId : undefined,
                  nonContactCodeAssociationId: undefined,
                  isLunch: false
                }
              })
              this.nonContactCodeClick.emit(nonContactCodeList);
              this.selection = [];
              this.selectedIds = [];
            }
          }
        })
      }
    ]

    menuEl.openMenu();
  }

  openContextMenuForPeriod(event: MouseEvent, period: ITimetableDayPeriod, week: ITimetableWeek, menuEl: any) {
    if (isBreak(period.periodCode)) {
      return;
    }
    const selection: any[] = [];
    const periods = week.days.map(d => d.periods).flat().filter(p => (p.periodDisplayName === period.periodDisplayName && !isBreak(p.periodCode) && p.id > 1));
    const periodIds = periods.map(p => p.id);
    const selectedId = this.selectedTeacherOrRoomControl.value;
    const sessions = this.sessions.filter(s => periodIds.includes(s.periodId!));
    const sessionIds = sessions.map(s => s.id);
    const nonContactCode = this._nonContactCodesAssociations
      .filter(nc => periodIds.includes(nc.periodId!) && (this.isTeacherTimetable ? nc.staffId === selectedId : nc.roomId === selectedId));
    const nonContactCodeIds = nonContactCode.map((n: any) => n.nonContactCodeAssociationId);
    const emptyCells = periods.filter(p => (this.getSessions(p)?.length === 0 && this.getNonContactCodes(p).length === 0));
    selection.push(...emptyCells);

    this.menuList = [
      {
        label: this.translateService.instant('Remove'),
        children: [
          {
            label: this.translateService.instant('Session'),
            disabled: !sessionIds.length,
            command: (event: IMenuItemCommandEvent) => {
              if (this.isTeacherTimetable) {
                this.removeStaffClick.emit({ staffId: selectedId, sessionIds, sessions });
              } else {
                this.removeRoomClick.emit({ roomId: selectedId, sessionIds });
              }
            }
          },
          {
            label: this.translateService.instant('Non-Contact Code'),
            command: (event: IMenuItemCommandEvent) => {
              this.nonContactCodeRemoveClick.emit(nonContactCodeIds);
            }
          },
          {
            label: this.translateService.instant('All'),
            command: (event: IMenuItemCommandEvent) => {
              if (this.isTeacherTimetable) {
                this.removeStaffClick.emit({ staffId: selectedId, sessionIds, sessions });
              } else {
                this.removeRoomClick.emit({ roomId: selectedId, sessionIds });
              }
              this.nonContactCodeRemoveClick.emit(nonContactCodeIds);
            }
          }
        ]
      },
      {
        label: this.translateService.instant('Non-Contact Codes'),
        children: this.nonContactCodes!.filter(nc => this.isTeacherTimetable ? nc.isTeacher : nc.isRoom).map(nc => {
          return {
            label: nc.codeName,
            command: (event: IMenuItemCommandEvent) => {
              const nonContactCodeList: INonContactCodesAssociation[] = selection.map(s => {
                return {
                  nonContactCodeId: nc.id,
                  periodId: s.id,
                  roomId: (!this.isTeacherTimetable && selectedId) ? selectedId : undefined,
                  staffId: (this.isTeacherTimetable && selectedId) ? selectedId : undefined,
                  nonContactCodeAssociationId: undefined,
                  isLunch: false
                }
              })
              this.nonContactCodeClick.emit(nonContactCodeList);
              this.selection = [];
              this.selectedIds = [];
            }
          }
        })
      }
    ]

    menuEl.openMenu();
  }

  openContextMenuForWeek(event: MouseEvent, week: ITimetableWeek, menuEl: any) {
    const selection: any[] = [];
    const periods = week.days.map(d => d.periods).flat().filter(p => !isBreak(p.periodCode) && p.id > 1);
    const periodIds = periods.map(p => p.id);
    const selectedId = this.selectedTeacherOrRoomControl.value;
    const sessions = this.sessions.filter(s => periodIds.includes(s.periodId!));
    const sessionIds = sessions.map(s => s.id);
    const nonContactCode = this._nonContactCodesAssociations
      .filter(nc => periodIds.includes(nc.periodId!) && (this.isTeacherTimetable ? nc.staffId === selectedId : nc.roomId === selectedId));
    const nonContactCodeIds = nonContactCode.map((n: any) => n.nonContactCodeAssociationId);
    const emptyCells = periods.filter(p => (this.getSessions(p)?.length === 0 && this.getNonContactCodes(p).length === 0));
    selection.push(...emptyCells);

    this.menuList = [
      {
        label: this.translateService.instant('Remove'),
        children: [
          {
            label: this.translateService.instant('Session'),
            disabled: !sessionIds.length,
            command: (event: IMenuItemCommandEvent) => {
              if (this.isTeacherTimetable) {
                this.removeStaffClick.emit({ staffId: selectedId, sessionIds, sessions });
              } else {
                this.removeRoomClick.emit({ roomId: selectedId, sessionIds });
              }
            }
          },
          {
            label: this.translateService.instant('Non-Contact Code'),
            command: (event: IMenuItemCommandEvent) => {
              this.nonContactCodeRemoveClick.emit(nonContactCodeIds);
            }
          },
          {
            label: this.translateService.instant('All'),
            command: (event: IMenuItemCommandEvent) => {
              if (this.isTeacherTimetable) {
                this.removeStaffClick.emit({ staffId: selectedId, sessionIds, sessions });
              } else {
                this.removeRoomClick.emit({ roomId: selectedId, sessionIds });
              }
              this.nonContactCodeRemoveClick.emit(nonContactCodeIds);
            }
          }
        ]
      },
      {
        label: this.translateService.instant('Non-Contact Codes'),
        children: this.nonContactCodes!.filter(nc => this.isTeacherTimetable ? nc.isTeacher : nc.isRoom).map(nc => {
          return {
            label: nc.codeName,
            command: (event: IMenuItemCommandEvent) => {
              const nonContactCodeList: INonContactCodesAssociation[] = selection.map(s => {
                return {
                  nonContactCodeId: nc.id,
                  periodId: s.id,
                  roomId: (!this.isTeacherTimetable && selectedId) ? selectedId : undefined,
                  staffId: (this.isTeacherTimetable && selectedId) ? selectedId : undefined,
                  nonContactCodeAssociationId: undefined,
                  isLunch: false
                }
              })
              this.nonContactCodeClick.emit(nonContactCodeList);
              this.selection = [];
              this.selectedIds = [];
            }
          }
        })
      }
    ]

    menuEl.openMenu();
  }

  getPeriodHeaders(week: ITimetableWeek) {
    this.dayWithMaxPeriods = week.days.reduce((maxDay, currentDay) => {
      return currentDay.periods.length > maxDay.periods.length ? currentDay : maxDay;
    }, week.days[0]);
    return this.dayWithMaxPeriods.periods.filter(period => period.periodCode !== 'SESSION').map(p => {
      return { ...p, name: p.periodDisplayName, isBreak: isBreak(p.periodCode), isLunch: p.isLunch }
    });
  }

  getDays(week: ITimetableWeek) {
    return week.days.map(day => {
      return {
        ...day,
        key: `W${week.weekNumber}-D${day.dayOfWeek}`,
        name: day.dayDisplayName,
        periods: day.periods.filter(p => p.periodCode !== 'SESSION').map((period: ITimetableDayPeriod, index: number) => {
          return {
            ...period,
            key: `W${week.weekNumber}-D${day.dayOfWeek}-P${index + 1}`,
            name: period.periodDisplayName,
            refId: period.id
          }
        })
      }
    });
  }

  identify(index: any, item: any) {
    return index;
  }

  getMaxSessionHeight(day: ITimetableDay) {
    const maxSessionHeight = Math.max(...day.periods.map(period => {
      const sessionCount = this.getSessions(period)?.length || 1;
      return sessionCount * 52;
    }));
    return `${maxSessionHeight}px`;
  }

  getPeriods(day: ITimetableDay) {
    const missingPeriods = this.dayWithMaxPeriods.periods.filter(period => {
      return !day.periods.find(p => p.periodDisplayName === period.periodDisplayName);
    }).map(p => ({
      ...p,
      id: -1
    }));
    day.periods.push(...missingPeriods);
    const missingPeriodNames = missingPeriods.map(p => p.periodDisplayName);
    return day.periods.map(p => {
      if (missingPeriodNames.includes(p.periodDisplayName)) {
        return {
          ...p,
          isBreak: isBreak(p.periodCode),
          hasSession: false,
          hasNonContactCode: false,
          isEmpty: false,
          isGreyedOut: true
        }
      } else {
        return {
          ...p,
          isBreak: isBreak(p.periodCode),
          rowHeight: this.getSessions(p)?.length > 0 ? this.getSessions(p)?.length * 52 : 52,
          hasSession: this.getSessions(p)?.length !== 0,
          hasNonContactCode: this.getNonContactCodes(p).length !== 0,
          isEmpty: (this.getSessions(p)?.length === 0 && this.getNonContactCodes(p).length === 0),
          isGreyedOut: false
        }
      }
    })
  }

  getSessions(period: ITimetableDayPeriod) {
    return this.sessions.filter(s => s.periodId === period.id).map(session => {
      return {
        ...session,
        sessionRoomName: this._rooms.find((room) => room.id === session.roomId)?.code,
        sessionStaffName: this._staffs.find((staff) => staff.id === session.mainStaffId)?.code
      }
    });
  }

  getNonContactCodes(period: ITimetableDayPeriod) {
    return this._nonContactCodesAssociations.filter(n => {
      if (this.isTeacherTimetable) {
        return n.staffId === this.selectedTeacherOrRoomControl.value && n.periodId === period.id
      } else {
        return n.roomId === this.selectedTeacherOrRoomControl.value && n.periodId === period.id
      }
    }).map(nc => {
      return { ...nc, codeName: this._nonContactCodes?.find(code => code.id === nc.nonContactCodeId)?.codeName,
         roomId: this.isTeacherTimetable && nc.nonContactCodeGroupId ? this._nonContactCodesAssociations.find(nca => nca.nonContactCodeGroupId === nc.nonContactCodeGroupId && nca.staffId == null && nca.roomId != null)?.roomId : nc.roomId }
    });
  }

  getColor(session: ISessionWithBlocks): string {
    const subjectId = session.block.subjectToYearGroups?.find(group => group.id === session.subjectToYearGroupId)?.subjectId || null;
    let backgroundColor = this.subjects?.find((subject) => subject.id === subjectId)?.color || defaultBackground;
    this.textColor = isColorDark(backgroundColor) ? '#FFF' : '#000';
    if (!backgroundColor.startsWith('#')) {
      backgroundColor = '#' + backgroundColor;
    }
    return backgroundColor;
  }

  getSubject(session: ISessionWithBlocks) {
    const subjectId = session.block.subjectToYearGroups?.find(group => group.id === session.subjectToYearGroupId)?.subjectId || null;
    const subjectName = this.subjects?.find((subject) => subject.id === subjectId)?.name;
    return subjectName;
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
