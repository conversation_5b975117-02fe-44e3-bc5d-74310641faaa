<mat-accordion [displayMode]="'flat'" #accordion>
  <mat-expansion-panel (opened)="panelOpenState = true; adjustPopupPosition()"
                       (closed)="panelOpenState = false"
                       [expanded]="panelOpenState"
                       [class.new-row]="params.data.id === 'newRowId'"
                       hideToggle>
    <mat-expansion-panel-header [expandedHeight]="'48px'">
      <mat-panel-title>
        <div *ngIf="!selectedItemTexts.length" class="title-text">{{placeholder}}</div>

        <div class="list-items"
             *ngIf="selectedItemTexts.length"
             #tooltip="matTooltip"
             [matTooltipDisabled]="!selectedItemTexts.length"
             [matTooltip]="selectedItemTexts.join('\n')"
             [matTooltipPosition]="'left'">
          <bcm-chip *ngFor="let text of selectedItemTexts" color="{{color}}" class="chip">{{text}}</bcm-chip>
        </div>
      </mat-panel-title>
      <mat-panel-description>
        <bcm-icon class="icon" [class.is-open]="panelOpenState === true" icon="far fa-angle-down"></bcm-icon>
      </mat-panel-description>
    </mat-expansion-panel-header>

    <div *ngIf="panelOpenState === true" class="block-vertical filter-popup" id="selectItems" #container [ngStyle]="dynamicStyles">
      <div class="filter-header">
        <span class="filter-icon">
            <i class="far fa-filter"></i>
        </span>
      </div>
      <div class="search">
        <bromcom-input-field [formControl]="searchControl"
                             [icon]="'fal fa-search'"
                             [iconPosition]="'prefix'"
                             [placeholder]="'Search' | translate"
        ></bromcom-input-field>
      </div>
      <div class="filter-options">
        <span (click)="selectAll()">{{ 'Select All' | translate }}</span>
        <span (click)="clearAll()">{{ 'Clear All' | translate }}</span>
      </div>
      <div class="vertical" [formGroup]="filterGroup">
        <ng-container formArrayName="items">
          <bromcom-checkbox *ngFor="let item of data; let i = index" class="data-item"
                            [text]="data[i].text"
                            [formControlName]="i" [color]="data[i].color"></bromcom-checkbox>
        </ng-container>
      </div>
      <div class="filter-buttons">
        <bcm-button class="button" kind="ghost" (click)="onCancel()">
          {{'Cancel' | translate}}
        </bcm-button>
        <bcm-button class="button apply-btn" (click)="onApply()">
          {{'Apply' | translate}}
        </bcm-button>
      </div>
    </div>
  </mat-expansion-panel>
</mat-accordion>
