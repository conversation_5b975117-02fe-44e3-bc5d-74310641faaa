<ng-container>
        <div class="auto-schedule-issues-container">
            <div class="action-bar">
                <bromcom-input-field class="search-field" [formControl]="searchControl" [icon]="'fal fa-search'"
                    [placeholder]="'Search' | translate "></bromcom-input-field>
                <bcm-button class="export-schedule-button" icon="far fa-file-export" kind="ghost" [disabled]="!gridData || !gridData.length" (click)="onExport()">{{'Export'
                    | translate }}
                </bcm-button>
            </div>

            <div class="table-container">
                <ag-grid-angular style="width: 100%; height:100%;" class="ag-theme-alpine" domLayout="normal"
                    [gridOptions]="gridOptions" (gridReady)="onGridReady($event)">
                </ag-grid-angular>
            </div>

          <div class="rerun-auto-schedule-box">
            <div class="info">
              <div class="info-icon">
                <img src="assets/images/info-icon.png" alt="">
              </div>
              <div class="text">
                {{'Review the actions and accept if required, then update to rerun auto-scheduling, this will return you to Feedback page.' | translate}}
              </div>
            </div>
            <bcm-button kind="link"  icon="far fa-redo-alt" (click)="rerunAutoSchedule()">
              {{'Update' | translate}}
            </bcm-button>
          </div>
        </div>
</ng-container>

<ng-container *ngIf="isOpen">
  <bcm-modal size="large"
             class="auto-schedule-reasons-modal"
             (bcm-modal-before-close)="onClose()"
             #autoScheduleReasonModal>
    <bcm-modal-header>
      <div>{{'See Reasons of Issue - ' | translate}}{{ openedSessionName }}</div>
    </bcm-modal-header>

    <div class="modal-body">
      <div class="expanded-container">
        <ag-grid-angular style="width: 100%; height:100%;" class="ag-theme-alpine" domLayout="normal"
            [gridOptions]="reasonsGridOptions" (gridReady)="onReasonsGridReady($event)">
        </ag-grid-angular>
      </div>
    </div>

    <bcm-modal-footer class="footer">
      <bcm-button kind="ghost" (click)="onClose()">{{'Close' | translate}}</bcm-button>
    </bcm-modal-footer>
  </bcm-modal>
</ng-container>
