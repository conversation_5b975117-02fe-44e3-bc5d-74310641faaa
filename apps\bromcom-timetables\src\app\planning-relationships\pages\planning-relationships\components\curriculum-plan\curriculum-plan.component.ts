import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  Output,
  Renderer2,
  ViewChild
} from '@angular/core';
import { STAFFING_VIEW_TYPES } from '../../../../../_shared/enums/StaffingViewTypes';
import { RelationshipsService } from '../../../../services/relationships.service';
import { LoadingSpinnerService, SnackbarService } from '@bromcom/ui';
import { distinctUntilChanged, finalize, forkJoin, Observable, Subject, switchMap, takeUntil } from 'rxjs';
import { CurriculumPlanService } from '../../../../services/curriculum-plan.service';
import { FormControl, Validators } from '@angular/forms';
import { IYearGroup } from '../../../../../_shared/models/IYearGroup';
import { PlanningRelationshipsService } from '../../../../services/planning-relationships.service';
import { BaseTwoPaneComponent } from '../common/two-pane.component';
import { ICurriculumPlanBlock } from "../../../../../_shared/models/ICurriculumPlanBlock";
import { StaffService } from "../../../../../projects/services/staff.service";
import { IStaff } from "../../../../../_shared/models/IStaff";
import { BLOCK_TYPE } from "../../../../../_shared/enums/BlockType";
import { IRooms } from "../../../../../_shared/models/IRooms";
import { RoomsService } from "../../../../../projects/services/rooms.service";
import { MatMenuPanel } from '@angular/material/menu';
import { CurriculumPlanBlocksService } from '../../../../services/curriculum-plan-blocks';
import { scrollToElement, updateLastVisibleIndexForBlocks } from "./_shared/base-helper";
import { TranslateService } from "@ngx-translate/core";
import { ICurriculumSessions } from '../../../../../_shared/models/ICurriculumSessions';
import { DeleteConfirmationComponent } from '../common/delete-confirmation/delete-confirmation.component';
import { equals } from 'rambda';
import { IClassCodes } from "../../../../../_shared/models/IClassCodes";
import { ClassNameDefinitionService } from "../../../../services/class-name-definition.service";
import { CellValueChangedEvent, ColDef, GridReadyEvent } from "ag-grid-community";
import { GridApi } from "ag-grid-community/dist/lib/gridApi";
import { CellClassParams } from "ag-grid-community/dist/lib/entities/colDef";
import { ClassNameDefinitions } from "../../../../../_shared/enums/ClassNameDefinitions";
import { IClassNameItems } from "../../../../../_shared/models/IClassNameItems";
import { CLASS_NAME_TYPE } from '../../../../../_shared/enums/ClassNameType';
import { CustomHeaderComponent } from "./custom-header.component";
import { ISession } from "../../../../../_shared/models/ISession";
import {
  CheckForMissingStaffRoomComponent
} from './components/check-for-missing-staff-room/check-for-missing-staff-room.component';
import { NotesComponent } from '../planning-relationship-actions/notes/notes.component';
import { ICurriculumPlanStaff } from '../../../../../_shared/models/ICurriculumPlanStaff';
import { GeneralModalComponent } from '../../../../../_shared/components/general-modal/general-modal.component';
import { ISwapStaffDataEntity } from '../../../../../_shared/models/ISwapStaffDataEntity';
import { SwapDataModalComponent } from '../common/swap-data-modal/swap-data-modal.component';
import { IConflict } from '../../../../../_shared/models/IConflict';
import {
  CurriculumPlanSubjectsComponent
} from './components/curriculum-plan-subjects/curriculum-plan-subjects.component';
import { CurriculumPlanStaffComponent } from './components/curriculum-plan-staff/curriculum-plan-staff.component';
import { CurriculumPlanRoomsComponent } from './components/curriculum-plan-rooms/curriculum-plan-rooms.component';
import { ICurriculumPlanRoom } from '../../../../../_shared/models/ICurriculumPlanRoom';
import { SchedulingService } from '../../../../services/scheduling.service';
import { LinkBlocksModalComponent } from './components/link-blocks-modal/link-blocks-modal.component';
import { IListOption, customComparator } from "@bromcom/core";
import {
  CurriculumPlanAutoAssignComponent
} from "./components/curriculum-plan-auto-assign/curriculum-plan-auto-assign.component";
import {
  CurriculumPlanContainerComponent
} from "./components/curriculum-plan-container/curriculum-plan-container.component";
import { BandService } from '../../../../services/band.service';
import { IDisplayTimetableData } from '../../../../../_shared/models/IDisplayTimetableData';
import {
  DisplayTimetableModalComponent
} from '../scheduling/components/display-timetable-modal/display-timetable-modal.component';
import { IListOfBlocksResponse } from '../../../../../_shared/models/IListOfBlocksResponse';
import { NonContactCodesService } from '../../../../../projects/services/non-contact-codes.service';
import { INonContactCodes } from '../../../../../_shared/models/INonContactCodes';
import { INonContactCodesAssociation } from '../../../../../_shared/models/INonContactCodesAssociation';
import { NewTimetableService } from '../../../../../timetables/services/new-timetable.service';
import { IWithPeriodStructureResponse } from '../../../../../_shared/models/IWithPeriodStructureResponse';
import { ISessionWithBlocks } from '../../../../../_shared/models/ISessionWithBlocks';
import { IRemoveRoomFromSession } from '../../../../../_shared/models/IRemoveRoomFromSession';
import { IRemoveStaffFromSession } from '../../../../../_shared/models/IRemoveStaffFromSession';
import { ISwapRoomDataEntity } from '../../../../../_shared/models/ISwapRoomDataEntity';
import { IMenuItem } from '../../../../../_shared/models/IMenuItem';
import { ISubjectToYearGroup } from '../../../../../_shared/models/ISubjectToYearGroup';
import { ISwapClassRequest } from '../../../../../_shared/models/ISwapClassRequest';
import { SwapDataService } from '../../../../services/swap-data.service';
import { SWAP_CLASS_OPTIONS } from '../../../../../_shared/enums/SwapClassMenuOptions';
import {
  CurriculumPlanOverviewModalComponent
} from './components/curriculum-plan-overview/curriculum-plan-overview.component';
import { IImportOptionPatterns } from '../../../../../_shared/models/IImportOptionPatterns';
import { YearGroupService } from "../../../../../projects/services/year-group.service";
import { SubstituteStaffModalComponent } from '../common/substitute-staff-modal/substitute-staff-modal.component';
import { ISubstituteStaffDataEntity } from '../../../../../_shared/models/ISubstituteStaffDataEntity';
import { IPeriodIndexDetails } from '../../../../../_shared/models/IPeriodIndexDetails';
import {COUNTRY} from "../../../../../_shared/enums/Country";

@Component({
  selector: 'bromcom-curriculum-plan',
  templateUrl: './curriculum-plan.component.html',
  styleUrls: ['./curriculum-plan.component.scss']
})
export class CurriculumPlanComponent extends BaseTwoPaneComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('classContextMenu') classContextMenuRef!: MatMenuPanel;
  @ViewChild('deleteClassConfirmationComponent') deleteClassConfirmationComponent!: DeleteConfirmationComponent;
  @ViewChild('periodMenu') periodMenuRef!: MatMenuPanel;
  @ViewChild('deletePeriodConfirmationComponent') deletePeriodConfirmationComponent!: DeleteConfirmationComponent;
  @ViewChild('sessionMenu') sessionMenuRef!: MatMenuPanel;
  @ViewChild('deleteSessionClassConfirmationComponent') deleteSessionClassConfirmationComponent!: DeleteConfirmationComponent;
  @ViewChild('subjectMenu') subjectMenuRef!: MatMenuPanel;
  @ViewChild('notesPanelComponent') notesPanelComponent!: NotesComponent;
  @ViewChild('autoAssignModal') autoAssignModalRef!: CurriculumPlanAutoAssignComponent;
  @ViewChild('planContainer') planContainer!: CurriculumPlanContainerComponent;
  @ViewChild('classMenu') classMenuRef!: MatMenuPanel;
  @ViewChild('swapSubjectsConfirmationComponent') swapSubjectsConfirmationComponent: DeleteConfirmationComponent | undefined;

  @ViewChild('subjectsSidePanel') subjectsSidePanel!: CurriculumPlanSubjectsComponent;
  @ViewChild('editSubjectsModal') editSubjectsModal!: ElementRef;
  @ViewChild('staffSidePanel') staffSidePanel!: CurriculumPlanStaffComponent;
  @ViewChild('editStaffsModal') editStaffsModal!: ElementRef;
  @ViewChild('roomSidePanel') roomSidePanel!: CurriculumPlanRoomsComponent;
  @ViewChild('editRoomsModal') editRoomsModal!: ElementRef;
  @ViewChild('editClassCodesModal') editClassCodesModalRef!: ElementRef;
  @ViewChild('checkForMissingComponent') checkForMissingComponent!: CheckForMissingStaffRoomComponent;
  @ViewChild('warningModalPopup') warningModalPopup!: GeneralModalComponent;
  @ViewChild('linearUnScheduleWarningModalPopup') linearUnScheduleWarningModalPopup!: GeneralModalComponent;
  @ViewChild('swapDataModal') swapDataModal!: SwapDataModalComponent;
  @ViewChild('linkBlocksModalComponent') linkBlocksModalComponent!: LinkBlocksModalComponent;
  @ViewChild('clearCurriculumWarningModal') clearCurriculumWarningModal!: GeneralModalComponent;
  @ViewChild('displayTimetableComponent') displayTimetableComponent!: DisplayTimetableModalComponent;
  @ViewChild('swapClassWarningModalPopup') swapClassWarningModalPopup!: GeneralModalComponent;
  @ViewChild('linkedPeriodExceedsTotalWarningModalPopup') linkedPeriodExceedsTotalWarningModalPopup!: GeneralModalComponent;
  @ViewChild('overviewModal') overviewModal!: CurriculumPlanOverviewModalComponent;
  @ViewChild('importOptionPatternsWarningModal') importOptionPatternsWarningModal!: GeneralModalComponent;
  @ViewChild('substituteStaffModal') substituteStaffModal!: SubstituteStaffModalComponent;

  @Input() projectName!: string;
  @Input() timetableName!: string;
  @Input() projectId!: number;

  @Output() changeToCurriculumTab = new EventEmitter<any>();
  @Output() changeToSchedulingTab = new EventEmitter<any>();

  selectYearGroups: IListOption[] = [];
  timetableId!: number;
  isEditSubjects = false;
  isEditStaff = false;
  isEditRooms = false;
  disableClearCurriculum = false;
  actionsMenu: IMenuItem[] = [];

  resetSelectedPeriodIds$ = new Subject<void>();
  resetSelectedClassIds$ = new Subject<void>();
  setScrollToPeriodIndex$ = new Subject<IPeriodIndexDetails>();
  classCodeRows: IClassCodes[] = [];
  data!: any;
  selectedYearGroup!: number;
  periodMenuData!: any;
  sessionMenuData!: any;
  subjectMenuData!: any;
  classMenuData!: any;
  classOptionsLength = Array(10);
  additionalStaff: string[] = [];
  selectedSessionIndexes: number[] = [];
  disableTeacherAndRoom = false;
  disableTeacher = false;
  disableAdditionalTeacher = false;
  disableRoom = false;
  isScheduled = false;
  disableClear = false;
  isLocked = false;
  isDoubleSelected = false;
  disableRemoveClass = false;
  disableSwapSubjects = false;
  disableComplexOptionOnlyFreeSession = false;
  BLOCK_TYPE = BLOCK_TYPE;
  viewType = STAFFING_VIEW_TYPES.Subject;
  viewTypes = STAFFING_VIEW_TYPES;
  selectedYearGroupControl = new FormControl<number | null>(null);
  yearGroups: IYearGroup[] = [];
  staffs: IStaff[] = [];
  rooms: IRooms[] = [];
  isExpanded = false;
  currentSelectedBlock: ICurriculumPlanBlock | undefined | null;
  columnDefs: ColDef[] = [];
  selectedCells: { colIndex: number, rowIndex: number }[] = []
  editClassCodeForm = new FormControl();
  selectedSessionIds: number[] = [];
  editClassCodeFormMaxLength = 15;
  shiftKeyPressed = false;
  components: { [p: string]: any; } = { agColumnHeader: CustomHeaderComponent };
  isSubjectDraggingActive = false;
  selectedPeriodIndexes: number[] = [];
  selectedSubjectId: number | undefined;
  field!: ClassNameDefinitions;
  missingStaffRoomSessionId: number | null = null;
  staffToSessionActiveId: number | null = null;
  roomToSessionActiveId: number | null = null;
  conflictList: IConflict[] = [];
  selectedStaffData!: ISwapStaffDataEntity;
  staffsData: ISwapStaffDataEntity[] = [];
  roomsData: ISwapRoomDataEntity[] = [];
  selectedRoomData!: ISwapRoomDataEntity;
  warningModalTitle!: string;
  warningModalMain = this.translate.instant('Please resolve or accept the conflicts for the swap to be available.');
  importOptionsMessage!: string;
  displayTimetableSelectedData!: IDisplayTimetableData;
  blocksData: IListOfBlocksResponse[] | null = [];
  isTeacherTimetable!: boolean;
  displayTimetableData: IStaff[] | IRooms[] = [];
  nonContactCodes!: INonContactCodes[];
  nonContactCodesAssociations: INonContactCodesAssociation[] = [];
  periodStructure!: IWithPeriodStructureResponse;
  openedModal: 'Subject' | 'Staff' | 'Room' | 'EditClassCodes' | null = null;
  isSwapStaffView!: boolean;
  combineSessionsData!: { blockId: number, subjectToYearGroupId: number, periodIndexes: number[] }
  selectedClassIndexes: number[] = [];
  showMenu!: boolean;
  allowSwapMainStaff = false;
  allowSwapRoom = false;
  allowSwapAdditionalStaff = false;
  allowSwapAllAllocations = false;
  freeSubjectId!: number | null;
  swapClassMenuOptions = SWAP_CLASS_OPTIONS;
  blockForSwap!: ICurriculumPlanBlock;
  swapClassRequest: ISwapClassRequest = {
    classIndexes: [],
    subjectId: null,
    subjectToYearGroupIds: null,
    swapMainStaff: false,
    swapAdditionalStaff: false,
    swapRoom: false
  };
  swapClassWarningModalTitle!: string;
  swapClassWarningModalMain!: string;
  importOptionsPattern: string[] = ['9', '10', '12'];
  misYearGroups: IListOption[] = [];
  message = "";
  country: COUNTRY = COUNTRY.England;
  canLink = false;
  canUnlink = false;
  canUnlinkAll = false;

  readonly ClassNameDefinitions = ClassNameDefinitions;
  readonly unsubscribe$: Subject<void> = new Subject();
  private classNameDefinitions: IClassNameItems[] | undefined;
  private gridApi!: GridApi<{ [key: string]: string }>;
  private selectedSessions: IClassCodes[] = [];
  private colIndex: number | undefined;
  private readonly READONLY_IDS: ClassNameDefinitions[] = [
    ClassNameDefinitions.Subject,
    ClassNameDefinitions.YearGroup,
    ClassNameDefinitions.ExamLevels,
    ClassNameDefinitions.StaffCode,
    ClassNameDefinitions.Separators,
    ClassNameDefinitions.Space,
    ClassNameDefinitions.Dash
  ];
  private clickType: string | undefined;
  private clickedHeaderColIndex = '';


  @HostListener('document:keydown', ['$event'])
  handleKeyDown(event: KeyboardEvent) {
    if (event.shiftKey) {
      this.shiftKeyPressed = true;
    }
  }

  @HostListener('document:keyup', ['$event'])
  handleKeyUp(event: KeyboardEvent) {
    if (!event.shiftKey) {
      this.shiftKeyPressed = false;
    }
  }

  constructor(
    private planningRelationShipsService: PlanningRelationshipsService,
    private curriculumPlanBlocks: CurriculumPlanBlocksService,
    private planningRelationships: PlanningRelationshipsService,
    private bandService: BandService,
    private relationships: RelationshipsService,
    private staffService: StaffService,
    private roomService: RoomsService,
    private translate: TranslateService,
    private snackbar: SnackbarService,
    private classNameDefinitionService: ClassNameDefinitionService,
    private curriculumPlan: CurriculumPlanService,
    private loading: LoadingSpinnerService,
    private cdk: ChangeDetectorRef,
    renderer: Renderer2,
    private translationService: TranslateService,
    private timetableService: NewTimetableService,
    private swapService: SwapDataService,
    public schedulingService: SchedulingService,
    public nonContactCodesService: NonContactCodesService,
    private translateService: TranslateService,
    private yearGroupService: YearGroupService
  ) {
    super(renderer);
  }

  ngOnInit(): void {
    this.actionsMenu = [
      {
        label: this.translate.instant('Overview'),
        value: 1,
        command: () => this.openOverviewsModal(),
        icon: 'far fa-eye'
      },
      {
        label: this.translate.instant('Check for Missing Staff/Rooms'),
        value: 2,
        command: () => this.openCheckForMissingStaffRoom()
      },
      {
        label: this.translate.instant('Auto Assign'),
        value: 3,
        command: () => this.autoAssign(),
        icon: 'far fa-user-plus'
      },
      {
        label: this.translate.instant('Clear Curriculum Plan'),
        value: 4,
        command: () => this.openClearCurriculum(),
        icon: 'far fa-minus-circle'
      },
      {
        label: this.translate.instant('Import Options'),
        value: 5,
        command: () => this.importOptions()
      }
    ] as IMenuItem[];

    this.planningRelationShipsService.timetableId$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(timetableId => {
        if (timetableId) {
          this.timetableId = timetableId
        }
      });

    this.schedulingService.getListOfBlocks(this.timetableId)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(blocksData => {
        if (blocksData) {
          this.blocksData = blocksData;
          this.actionsMenu.find(item => item.value === 4)!.disabled = !blocksData.length;
          const disableItem = !blocksData.length || blocksData.every(block => block.subjectToYearGroups[0].subjectId == null);
          this.actionsMenu.find(item => item.value === 3)!.disabled = disableItem;
        }
      });

    this.planningRelationships.getCountry().subscribe(countryResponse => {
      this.country = countryResponse.country;
      this.importOptionsPattern = this.country === COUNTRY.NorthernIreland ? ['10', '11', '13'] : ['9', '10', '12'];

      this.yearGroupService.getListOfNCYearGroupOptions().subscribe(res => {
        this.misYearGroups = res;
        if (!this.importOptionsPattern.includes(this.getSelectedMISYearGroup()[0])) {
          this.actionsMenu.find(item => item.value === 5)!.disabled = true;
        }
      });
    })

    this.nonContactCodesService.getList(this.projectId).subscribe(response => {
      this.nonContactCodes = response;
    });

    window.addEventListener("contextmenu", e => e.preventDefault());

    this.curriculumPlanBlocks.isSubjectDraggingActive$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe(isSubjectDraggingActive => {
        this.isSubjectDraggingActive = isSubjectDraggingActive
      })

    this.curriculumPlan.selectedYearGroup$
      .pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$))
      .subscribe(selectedYearGroup => {
        if (selectedYearGroup) {
          this.selectedYearGroup = selectedYearGroup;
          this.selectedYearGroupControl.setValue(selectedYearGroup, { emitEvent: false });
        }
      })

    this.planningRelationships.yearGroupsData$
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe((yearGroups) => {
        this.yearGroups = yearGroups
          .filter((yearGroup) => !yearGroup.isExcluded)
          .map((yearGroup) => {
            return { ...yearGroup, text: yearGroup.description };
          })
          .sort((a, b) => Number(b.name) - Number(a.name));

        this.selectYearGroups = [];
        this.yearGroups.map((yearGroup) => {
          if (yearGroup.id) {
            this.selectYearGroups.push({
              id: yearGroup.id,
              text: yearGroup.name
            } as any);
          }
        })
        this.curriculumPlan.selectedYearGroup$.next(this.selectedYearGroup ?? this.yearGroups[0]?.id);
        this.selectedYearGroupControl.setValue(this.selectedYearGroup ?? this.yearGroups[0]?.id, { emitEvent: false });
        this.planningRelationships.getConflictsList(this.timetableId).subscribe((conflicts) => this.planningRelationships.conflictsData$.next(conflicts));
        this.nonContactCodesService.getAssociations(this.timetableId).subscribe();
        this.bandService.getListOfBandsByYearGroup(this.timetableId, this.selectedYearGroup).subscribe();
        this.schedulingService.getSubjectList(this.projectId).subscribe();
        this.curriculumPlanBlocks.getAllBlocks(this.timetableId).subscribe(allBlocks => this.disableClearCurriculum = !allBlocks.length);
        this.staffService.getList(this.projectId).subscribe(res => {
          this.staffs = res;
        });
        this.roomService.getList(this.projectId).subscribe(res => {
          this.rooms = res;
        });
      });

    this.nonContactCodesService.fetchedAssociations$
      .pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$))
      .subscribe(nonContactCodesAssociations => {
        this.nonContactCodesAssociations = nonContactCodesAssociations
      });

    this.timetableService.getPeriodStructureByTimetableId(this.timetableId).subscribe((periodStructure) => {
      this.periodStructure = this.markPeriodsAndRemoveAMPMPeriods(periodStructure);
    });

    this.curriculumPlan.isExpanded$
      .pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$))
      .subscribe(val => {
        this.isExpanded = val.isExpanded;

        if (val.sessionId) {
          scrollToElement(val.sessionId);
          this.curriculumPlan.isExpanded$.next({ isExpanded: val.isExpanded, sessionId: null })
        }
      });

    this.curriculumPlanBlocks.missingStaffRoomSessionId$
      .pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$))
      .subscribe(missingStaffRoomSessionId => {
        this.missingStaffRoomSessionId = missingStaffRoomSessionId;
      });

    this.selectedYearGroupControl.valueChanges
      .pipe(
        distinctUntilChanged(),
        takeUntil(this.unsubscribe$))
      .subscribe((selectedYearGroup) => {
        this.curriculumPlan.isExpanded$.next({ isExpanded: false, sessionId: null });
        this.curriculumPlan.selectedYearGroup$.next(selectedYearGroup);
        this.curriculumPlanBlocks.selectedBlockId$.next(null);
        this.relationships.staffToSessionActiveId$.next(null);
        this.relationships.roomToSessionActiveId$.next(null);
        this.onCancelBandUpdate();

        if (!this.importOptionsPattern.includes(this.getSelectedMISYearGroup()[0])) {
          this.actionsMenu.find(item => item.value === 5)!.disabled = true;
        } else {
          this.actionsMenu.find(item => item.value === 5)!.disabled = false;
        }
      });

    this.curriculumPlan.currentSelectedBlock$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(selected => {
        this.currentSelectedBlock = selected;
      })

    this.curriculumPlan.clickedHeaderColIndex$.pipe(takeUntil(this.unsubscribe$)).subscribe(clickedHeaderColIndex => {
      this.clickedHeaderColIndex = clickedHeaderColIndex;
    })

    this.curriculumPlanBlocks.scrollToSelectedStaffId$.subscribe(id => {
      if (id) {
        this.viewType = this.viewTypes.Staff;
        scrollToElement(id);
      }
    })

    this.curriculumPlanBlocks.scrollToSelectedRoomId$.subscribe(id => {
      if (id) {
        this.viewType = this.viewTypes.Room;
        scrollToElement(id);
      }
    })

    this.curriculumPlanBlocks.stateChanged$
      .pipe(
        takeUntil(this.unsubscribe$),
        switchMap(() => forkJoin([
          this.bandService.getListOfBandsByYearGroup(this.timetableId, this.selectedYearGroup),
          this.curriculumPlanBlocks.getAllBlocks(this.timetableId),
          this.planningRelationships.getConflictsList(this.timetableId),
          this.schedulingService.getListOfBlocks(this.timetableId)
        ]))
      )
      .subscribe(([bands, allBlocks, conflicts, blocks]) => {
        this.disableClearCurriculum = !allBlocks.length;
        this.planningRelationships.conflictsData$.next(conflicts);
        this.blocksData = blocks
        this.actionsMenu.find(item => item.value === 4)!.disabled = !blocks.length;
        const disableItem = !blocks.length || blocks.every(block => block.subjectToYearGroups[0].subjectId == null);
        this.actionsMenu.find(item => item.value === 3)!.disabled = disableItem;
      });


    this.relationships.staffToSessionActiveId$
      .pipe(takeUntil(this.unsubscribe$), distinctUntilChanged())
      .subscribe(staffToSessionActiveId => {
        this.staffToSessionActiveId = staffToSessionActiveId;
        if (staffToSessionActiveId) {
          this.switchViewType(this.viewTypes.Staff);
          this.isLeftBoxVisible = true;
        }
        this.cdk.detectChanges();
      })

    this.relationships.roomToSessionActiveId$
      .pipe(takeUntil(this.unsubscribe$), distinctUntilChanged())
      .subscribe(roomToSessionActiveId => {
        this.roomToSessionActiveId = roomToSessionActiveId;
        if (roomToSessionActiveId) {
          this.switchViewType(this.viewTypes.Room);
          this.isLeftBoxVisible = true;
        }
        this.cdk.detectChanges();
      })

    this.schedulingService.getDepartmentList(this.projectId).subscribe();

    this.planningRelationships.conflictsData$.subscribe(conflictList => {
      this.conflictList = conflictList?.conflicts ?? [];
    });

    this.relationships.freeSubjectId$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(id => {
        this.freeSubjectId = id;
      });

    this.refreshData();
  }


  ngAfterViewInit(): void {
    this.cdk.detectChanges();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  openNotes() {
    this.notesPanelComponent.show();
  }

  sidePanelChange() {
    this.subjectsSidePanel.searchControl.setValue(null);
    this.staffSidePanel.searchControl.setValue(null);
    this.roomSidePanel.searchControl.setValue(null);
  }

  switchViewType(type: STAFFING_VIEW_TYPES): void {
    this.viewType = type;
  }

  editSubjects(show: boolean): void {
    if (show) {
      this.openedModal = 'Subject';
      setTimeout(() => {
        this.isEditSubjects = true;
        this.editSubjectsModal.nativeElement.show();
      }, 100);
    } else {
      this.openedModal = null;
    }
  }

  onToggleRightBox(): void {
    this.isLeftBoxVisible = !this.isLeftBoxVisible;
  }

  onBackToSubjects(): void {
    this.isEditSubjects = false;
    this.editSubjectsModal.nativeElement.hide();
    this.relationships
      .getSubjectsList(this.projectId)
      .pipe(
        switchMap(() => this.schedulingService.getDepartmentList(this.projectId)))
      .subscribe(() => {
        this.curriculumPlanBlocks.stateChanged$.next();
        if(this.editSubjectsModal){
          this.editSubjectsModal.nativeElement.hide();
        }
        this.openedModal = null;
      });
  }

  editStaffs(show: boolean): void {
    if (show) {
      this.openedModal = 'Staff';
      setTimeout(() => {
        this.isEditStaff = true;
        this.editStaffsModal.nativeElement.show();
      }, 100);
    } else {
      this.openedModal = null;
    }
  }

  displayTimetable(data: ICurriculumPlanStaff | ICurriculumPlanRoom): void {
    if (data && (data as ICurriculumPlanStaff)?.staffId != null) {
      this.displayTimetableSelectedData = {
        selectedId: (data as ICurriculumPlanStaff)?.staffId,
        selectedData: this.staffs!.find(s => s.id === (data as ICurriculumPlanStaff).staffId)!,
        isTeacherTimetable: true
      };
    } else if (data) {
      this.displayTimetableSelectedData = {
        selectedId: (data as ICurriculumPlanRoom)?.id,
        selectedData: this.rooms!.find(s => s.id === data.id)!,
        isTeacherTimetable: false
      };
    }
    this.isTeacherTimetable = this.displayTimetableSelectedData.isTeacherTimetable;
    if (this.isTeacherTimetable) {
      this.displayTimetableData = this.staffs.sort((a, b) => customComparator(a.lastName, b.lastName));
    } else {
      this.displayTimetableData = this.rooms.sort((a, b) => customComparator(a.name, b.name));
    }
    this.displayTimetableComponent.show();
  }

  onNonContactCodeRemoveClick(event: number[] | undefined) {
    if (event && event.length) {
      this.nonContactCodesService.deleteAssociations(this.timetableId, event)
        .pipe(switchMap(() => this.nonContactCodesService.getAssociations(this.timetableId))).subscribe();
    }
  }

  onNonContactCodeClick(event: INonContactCodesAssociation[]) {
    const nonContactCodeAssociationIds = event.map(nc => nc.nonContactCodeAssociationId).filter(nonContactCodeAssociationId => (nonContactCodeAssociationId ?? -1) > -1) as number[];
    if (nonContactCodeAssociationIds.length) {

      this.nonContactCodesService.deleteAssociations(this.timetableId, nonContactCodeAssociationIds)
        .pipe(switchMap(
          () => this.nonContactCodesService.createAssociations(this.timetableId, event.map(e => ({
            ...e,
            nonContactCodeAssociationId: undefined
          })))
        ))
        .pipe(switchMap(() => this.nonContactCodesService.getAssociations(this.timetableId))).subscribe();
    } else {
      this.nonContactCodesService.createAssociations(this.timetableId, event)
        .pipe(switchMap(() => this.nonContactCodesService.getAssociations(this.timetableId))).subscribe();
    }
  }

  onRemoveStaffClick(event: IRemoveStaffFromSession) {
    if (event.staffId) {
      if (event.sessions) {
        const removeMainStaffRequest = {
          sessionIds: [] as number[],
          removeMainStaff: true,
          additionalStaffId: null
        }
        const removeAdditionalStaffRequest = {
          sessionIds: [] as number[],
          removeMainStaff: false,
          additionalStaffId: event.staffId
        }

        event.sessions.forEach(session => {
          if (session.mainStaffId === event.staffId) {
            removeMainStaffRequest.sessionIds.push(session.id);
          } else if (session.additionalStaffIds.includes(event.staffId!)) {
            removeAdditionalStaffRequest.sessionIds.push(session.id);
          }
        })

        let endpoint;
        if (removeMainStaffRequest.sessionIds.length > 0 && removeAdditionalStaffRequest.sessionIds.length > 0) {
          endpoint = this.curriculumPlanBlocks.removeStaffFromSessions(removeMainStaffRequest).pipe(
            switchMap(() => this.curriculumPlanBlocks.removeStaffFromSessions(removeAdditionalStaffRequest))
          )
        } else if (removeMainStaffRequest.sessionIds.length > 0) {
          endpoint = this.curriculumPlanBlocks.removeStaffFromSessions(removeMainStaffRequest);
        } else if (removeAdditionalStaffRequest.sessionIds.length > 0) {
          endpoint = this.curriculumPlanBlocks.removeStaffFromSessions(removeAdditionalStaffRequest);
        }

        if (endpoint) {
          endpoint.subscribe(() => {
            const blocks = JSON.parse(JSON.stringify(this.findBlocksForSessionIds(event.sessionIds))) as IListOfBlocksResponse[];
            blocks.forEach(block => {
              const foundSessions = block.sessions!.filter(session => event.sessionIds.indexOf(session.id) > -1);

              foundSessions.forEach((foundSession) => {
                if (foundSession.mainStaffId === event.staffId) {
                  foundSession.mainStaffId = null;
                } else {
                  const index = foundSession.additionalStaffIds.indexOf(event.staffId!);
                  if (index > -1) {
                    foundSession.additionalStaffIds.splice(index, 1);
                  }
                }
              })
              this.afterBackendCall(block, this.blocksData)
            })
            this.updateData();
          })
        }

      } else {

        const removeRequest = {
          sessionIds: event.sessionIds,
          removeMainStaff: !!event.isMainStaff,
          additionalStaffId: !event.isMainStaff ? event.staffId : null
        }

        this.curriculumPlanBlocks.removeStaffFromSessions(removeRequest).subscribe(() => {
          const blocks = JSON.parse(JSON.stringify(this.findBlocksForSessionIds(event.sessionIds))) as IListOfBlocksResponse[];
          blocks.forEach(block => {
            const foundSession = block.sessions!.find(session => event.sessionIds.indexOf(session.id) > -1);
            if (foundSession) {
              // removing staff from source data
              if (foundSession.mainStaffId === event.staffId) {
                foundSession.mainStaffId = null;
              } else {
                const index = foundSession.additionalStaffIds.indexOf(event.staffId!);
                if (index > -1) {
                  foundSession.additionalStaffIds.splice(index, 1);
                }
              }
            }
            this.afterBackendCall(block, this.blocksData)
          })
          this.updateData();
        })
      }
    } else {
      const removeMainStaffRequest = {
        sessionIds: [...new Set(event.sessionIds)],
        removeMainStaff: true,
        additionalStaffId: null
      }
      const removeAdditionalStaffRequest = {
        sessionIds: [...new Set(event.sessionIds)],
        removeMainStaff: false,
        additionalStaffId: null
      }

      let endpoint = null;
      if (!event.removeOnlyAllAdditionalStaff) {
        endpoint = this.curriculumPlanBlocks.removeStaffFromSessions(removeMainStaffRequest).pipe(
          switchMap(() => this.curriculumPlanBlocks.removeStaffFromSessions(removeAdditionalStaffRequest))
        )
      } else {
        endpoint = this.curriculumPlanBlocks.removeStaffFromSessions(removeAdditionalStaffRequest)
      }

      endpoint.subscribe(() => {
        const blocks = JSON.parse(JSON.stringify(this.findBlocksForSessionIds(event.sessionIds))) as IListOfBlocksResponse[];
        blocks.forEach(block => {
          const foundSessions = block.sessions!.filter(session => event.sessionIds.indexOf(session.id) > -1);

          foundSessions.forEach((foundSession) => {
            if (!event.removeOnlyAllAdditionalStaff) {
              foundSession.mainStaffId = null;
            }
            foundSession.additionalStaffIds = [];
          })
          this.afterBackendCall(block, this.blocksData);
        })
        this.updateData();
      })
    }
  }

  onRemoveRoomClick(event: IRemoveRoomFromSession) {
    this.curriculumPlanBlocks.removeRoomFromSessions({ sessionIds: event.sessionIds }).subscribe(() => {
      const blocks = JSON.parse(JSON.stringify(this.findBlocksForSessionIds(event.sessionIds))) as IListOfBlocksResponse[];
      this.updateData();
      blocks.forEach(block => {
        const foundSessions = block.sessions!.filter(session => event.sessionIds.indexOf(session.id) > -1);
        foundSessions.forEach(foundSession => {
          if (foundSession) {
            // removing staff from source data
            foundSession.roomId = null;
          }
        })
        this.afterBackendCall(block, this.blocksData);
      })
    });
  }

  onSwapStaff(data: ICurriculumPlanStaff) {
    this.isSwapStaffView = true;
    const conflicts = this.conflictList.filter(c => c.type === "Teacher" && !c.isAccepted).map(conflicts => conflicts.parameters);
    const hasConflicts = conflicts.some((c: any) => c.staffId === data.staffId);
    if (hasConflicts) {
      this.warningModalTitle = data.name + ' ' + this.translate.instant('has unresolved conflicts.');
      this.warningModalPopup.show();
    } else {
      setTimeout(() => {
        this.swapDataModal.show();
      });
      this.curriculumPlan.getAllAssignedContactTimeForStaff(this.timetableId).subscribe(staffs => {
        this.staffsData = this.staffs.filter(staff => staff.id !== data.staffId && !staff.isExcluded).map(s => {
          const assigned = staffs.find(staff => staff.staffId === s.id)?.assigned ?? 0;
          return {
            ...s,
            hasConflict: conflicts.some((c: any) => c.staffId === s.id),
            assigned: assigned,
            remaining: s.totalContactTime > assigned ? s.totalContactTime - assigned : 0
          };
        }) as ISwapStaffDataEntity[];
      });
      const selectedData = this.staffs.find(staff => staff.id === data.staffId);
      this.selectedStaffData = selectedData as ISwapStaffDataEntity;
    }
  }

  onSwapRoom(data: ICurriculumPlanRoom) {
    this.isSwapStaffView = false;
    const conflicts = this.conflictList.filter(c => c.type === "Room" && !c.isAccepted).map(conflicts => conflicts.parameters);
    const hasConflicts = conflicts.some((c: any) => c.roomId === data.id);
    if (hasConflicts) {
      this.warningModalTitle = 'Room ' + data.code + ' ' + this.translationService.instant('has unresolved conflicts.');
      this.warningModalPopup.show();
    } else {
      setTimeout(() => {
        this.swapDataModal.show();
      });
      this.curriculumPlan.getAllAssignedContactTimeForRoom(this.timetableId).subscribe(rooms => {
        this.roomsData = rooms.filter(room => room.id !== data.id).map(r => {
          const selectedRoom = this.rooms.find(room => room.id === r.id);
          const assigned = r!.assignedContactTime;
          const available = r!.contactTime;
          return {
            ...r,
            originalId: selectedRoom?.originalId,
            name: selectedRoom?.name,
            capacity: selectedRoom?.capacity,
            typeId: selectedRoom?.typeId,
            siteId: selectedRoom?.siteId,
            isExcluded: selectedRoom?.isExcluded,
            hasConflict: conflicts.some((c: any) => c.roomId === r.id),
            contactTime: available,
            assigned: assigned,
            remaining: available > assigned ? available - assigned : 0
          };
        }) as ISwapRoomDataEntity[];
      });
      const selectedData = this.rooms.find(room => room.id === data.id);
      this.selectedRoomData = selectedData as ISwapRoomDataEntity;
    }
  }

  onSubstituteStaff(data: ICurriculumPlanStaff) {
    setTimeout(() => {
      this.substituteStaffModal.show();
    });
    this.curriculumPlan.getAllAssignedContactTimeForStaff(this.timetableId).subscribe(staffs => {
      this.staffsData = this.staffs.filter(staff => staff.id !== data.staffId && !staff.isExcluded).map(s => {
        const assigned = staffs.find(staff => staff.staffId === s.id)?.assigned ?? 0;
        return {
          ...s,
          assigned: assigned,
          remaining: s.totalContactTime > assigned ? s.totalContactTime - assigned : 0
        };
      }) as ISubstituteStaffDataEntity[];
    });
    const selectedData = this.staffs.find(staff => staff.id === data.staffId);
    this.selectedStaffData = selectedData as ISubstituteStaffDataEntity;
  }

  onBackToStaffs(): void {
    this.isEditStaff = false;
    this.editStaffsModal.nativeElement.hide();
    this.relationships
      .getStaffsList(this.projectId)
      .pipe(
        switchMap(() => this.curriculumPlan.getAllAssignedContactTimeForStaff(this.timetableId)),
        switchMap(() => this.curriculumPlan.getAllAssignedContactTimeForStaffByYearGroup(this.timetableId, this.selectedYearGroup)),
        switchMap(() => this.staffService.getList(this.projectId))
      )
      .subscribe((staff) => {
        this.staffs = staff;
        this.curriculumPlanBlocks.stateChanged$.next();
        if(this.editStaffsModal){
          this.editStaffsModal.nativeElement.hide();
        }
        this.openedModal = null;
      });
  }

  editRooms(show: boolean): void {
    if (show) {
      this.openedModal = 'Room';
      setTimeout(() => {
        this.isEditRooms = true;
        this.editRoomsModal.nativeElement.show()
      }, 100);
    } else {
      this.openedModal = null;
    }
  }

  onBackToRooms(): void {
    this.isEditRooms = false;
    this.editRoomsModal.nativeElement.hide()
    this.relationships.getRoomsList(this.projectId)
      .pipe(
        switchMap(() => this.roomService.getList(this.projectId))
      )
      .subscribe((room) => {
        this.rooms = room;
        this.curriculumPlanBlocks.stateChanged$.next();
        if(this.editRoomsModal){
          this.editRoomsModal.nativeElement.hide();
        }
        this.openedModal = null;
      });
  }

  ////////////////////////
  // CLASS CONTEXT MENU //
  ////////////////////////

  onDeleteClassClicked(data: any): void {
    this.data = data;
    if ([BLOCK_TYPE.Complex, BLOCK_TYPE.Linear].includes(data.block.blockTypeId)) {
      this.deleteClassConfirmationComponent.show();
    } else {
      this.deleteClass(data);
    }
  }

  deleteClass(data: any): void {
    this.curriculumPlanBlocks.removeClass({
      blockId: data.blockId,
      subjectToYearGroupId: data.subjectToYearGroupId,
      className: data.className
    })
      .subscribe({
        next: () => {
          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
        }
      });
  }


  ////////////////////
  // PERIOD MENU
  ////////////////////

  onDeletePeriod(data: any): void {
    this.periodMenuData = data;
    if (data?.isLinear) {
      const block = this.blocksData?.find(block => block.id === data.blockId);
      const hasScheduledSessions = block?.sessions?.some(s => {
        const periodIndex = s.periodIndex ? s.periodIndex - 1 : null;
        return data.selectedPeriodIndexes.includes(periodIndex) && s.periodId != null;
      });
      if (hasScheduledSessions) {
        this.deletePeriodConfirmationComponent.show();
      } else {
        this.deletePeriod(data);
      }
    } else {
      this.deletePeriod(data);
    }
  }

  deletePeriod(data: any, isDelete = true): void {
    if (isDelete) {
      this.curriculumPlanBlocks
      .deletePeriods({
        blockId: data.blockId,
        subjectToYearGroupId: data.subjectToYearGroupId,
        periodIndexes: data.selectedPeriodIndexes.map((index: any) => index + 1)
      })
      .pipe(switchMap(() => this.curriculumPlan.getBlock(data.blockId)))
      .subscribe({
        next: (block) => {
          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
          this.curriculumPlanBlocks.expandedSelectedSubjectIndex$.next(this.curriculumPlanBlocks.expandedSelectedSubjectIndex$.getValue());
          this.setScrollToPeriodIndex$.next({lastPeriodIndex: data.lastIndex, deletedPeriodIndicesCount: data.selectedPeriodIndexes.length});
          data.selectedPeriodIndexes$.next([]);
        }
      });
    }
  }

  linkPeriods(periodMenuData: any): void {
    const periodIndexes: number[] = this.periodMenuData.selectedPeriodIndexes || [];

    this.curriculumPlanBlocks
    .linkPeriods({
      blockId: this.periodMenuData.blockId,
      subjectToYearGroupId: this.periodMenuData.block.blockTypeId === BLOCK_TYPE.Linear ? periodMenuData.subjectToYearGroupId : null,
      startPeriodIndex: Math.min(...periodIndexes) + 1,
      endPeriodIndex: Math.max(...periodIndexes) + 1
    })
    .pipe(switchMap(() => this.curriculumPlan.getBlock(periodMenuData.blockId)))
    .subscribe({
      next: (block) => {
        this.curriculumPlanBlocks.stateChanged$.next();
        this.curriculumPlanBlocks.blockSideStateChanged$.next();
        this.curriculumPlan.currentSelectedBlock$.next(block);
        this.curriculumPlanBlocks.expandedSelectedSubjectIndex$.next(this.curriculumPlanBlocks.expandedSelectedSubjectIndex$.getValue());
        this.setScrollToPeriodIndex$.next({lastPeriodIndex: periodMenuData.lastIndex, deletedPeriodIndicesCount: 0});
        periodMenuData.selectedPeriodIndexes$.next([]);
        this.snackbar.success(this.translate.instant('Periods linked successfully.'));
      },
      error: () => {
        this.linkedPeriodExceedsTotalWarningModalPopup.show();
      }
    });
  }

  unlinkPeriods(periodMenuData: any): void {
    const periodIndexes: number[] = this.periodMenuData.selectedPeriodIndexes || [];

    this.curriculumPlanBlocks
      .unlinkPeriods({
        blockId: this.periodMenuData.blockId,
        subjectToYearGroupId: this.periodMenuData.block.blockTypeId === BLOCK_TYPE.Linear ? periodMenuData.subjectToYearGroupId : null,
        startPeriodIndex: periodIndexes.length > 0 ? Math.min(...periodIndexes) + 1 : this.periodMenuData.index + this.periodMenuData.firstIndex,
        endPeriodIndex: periodIndexes.length > 0 ? Math.max(...periodIndexes) + 1 : this.periodMenuData.index + this.periodMenuData.firstIndex,
      })
      .pipe(switchMap(() => this.curriculumPlan.getBlock(periodMenuData.blockId)))
      .subscribe({
        next: (block) => {
          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
          this.curriculumPlanBlocks.expandedSelectedSubjectIndex$.next(this.curriculumPlanBlocks.expandedSelectedSubjectIndex$.getValue());
          this.setScrollToPeriodIndex$.next({lastPeriodIndex: periodMenuData.lastIndex, deletedPeriodIndicesCount: 0});
          periodMenuData.selectedPeriodIndexes$.next([]);
          this.snackbar.success(this.translate.instant('Periods unlinked successfully.'));
        }
      });
  }

  unlinkAllPeriods(periodMenuData: any): void {
    const periodIndexes: number[] = this.periodMenuData.selectedPeriodIndexes || [];

    this.curriculumPlanBlocks
      .unlinkAllPeriods({
        blockId: this.periodMenuData.blockId,
        subjectToYearGroupId: this.periodMenuData.block.blockTypeId === BLOCK_TYPE.Linear ? periodMenuData.subjectToYearGroupId : null,
        startPeriodIndex: periodIndexes.length > 0 ?  Math.min(...periodIndexes) + 1 : this.periodMenuData.index + this.periodMenuData.firstIndex,
        endPeriodIndex: periodIndexes.length > 0 ?  Math.max(...periodIndexes) + 1 : this.periodMenuData.index + this.periodMenuData.firstIndex,
      })
      .pipe(switchMap(() => this.curriculumPlan.getBlock(periodMenuData.blockId)))
      .subscribe({
        next: (block) => {
          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
          this.curriculumPlanBlocks.expandedSelectedSubjectIndex$.next(this.curriculumPlanBlocks.expandedSelectedSubjectIndex$.getValue());
          this.setScrollToPeriodIndex$.next({lastPeriodIndex: periodMenuData.lastIndex, deletedPeriodIndicesCount: 0});
          periodMenuData.selectedPeriodIndexes$.next([]);
          this.snackbar.success(this.translate.instant('Link removed successfully.'));
        }
      });
  }


  ////////////////////
  // SESSION MENU
  ////////////////////

  onRemoveFromThisSession(data: { removeTeacher: boolean, removeAdditionalStaff: boolean, removeRoom: boolean }): void {
    const sessionIds = this.sessionMenuData.onlySessionRelated
      ? [this.sessionMenuData.session.id]
      : this.sessionMenuData.session.classRelatedSessionIds;
    let subscription: Observable<void | void[]>;
    let snackbarMessage: string;

    const sessionIdsToRemoveMainStaff: number[] = this.sessionMenuData.block.sessions
      .filter((session: ICurriculumSessions) => sessionIds.includes(session.id) && session.mainStaffId)
      .map((session: ICurriculumSessions) => session.id);

    const sessionIdsToRemoveRoom: number[] = this.sessionMenuData.block.sessions
      .filter((session: ICurriculumSessions) => sessionIds.includes(session.id) && session.roomId)
      .map((session: ICurriculumSessions) => session.id);

    if (data.removeTeacher && data.removeRoom) {
      subscription = forkJoin([
        this.curriculumPlanBlocks.removeStaffFromSessions({
          sessionIds: Array.from(new Set([...sessionIdsToRemoveMainStaff, ...sessionIdsToRemoveRoom])),
          removeMainStaff: true
        }),
        this.curriculumPlanBlocks.removeRoomFromSessions({ sessionIds })
      ])
      snackbarMessage = this.translate.instant(`Main staff and room were removed successfully.`)
    } else if (data.removeTeacher) {
      subscription = this.curriculumPlanBlocks.removeStaffFromSessions({
        sessionIds: sessionIdsToRemoveMainStaff,
        removeMainStaff: true
      })
      snackbarMessage = this.translate.instant(`Main staff was removed successfully.`)
    } else {
      subscription = this.curriculumPlanBlocks.removeRoomFromSessions({ sessionIds: sessionIdsToRemoveRoom })
      snackbarMessage = this.translate.instant(`Room was removed successfully.`)
    }

    subscription?.pipe(switchMap(() => this.curriculumPlan.getBlock(this.sessionMenuData.blockId)))
      .subscribe({
        next: (block) => {
          this.snackbar.success(snackbarMessage);

          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
        }
      })
  }

  removeAdditionalStaff(staffCode: string): void {
    const sessionIds = this.sessionMenuData.onlySessionRelated
      ? [this.sessionMenuData.session.id]
      : this.sessionMenuData.session.classRelatedSessionIds;

    const staff = this.relationships.staffListData$.getValue();
    const staffId = staff.find(staffMember => staffMember.code === staffCode)?.id;

    let sessionIdsToRemove = []
    if (staffId) {
      sessionIdsToRemove = this.sessionMenuData.block.sessions
        .filter((session: ICurriculumSessions) => sessionIds.includes(session.id) &&
          session.additionalStaffIds.includes(staffId))
        .map((session: ICurriculumSessions) => session.id);
    }

    this.curriculumPlanBlocks.removeStaffFromSessions({
      sessionIds: staffCode === 'all' ? sessionIds : sessionIdsToRemove,
      removeMainStaff: false,
      additionalStaffId: staffCode === 'all' ? null : staffId
    }).pipe(switchMap(() => this.curriculumPlan.getBlock(this.sessionMenuData.blockId)))
      .subscribe({
        next: (block) => {
          this.snackbar.success(this.translate.instant(`Additional staff was removed successfully.`));

          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
        }
      })
  }

  clearSessions(): void {
    const sessionIds = this.sessionMenuData?.onlySessionRelated
      ? [this.sessionMenuData.session.id]
      : this.sessionMenuData.session.classRelatedSessionIds;

    const sessionIdsToRemove = this.sessionMenuData.block.sessions
      .filter((session: ICurriculumSessions) => sessionIds.includes(session.id) &&
        !session.isLocked &&
        (session.mainStaffId || session.additionalStaffIds.length || session.roomId || session.periodId))
      .map((session: ICurriculumSessions) => session.id);

    this.curriculumPlanBlocks.clearSessions({ sessionIds: sessionIdsToRemove, blockId: this.sessionMenuData.blockId })
      .pipe(switchMap(() => this.curriculumPlan.getBlock(this.sessionMenuData.blockId)))
      .subscribe({
        next: (block) => {
          this.snackbar.success(this.translate.instant(this.sessionMenuData.onlySessionRelated ? 'Session was cleared successfully.' : 'Sessions were cleared successfully.'));

          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
          this.relationships.staffToSessionActiveId$.next(null);
          this.relationships.roomToSessionActiveId$.next(null);
        }
      });
  }

  unScheduleSubject(): void {
    const sessions = this.subjectMenuData.block.sessions.filter((session: ISession) => session.subjectToYearGroupId === this.subjectMenuData.subject.id && session.periodId);
    const sessionIds = sessions.map((session: ISession) => session.id);
    this.curriculumPlanBlocks.unscheduleSessions({ blockId: this.subjectMenuData.blockId, sessionIds })
      .pipe(switchMap(() => this.curriculumPlan.getBlock(this.subjectMenuData.blockId)))
      .subscribe({
        next: (block) => {
          this.snackbar.success(this.translate.instant('Sessions were unscheduled successfully.'));
          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
        }
      });
  }

  unSchedulePeriod(periodMenuData: any): void {
    const selectedIndexes = periodMenuData.selectedPeriodIndexes.map((item: number) => item + 1);
    const sessions = this.periodMenuData.sessions?.filter((session: ISession) => selectedIndexes.includes(session.periodIndex) && session.periodId && session.subjectToYearGroupId === periodMenuData.subjectToYearGroupId);
    const sessionIds = sessions.map((session: ISession) => session.id);

    this.curriculumPlanBlocks.unscheduleSessions({ blockId: this.periodMenuData.blockId, sessionIds })
      .pipe(switchMap(() => this.curriculumPlan.getBlock(this.periodMenuData.blockId)))
      .subscribe({
        next: (block) => {
          this.snackbar.success(this.translate.instant('Sessions were unscheduled successfully.'));
          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
          this.resetSelectedPeriodIds$.next();
        }
      });
  }

  unScheduleSessions(): void {
    const sessionIds = this.sessionMenuData.onlySessionRelated
      ? [this.sessionMenuData.session.id]
      : this.sessionMenuData.session.classRelatedSessionIds;

    const notLockedIds = this.sessionMenuData.block.sessions
      .filter((session: ICurriculumSessions) => sessionIds.includes(session.id) && !session.isLocked)
      .map((session: ICurriculumSessions) => session.id);

    this.curriculumPlanBlocks.unscheduleSessions({ blockId: this.sessionMenuData.blockId, sessionIds: notLockedIds })
      .pipe(switchMap(() => this.curriculumPlan.getBlock(this.sessionMenuData.blockId)))
      .subscribe({
        next: (block) => {
          this.snackbar.success(this.translate.instant(this.sessionMenuData.onlySessionRelated ? 'Session was unscheduled successfully.' : 'Sessions were unscheduled successfully.'));

          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
        }
      });
  }

  combineSessions(): void {
    this.combineSessionsData = {
      blockId: this.sessionMenuData.blockId,
      subjectToYearGroupId: this.sessionMenuData.session.subjectToYearGroupId,
      periodIndexes: this.sessionMenuData.selectedSessionIndexes
    }

    if (this.currentSelectedBlock?.blockTypeId === this.BLOCK_TYPE.Linear) {
      const hasScheduledSession = this.currentSelectedBlock.sessions.some(session => session.periodId);
      if (hasScheduledSession) {
        this.linearUnScheduleWarningModalPopup.show();
      } else {
        this.combineSessionsRequest();
      }
    } else {
      this.combineSessionsRequest();
    }
  }

  unScheduleLinearBlockAndCombine(): void {
    const sessionIds = this.currentSelectedBlock?.sessions.filter(session => session.periodId).map(session => session.id) || [];
    this.curriculumPlanBlocks.unscheduleSessions({ blockId: this.sessionMenuData.blockId, sessionIds })
      .subscribe({
        next: () => {
          this.snackbar.success(this.translate.instant('Block was unscheduled successfully.'));
          this.combineSessionsRequest();
        }
      });
  }

  combineSessionsRequest(): void {
    this.curriculumPlanBlocks.combineSessions(this.combineSessionsData)
      .pipe(switchMap(() => this.curriculumPlan.getBlock(this.sessionMenuData.blockId)))
      .subscribe({
        next: (block) => {
          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
          this.sessionMenuData.selectedSessionIds$.next([]);
          this.sessionMenuData.selectedSessionIndexes$.next([]);
        }
      });
  }

  splitSessions(): void {
    const data = {
      blockId: this.sessionMenuData.blockId,
      subjectToYearGroupId: this.sessionMenuData.session.subjectToYearGroupId,
      periodIndexes: this.sessionMenuData.selectedSessionIndexes
    }

    this.curriculumPlanBlocks.splitSessions(data)
      .pipe(switchMap(() => this.curriculumPlan.getBlock(this.sessionMenuData.blockId)))
      .subscribe({
        next: (block) => {
          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
          this.sessionMenuData.selectedSessionIds$.next([]);
          this.sessionMenuData.selectedSessionIndexes$.next([]);
        }
      });
  }

  addPeriod(): void {
    const data = {
      blockId: this.sessionMenuData.blockId,
      subjectToYearGroupId: this.sessionMenuData.session.subjectToYearGroupId,
      classNameIndex: typeof this.sessionMenuData.classNameIndex === 'number' ? this.sessionMenuData.classNameIndex : null,
      periodIndex: typeof this.sessionMenuData.periodIndex === 'number' ? this.sessionMenuData.periodIndex : null
    }

    this.curriculumPlanBlocks.addPeriod(data)
      .pipe(switchMap(() => this.curriculumPlan.getBlock(this.sessionMenuData.blockId)))
      .subscribe({
        next: (block) => {
          updateLastVisibleIndexForBlocks(block, this.curriculumPlanBlocks.lastVisibleIndexForBlocks);

          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
        }
      });
  }

  onAddClasses(numberOfClasses: number): void {
    const data = {
      blockId: this.sessionMenuData.blockId,
      subjectToYearGroupId: this.sessionMenuData.session.subjectToYearGroupId,
      numberOfClasses,
      classNameIndex: this.sessionMenuData.isComplexBlock ? +this.sessionMenuData.session.className.split('-')[0] : null
    }

    this.curriculumPlanBlocks.addClassesToBlockSubject(data)
      .pipe(switchMap(() => this.curriculumPlan.getBlock(this.sessionMenuData.blockId)))
      .subscribe({
        next: (block) => {
          this.snackbar.success(this.translate.instant(numberOfClasses === 1 ? 'Class was added successfully.' : 'Classes were added successfully.'));

          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
        }
      });
  }

  onRemoveClassClicked(): void {
    if (this.sessionMenuData.block && [BLOCK_TYPE.Complex, BLOCK_TYPE.Linear].includes(this.sessionMenuData.block.blockTypeId)) {
      this.deleteSessionClassConfirmationComponent.show();
    } else {
      this.removeClass(true);
    }
  }

  removeClass(isRemove: boolean): void {
    if (!isRemove) {
      return
    }

    this.curriculumPlanBlocks.removeClass({
      blockId: this.sessionMenuData.blockId,
      subjectToYearGroupId: this.sessionMenuData.session.subjectToYearGroupId,
      className: this.sessionMenuData.session.className
    })
      .pipe(switchMap(() => this.curriculumPlan.getBlock(this.sessionMenuData.blockId)))
      .subscribe({
        next: (block) => {
          this.snackbar.success(this.translate.instant('Class was removed successfully.'));
          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
        }
      });
  }

  periodMenuOpened(data: any) {
    this.periodMenuData = data;
    this.selectedSubjectId = data.block.subjectToYearGroups[data.subjectIndex].id;
    this.selectedPeriodIndexes = data.selectedPeriodIndexes;
    let sessionsInSelectedPeriods = [];
    let adjustedIndex = data.index + data.firstIndex;

    if (data.block.blockTypeId === BLOCK_TYPE.Linear) {
      this.isScheduled = data.sessions.filter((session: ISession) => session.subjectToYearGroupId === this.selectedSubjectId && session.periodIndex === adjustedIndex && session.periodId).length > 0;
      sessionsInSelectedPeriods = data.sessions.filter((session: ISession) => session?.periodIndex != null && session.subjectToYearGroupId === this.selectedSubjectId && this.selectedPeriodIndexes.includes(session?.periodIndex - 1));
    } else {
      this.isScheduled = data.sessions.filter((session: ISession) => session.periodIndex === adjustedIndex && session.periodId).length > 0;
      sessionsInSelectedPeriods = data.sessions.filter((session: ISession) => session?.periodIndex != null && this.selectedPeriodIndexes.includes(session?.periodIndex - 1));
    }

    let isComplexBlock = data.block.blockTypeId === BLOCK_TYPE.Complex;
    let areConsecutivePeriods = this.areConsecutivePeriods(this.selectedPeriodIndexes);

    this.canLink = !isComplexBlock && areConsecutivePeriods && this.selectedPeriodIndexes.length > 1 &&
      sessionsInSelectedPeriods.every((session: ISession) => session.periodId == null && session.linkedBlockPeriod == null);

    if (this.selectedPeriodIndexes?.length < 1)
    {
      console.log("kisebb mint 1")
      if (data.block.blockTypeId === BLOCK_TYPE.Linear) {
        sessionsInSelectedPeriods = data.sessions.filter((session: ISession) => session?.periodIndex != null && session.subjectToYearGroupId === this.selectedSubjectId && adjustedIndex === session?.periodIndex);
      } else {
        sessionsInSelectedPeriods = data.sessions.filter((session: ISession) => session?.periodIndex != null && adjustedIndex === session?.periodIndex);
      }
    }

    let sessionInSelectedPeriod = sessionsInSelectedPeriods[0] as ISession;

    this.canUnlink = !isComplexBlock && (this.selectedPeriodIndexes?.length < 2 || areConsecutivePeriods) &&
      sessionsInSelectedPeriods.every((session: ISession) => session.linkedBlockPeriod != null && session.linkedBlockPeriod?.linkIndex == sessionInSelectedPeriod.linkedBlockPeriod?.linkIndex) &&
      sessionsInSelectedPeriods.some((session: ISession) => session.periodIndex == sessionInSelectedPeriod.linkedBlockPeriod?.startIndex || session.periodIndex == sessionInSelectedPeriod.linkedBlockPeriod?.endIndex);

    this.canUnlinkAll = !isComplexBlock && sessionsInSelectedPeriods.every((session: ISession) => session.linkedBlockPeriod != null && session.linkedBlockPeriod?.linkIndex == sessionInSelectedPeriod.linkedBlockPeriod?.linkIndex);
  }

  areConsecutivePeriods(periods: number[]) {
    let n = periods.length;

    periods.sort((a, b) => a - b);

    for (let i = 1; i < n; i++) {
      if (periods[i] !== periods[i - 1] + 1) {
        return false;
      }
    }

    return true;
  }

  classMenuOpened(data: any) {
    this.classMenuData = data;
    this.selectedClassIndexes = data.selectedClassIndexes;
    this.blockForSwap = data.block;

    if (data.isSimpleBlock || data.isOptionBlock) {
      this.showMenu = this.selectedClassIndexes.length === 2 && this.selectedClassIndexes.includes(data.currentIndex);

      if (this.showMenu && this.selectedClassIndexes.length === Object.keys(data.classData)?.length) {
        const sessionsOfClass1 = data.sessions.filter((s: ISession) => s.className === data.classData[data.selectedClassIndexes[0]].className && s.subjectToYearGroupId === data.classData[data.selectedClassIndexes[0]].subjectToYearGroupId);
        const sessionsOfClass2 = data.sessions.filter((s: ISession) => s.className === data.classData[data.selectedClassIndexes[1]].className && s.subjectToYearGroupId === data.classData[data.selectedClassIndexes[1]].subjectToYearGroupId);

        this.setSwapMenu(sessionsOfClass1, sessionsOfClass2, data.block);
      }
    } else if (data.isLinear) {
      const selectedClassesForSubject = Object.keys(data.classData).filter(key => data.classData[key].subjectIndex === data.subjectIndex).map(Number);
      this.showMenu = selectedClassesForSubject.length === 2 && selectedClassesForSubject.includes(data.currentIndex);

      if (this.showMenu) {
        const selectedClasses = Object.fromEntries(
          Object.entries(data.classData)
            .filter(([key, value]) => (value as any).subjectIndex === data.subjectIndex)
            .map(([key, value]) => [Number(key), value])
        ) as Record<number, { className: string, subjectToYearGroupId: number | undefined, subjectIndex: number | undefined }>;

        const sessionsOfClass1 = data.sessions.filter((s: ISession) => s.className === selectedClasses[selectedClassesForSubject[0]].className && s.subjectToYearGroupId === selectedClasses[selectedClassesForSubject[0]].subjectToYearGroupId);
        const sessionsOfClass2 = data.sessions.filter((s: ISession) => s.className === selectedClasses[selectedClassesForSubject[1]].className && s.subjectToYearGroupId === selectedClasses[selectedClassesForSubject[1]].subjectToYearGroupId);

        this.setSwapMenu(sessionsOfClass1, sessionsOfClass2, data.block);
      }
    } else if (data.isComplex) {
      const hasFreeSession = data.block.subjectToYearGroups.some((s: ISubjectToYearGroup) => s.subjectId === this.freeSubjectId);
      const hasSameSubject = (data.block.subjectToYearGroups.length === 1 && !hasFreeSession) || (data.block.subjectToYearGroups.length >= 2 && hasFreeSession);
      const enableMenu = this.selectedClassIndexes.length === 2 && this.selectedClassIndexes.includes(data.currentIndex);

      if (hasSameSubject && enableMenu) {
        const sessionsOfClass1 = data.sessions.filter((s: ISession) => s.className === data.classData[data.selectedClassIndexes[0]].className);
        const sessionsOfClass2 = data.sessions.filter((s: ISession) => s.className === data.classData[data.selectedClassIndexes[1]].className);

        const subjectToYearGroupId = (data.block.subjectToYearGroups.length === 1) ? data.block.subjectToYearGroups[0].id : data.block.subjectToYearGroups.find((s: ISubjectToYearGroup) => s.subjectId !== this.freeSubjectId).id;

        const periodsWithSameSubjectClass1 = sessionsOfClass1.filter((s: ISession) => s.subjectToYearGroupId === subjectToYearGroupId).length;
        const periodsWithSameSubjectClass2 = sessionsOfClass2.filter((s: ISession) => s.subjectToYearGroupId === subjectToYearGroupId).length;

        this.showMenu = (periodsWithSameSubjectClass1 === periodsWithSameSubjectClass2);

        if (this.showMenu) {
          this.setSwapMenu(sessionsOfClass1, sessionsOfClass2, data.block);
        }
      } else {
        this.showMenu = false;
      }
    }
  }

  setSwapMenu(sessionsOfSelectedClass1: ISession[], sessionsOfSelectedClass2: ISession[], block: IListOfBlocksResponse) {
    this.swapClassRequest.classIndexes = [];
    this.swapClassRequest.classIndexes.push(sessionsOfSelectedClass1[0].classIndex ?? 0);
    this.swapClassRequest.classIndexes.push(sessionsOfSelectedClass2[0].classIndex ?? 0);
    this.swapClassRequest.subjectId = block.blockTypeId === BLOCK_TYPE.Linear ? block.subjectToYearGroups?.find((subject: ISubjectToYearGroup) => subject.id === sessionsOfSelectedClass1[0].subjectToYearGroupId)?.subjectId ?? null : null;
    if (block.blockTypeId === BLOCK_TYPE.Options) {
      this.swapClassRequest.subjectToYearGroupIds = [];
      this.swapClassRequest.subjectToYearGroupIds?.push(sessionsOfSelectedClass1[0].subjectToYearGroupId ?? 0);
      this.swapClassRequest.subjectToYearGroupIds?.push(sessionsOfSelectedClass2[0].subjectToYearGroupId ?? 0);
    } else {
      this.swapClassRequest.subjectToYearGroupIds = null;
    }

    this.allowSwapMainStaff = sessionsOfSelectedClass1.some((s: ISession) => s.mainStaffId != null) || sessionsOfSelectedClass2.some((s: ISession) => s.mainStaffId != null);
    this.allowSwapRoom = sessionsOfSelectedClass1.some((s: ISession) => s.roomId != null) || sessionsOfSelectedClass2.some((s: ISession) => s.roomId != null);
    this.allowSwapAdditionalStaff = sessionsOfSelectedClass1.some((s: ISession) => s.additionalStaffIds.length > 0) || sessionsOfSelectedClass2.some((s: ISession) => s.additionalStaffIds.length > 0);
    this.allowSwapAllAllocations = this.allowSwapMainStaff || this.allowSwapRoom || this.allowSwapAdditionalStaff;
  }

  onSwapClassMenuOptionClick(option: string): void {
    this.swapClassRequest.swapMainStaff = this.swapClassRequest.swapAdditionalStaff = this.swapClassRequest.swapRoom = false;
    switch (option) {
      case SWAP_CLASS_OPTIONS.SwapMainStaff:
        this.swapClassRequest.swapMainStaff = true;
        break;
      case SWAP_CLASS_OPTIONS.SwapAdditionalStaff:
        this.swapClassRequest.swapAdditionalStaff = true;
        break;
      case SWAP_CLASS_OPTIONS.SwapRoom:
        this.swapClassRequest.swapRoom = true;
        break;
      case SWAP_CLASS_OPTIONS.SwapAllAllocations:
        this.swapClassRequest.swapMainStaff = this.swapClassRequest.swapAdditionalStaff = this.swapClassRequest.swapRoom = true;
        break;
      default:
        break;
    }

    this.swapService.getSwapClassFeedback(this.blockForSwap.id!, this.swapClassRequest).subscribe(createsConflict => {
      if (!createsConflict) {
        this.swapClass();
      } else {
        this.swapClassWarningModalTitle = this.translate.instant('Are you sure you want to continue?');
        this.swapClassWarningModalMain = this.translate.instant('By doing this swap you will create conflicts.');
        this.swapClassWarningModalPopup.show();
      }
    });
  }

  swapClass() {
    this.swapService.swapClass(this.blockForSwap.id!, this.swapClassRequest).subscribe({
      next: () => {
        this.updateData();
        this.classMenuData.selectedClassIndexes = [];
        this.classMenuData.classData = [];
        this.resetSelectedClassIds$.next();
        this.snackbar.success(this.translate.instant('Swapped successfully'));
      },
      error: ({ error }) => {
        if (error?.validationErrors && error?.validationErrors[0]) {
          this.snackbar.error(error.validationErrors[0].errorMessage);
        }
      }
    });
  }

  sessionMenuOpened(data: any) {
    this.setProperties(data);
  }

  private setProperties(data: any): void {
    this.sessionMenuData = data;
    if (data.selectedSessionIds) {
      this.isDoubleSelected = equals([...data.session.joinedSessions].sort(), [...data.selectedSessionIds].sort())
    }

    const {
      allMainStaffIds,
      mainStaffCode,
      mainStaffId,
      allRoomIds,
      roomCode,
      roomId,
      allAdditionalStaffCodes,
      additionalStaffCodes,
      additionalStaffIds
    } = data.session;

    if (data.isComplexBlock) {
      if (data.onlySessionRelated) {
        this.disableTeacherAndRoom = !(mainStaffCode && roomCode);
        this.disableTeacher = !mainStaffCode;
        this.disableAdditionalTeacher = !additionalStaffCodes.length;
        this.disableRoom = !roomCode;
      } else {
        this.disableTeacherAndRoom = !(allMainStaffIds.length && allRoomIds.length);
        this.disableTeacher = !allMainStaffIds.length;
        this.disableAdditionalTeacher = !allAdditionalStaffCodes.length;
        this.disableRoom = !allRoomIds.length;
      }
    } else {
      if (data.onlySessionRelated) {
        this.disableTeacherAndRoom = !(mainStaffId && roomId);
        this.disableTeacher = !mainStaffId;
        this.disableAdditionalTeacher = !additionalStaffIds?.length;
        this.disableRoom = !roomId;
      } else {
        this.disableTeacherAndRoom = !(allMainStaffIds.length && allRoomIds.length);
        this.disableTeacher = !allMainStaffIds.length;
        this.disableAdditionalTeacher = !allAdditionalStaffCodes.length;
        this.disableRoom = !allRoomIds.length;
      }
    }

    const session = this.sessionMenuData.session;
    this.disableClear = this.sessionMenuData.onlySessionRelated
      ? !((session.roomId || session.mainStaffId || session.additionalStaffIds.length || session.periodId) && !session.isLocked)
      : !this.sessionMenuData.block.sessions
        .filter((session: ICurriculumSessions) => this.sessionMenuData.session.classRelatedSessionIds.includes(session.id))
        .filter((session: ICurriculumSessions) => !session.isLocked)
        .some((session: ICurriculumSessions) => session.roomId || session.mainStaffId || session.additionalStaffIds.length || session.periodId);

    this.isScheduled = this.sessionMenuData.onlySessionRelated
      ? !!this.sessionMenuData.session.periodId
      : this.sessionMenuData.block.sessions
        .filter((session: ICurriculumSessions) => this.sessionMenuData.session.classRelatedSessionIds.includes(session.id) && session.periodId)
        .some((session: ICurriculumSessions) => !session.isLocked);

    this.isLocked = this.sessionMenuData.onlySessionRelated
      ? data.session.isLocked
      : this.sessionMenuData.block.sessions
        .filter((session: ICurriculumSessions) => this.sessionMenuData.session.classRelatedSessionIds.includes(session.id) && session.periodId)
        .every((session: ICurriculumSessions) => session.isLocked);

    if (data.isComplexBlock) {
      this.disableRemoveClass = Array.from(new Set(
        data.block?.sessions
          .filter((session: ICurriculumSessions) => session.className.split('-')[0] === data.session.className.split('-')[0])
          .map((session: ICurriculumSessions) => session.className))).length === 1;
    } else {
      this.disableRemoveClass = Array.from(new Set(data.block?.sessions
        .filter((session: ICurriculumSessions) => session.subjectToYearGroupId === data.session.subjectToYearGroupId)
        .map((session: ICurriculumSessions) => session.className))).length === 1;
    }
    this.additionalStaff = data.session.allAdditionalStaffCodes ?? data.session.additionalStaffCodes
  }

  //////////////////
  // SUBJECT MENU //
  //////////////////

  addPeriodForSubject(): void {
    const data = {
      blockId: this.subjectMenuData.blockId,
      subjectToYearGroupId: this.subjectMenuData.subject.id,
      classNameIndex: this.subjectMenuData.isComplexBlock && this.subjectMenuData.classNameIndex ? this.subjectMenuData.classNameIndex : null,
      periodIndex: typeof this.subjectMenuData.periodIndex === 'number' ? this.subjectMenuData.periodIndex : null
    }

    this.curriculumPlanBlocks.addPeriod(data)
      .pipe(switchMap(() => this.curriculumPlan.getBlock(this.subjectMenuData.blockId)))
      .subscribe({
        next: (block) => {
          updateLastVisibleIndexForBlocks(block, this.curriculumPlanBlocks.lastVisibleIndexForBlocks);

          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
        }
      });
  }

  swapSubject(): void {
    this.curriculumPlanBlocks.SwapSubjectsFeedback(this.subjectMenuData.blockId, this.subjectMenuData.selectedSubjectSessionIds)
      .subscribe(msg => {
        //if true show message or call swapsubjects
        if (msg != null && msg == true)
          this.swapSubjectsConfirmationComponent?.show();
        else
          this.swapSubjects(true);
      });
  }

  swapSubjects(event: boolean): void {
    if (!event) {
      return;
    }
    this.curriculumPlanBlocks.swapSubject(this.subjectMenuData.blockId, this.subjectMenuData.selectedSubjectSessionIds)
      .subscribe({
        next: () => {
          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.subjectMenuData.selectedSubjectSessionIds$.next([]);

          const currentSelectedBlock = this.curriculumPlan.currentSelectedBlock$.getValue();
          if (currentSelectedBlock) {
            this.curriculumPlan.getBlock(currentSelectedBlock.id)
              .subscribe(block => this.curriculumPlan.currentSelectedBlock$.next(block))
          }
        },
        error: ({ error }) => {
          if (error?.validationErrors && error?.validationErrors[0]) {
            this.snackbar.error(error.validationErrors[0].errorMessage);
          }
        }
      });
  }


  deleteSubject(): void {
    this.curriculumPlanBlocks.removeSubject({
      blockId: this.subjectMenuData.block.id,
      subjectToYearGroupId: this.subjectMenuData.subject.id,
      sessionIds: this.subjectMenuData.sessionsToDelete ?? null
    }).pipe(switchMap(() => this.curriculumPlan.getBlock(this.subjectMenuData.blockId)))
      .subscribe({
        next: (block) => {
          // Simple view
          const isNewValueMoreThanZero = this.curriculumPlanBlocks.lastVisibleIndexForBlocks[block.id] - 1 > 0;
          if (isNewValueMoreThanZero && !this.subjectMenuData.isComplexBlock) {
            this.curriculumPlanBlocks.lastVisibleIndexForBlocks[block.id] -= 1;
          }

          // Expanded view
          if (typeof this.subjectMenuData.expandedSelectedSubjectIndex === 'number' && block) {
            this.curriculumPlanBlocks.expandedSelectedSubjectIndex$.next(
              this.subjectMenuData.expandedSelectedSubjectIndex + 1 > block.subjectToYearGroups.length ? this.subjectMenuData.expandedSelectedSubjectIndex - 1 : 0);
          }

          if (this.subjectMenuData.lastIndex && this.subjectMenuData.isComplexBlock) {
            const sessionsInSelectedPeriod = this.subjectMenuData.block.sessions.filter((s: ISession) => s.sessionName !== "--" && s.periodIndex === this.subjectMenuData.periodIndex)?.length;
            const sessionToDelete = this.subjectMenuData.block.sessions.find((s: ISession) => s.id === this.subjectMenuData.sessionsToDelete[0]);
            this.setScrollToPeriodIndex$.next({lastPeriodIndex: this.subjectMenuData.lastIndex, deletedPeriodIndicesCount: sessionsInSelectedPeriod === 1 && sessionToDelete.sessionName !== "--" ? 1 : 0});
          }

          // Common
          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);

          this.snackbar.success(this.translate.instant('Subject was deleted successfully.'))
        }
      });
  }

  subjectMenuOpened(event: any) {
    this.setSubjectMenuProperties(event);
    this.isScheduled = event.data.block.sessions.filter((session: ISession) => session.subjectToYearGroupId === event.data.subject.id && session.periodId).length > 0;
  }

  private setSubjectMenuProperties(data: any): void {
    this.subjectMenuData = data.data;
    this.disableComplexOptionOnlyFreeSession = this.subjectMenuData?.block?.blockTypeId === BLOCK_TYPE.Complex && this.subjectMenuData?.block.subjectToYearGroups.length === 1

    const ids = this.subjectMenuData.selectedSubjectSessionIds;
    if (ids?.length !== 2) {
      this.disableSwapSubjects = true;
      return;
    }

    const sessionA: ICurriculumSessions = this.subjectMenuData.block?.sessions.find((session: ICurriculumSessions) => session.id === ids[0]);
    const sessionB: ICurriculumSessions = this.subjectMenuData.block?.sessions.find((session: ICurriculumSessions) => session.id === ids[1]);
    let sessionADoubleAndSameSubject = false;
    let sessionBDoubleAndSameSubject = false;

    if (sessionA && sessionA.joinedSessions.length === 2) {
      const otherSessionId = sessionA.joinedSessions.find(id => id !== ids[0])
      const otherSession = this.subjectMenuData.block?.sessions.find((session: ICurriculumSessions) => session.id === otherSessionId);
      sessionADoubleAndSameSubject = sessionA.subjectToYearGroupId === otherSession?.subjectToYearGroupId
    }

    if (sessionB && sessionB.joinedSessions.length === 2) {
      const otherSessionId = sessionB.joinedSessions.find(id => id !== ids[1])
      const otherSession = this.subjectMenuData.block?.sessions.find((session: ICurriculumSessions) => session.id === otherSessionId);
      sessionBDoubleAndSameSubject = sessionB.subjectToYearGroupId === otherSession?.subjectToYearGroupId
    }

    this.disableSwapSubjects = sessionADoubleAndSameSubject || sessionBDoubleAndSameSubject ||
      sessionA?.classIndex !== sessionB?.classIndex
  }

  openEditClassCodesModal(menuData: any, clickType = ''): void {
    this.openedModal = 'EditClassCodes';
    this.sessionMenuData = menuData;
    this.selectedCells = [];
    this.selectedSessionIds = [];
    this.editClassCodeForm.reset('');

    if (clickType) {
      this.clickType = clickType;
    }

    let sessionIds: number[] = [];
    if (this.clickType === 'session') {
      sessionIds = menuData.session?.classRelatedSessionIds ? menuData.session?.classRelatedSessionIds : [menuData.session.id];
    } else if (this.clickType === 'class') {
      sessionIds =
        menuData.block.blockTypeId === BLOCK_TYPE.Linear || menuData.block.blockTypeId === BLOCK_TYPE.Complex
          ? menuData.block.sessions.filter((item: ICurriculumSessions) => item.className === menuData.className)
            .map((item: ICurriculumSessions) => item.id)
          : menuData.block.sessions.filter((item: ICurriculumSessions) => item.className === menuData.className && item.subjectToYearGroupId === menuData.subjectToYearGroupId)
            .map((item: ICurriculumSessions) => item.id);
    } else if (this.clickType === 'subject') {
      const subjectId = menuData?.subject?.id;
      sessionIds = subjectId ? menuData.block.sessions.filter((item: ICurriculumSessions) => item.subjectToYearGroupId === subjectId)
        .map((item: ICurriculumSessions) => item.id) : menuData.block.sessions.map((item: ICurriculumSessions) => item.id);
    } else if (this.clickType === 'block') {
      sessionIds = menuData?.block.sessions.map((item: ICurriculumSessions) => item.id);
    }

    this.classNameDefinitionService
      .getPossibleClassNameItems()
      .pipe(
        switchMap((classNameDefinitions: IClassNameItems[]) => {
          this.classNameDefinitions = classNameDefinitions.map(item => {
            if (item.id === 5) {
              return {
                ...item,
                text: item.text.replace('(locked)', '(default)')
              }
            }
            return item;
          })

          return this.classNameDefinitionService.getClassCodesBySessionIds(sessionIds);
        })
      )
      .subscribe((classCodeRows) => {
        const sortedClassCodeRows = this.sortClassCodeArray(classCodeRows);
        this.classCodeRows = sortedClassCodeRows;
        this.columnDefs = sortedClassCodeRows[0].classcodes.map((item) => {
          const tmp = this.classNameDefinitions?.find(className => className.id === item.classNameId);
          return {
            field: item.classNameId.toString(),
            headerName: tmp?.id === 12 ? '[Space]' : tmp?.text,
            editable: !this.READONLY_IDS.includes(item.classNameId) ?? true,
            cellEditor: 'agTextCellEditor',
            cellEditorParams: {
              useFormatter: true,
              required: [
                ClassNameDefinitions.Band,
                ClassNameDefinitions.ClassNumber,
                ClassNameDefinitions.BlockCode
              ].includes(item.classNameId),
              minLength: [ClassNameDefinitions.Band].includes(item.classNameId) ? 1 : null,
              maxLength: this.getMaxLengthByClassNameId(item.classNameId)
            },
            flex: tmp?.id !== 12 && tmp?.type === CLASS_NAME_TYPE.OPTIONAL_SEPARATOR ? 0.5 : 1,
            minWidth: tmp?.id !== 12 && tmp?.type === CLASS_NAME_TYPE.OPTIONAL_SEPARATOR ? 60 : 120,
            menuTabs: tmp?.type === CLASS_NAME_TYPE.OPTIONAL_SEPARATOR ? [] : ['generalMenuTab'],
            wrapHeaderText: true,
            rowStyle: { 'border-bottom': '0' },
            cellStyle: (params: CellClassParams) => {
              return {
                backgroundColor: this.READONLY_IDS.includes(item.classNameId) ? '#F1F5F9' : ''
              }
            },
            cellClass: (params: CellClassParams) => {
              const rowIndex = params.rowIndex;
              const colIndex = classCodeRows[0].classcodes.findIndex(item => params.colDef.field?.toString() === item.classNameId.toString());
              const selected = this.selectedCells.find(selected => selected.colIndex === colIndex && selected.rowIndex === rowIndex && params);
              return selected ? 'selected-cell' : '';
            }
          };
        });

        this.gridApi.setRowData(this.formatClassCodesForGrid(this.classCodeRows));
        this.gridApi.sizeColumnsToFit();
        setTimeout(() => {
          this.editClassCodesModalRef.nativeElement.show();
        }, 100)

      });
  }

  openLinkBlocksModal(blockId: number): void {
    if (blockId) {
      this.linkBlocksModalComponent.show(blockId);
    }
  }

  sortClassCodeArray(classCodeRows: IClassCodes[]): IClassCodes[] {
    return classCodeRows.sort((a, b) => {
      const classA = a.classcodes.find(item => item.classNameId === 3);
      const classB = b.classcodes.find(item => item.classNameId === 3);

      if (classA && classB) {
        return classA.text.localeCompare(classB.text);
      } else if (classA) {
        return -1;
      } else if (classB) {
        return 1;
      }

      return 0;
    });
  }

  onCellEditRequest(event: CellValueChangedEvent<{ [key: string]: string }>) {
    const rowIndex = event.rowIndex!;
    const field = event.colDef.field;
    const classCodeRows = this.classCodeRows;
    const currentElement = classCodeRows[rowIndex].classcodes
      .find(item => item?.classNameId.toString() === field?.toString());
    const newValue = this.newValueHelper(+field!, event.newValue);

    if (newValue.length === 0 && ClassNameDefinitions.FreeText.toString() != field?.toString() && ClassNameDefinitions.FreeTextLocked.toString() != field?.toString()) {
      return;
    }

    if (currentElement) {
      if (this.shiftKeyPressed) {
        this.gridApi.stopEditing(true);
        return;
      }

      const classCodes = this.classCodeRows;
      classCodes[rowIndex].classcodes.find(classCodeRows => classCodeRows.classNameId === currentElement.classNameId)!.text = newValue
      this.gridApi.setRowData(this.formatClassCodesForGrid(classCodes));

      currentElement['text'] = newValue;
      const result = this.classCodeRows[rowIndex!];
      result.classcodes = [currentElement];

      this.classNameDefinitionService
        .updateClassCodes([result])
        .pipe(switchMap(() => this.curriculumPlan.getBlock(this.sessionMenuData.blockId)))
        .subscribe(block => {
          this.curriculumPlanBlocks.stateChanged$.next();
          this.curriculumPlanBlocks.blockSideStateChanged$.next();
          this.curriculumPlan.currentSelectedBlock$.next(block);
          this.openEditClassCodesModal(this.sessionMenuData);
        });
    }
  }

  formatClassCodesForGrid(classcodes: IClassCodes[]): { [p: string]: string }[] {
    const formattedData: { [p: string]: string }[] = [];
    classcodes.forEach((item) => {
      const result: { [key: string]: string } = {};
      item.classcodes.forEach((classCode) => {
        const name: number = classCode.classNameId;
        result[name] = classCode.text;
      });
      formattedData.push(result);
    });
    return formattedData;
  }

  onGridReady(params: GridReadyEvent<{ [key: string]: string }>) {
    this.gridApi = params.api;
  }

  setSelectedRows() {
    this.selectedSessions = this.selectedSessions.map(session => {
      const tmpSession = session;
      if (this.colIndex) {
        const newValue = this.newValueHelper(tmpSession.classcodes[this.colIndex].classNameId, this.editClassCodeForm.value);

        if (newValue.length === 0) {
          return tmpSession;
        }

        tmpSession.classcodes[this.colIndex].text = newValue;
        tmpSession.classcodes = [tmpSession.classcodes[this.colIndex]];
      }
      return tmpSession;
    });

    this.classNameDefinitionService
      .updateClassCodes(this.selectedSessions)
      .pipe(switchMap(() => this.curriculumPlan.getBlock(this.sessionMenuData.blockId)))
      .subscribe(block => {
        this.openEditClassCodesModal(this.sessionMenuData);
        this.selectedSessionIds = [];
        this.selectedCells = [];
        this.editClassCodeForm.reset();
        this.curriculumPlanBlocks.stateChanged$.next();
        this.curriculumPlanBlocks.blockSideStateChanged$.next();
        this.curriculumPlan.currentSelectedBlock$.next(block);
        this.snackbar.saved();
      });
  }

  onRightClickContextMenu(_: any, event: any): void {
    const colIndex = event.target.ariaColIndex - 1;
    const rowIndex = event.target.offsetParent.ariaRowIndex - 2;
    this.updateSelectedCells({ colIndex, rowIndex });
  }

  updateSelectedCells(indexObj: { colIndex: number, rowIndex: number }): void {
    let headerClicked = false;
    if (indexObj.rowIndex < 0 && this.clickedHeaderColIndex) {
      indexObj.colIndex = this.classCodeRows[0].classcodes.findIndex(item => this.clickedHeaderColIndex === item.classNameId.toString());
      headerClicked = true;
    }

    if (indexObj.colIndex < 0) {
      return;
    }

    if (this.READONLY_IDS.includes(+this.columnDefs[indexObj.colIndex].field!)) {
      return;
    }

    if (this.selectedCells.length > 0 && indexObj.colIndex !== this.selectedCells[0].colIndex) {
      return;
    }

    this.colIndex = indexObj.colIndex;

    const selected: {
      colIndex: number,
      rowIndex: number
    } | undefined = this.selectedCells.find(selected => selected.colIndex === indexObj.colIndex && selected.rowIndex === indexObj.rowIndex);

    if (headerClicked) {
      if (this.selectedCells.length === this.classCodeRows.length && indexObj.colIndex === this.selectedCells[0].colIndex) {
        this.selectedCells = [];
      } else if (this.selectedCells.length === 0 || indexObj.colIndex === this.selectedCells[0].colIndex) {
        this.selectedCells = [];
        this.classCodeRows.forEach((_, index) => {
          this.selectedCells.push({ colIndex: indexObj.colIndex, rowIndex: index });
        })
      }
    } else {
      if (selected) {
        this.selectedCells = this.selectedCells.filter(selected => selected.colIndex !== indexObj.colIndex || selected.rowIndex !== indexObj.rowIndex);
      } else {
        this.selectedCells.push(indexObj);
      }
    }

    this.gridApi.redrawRows();
    this.selectedSessionIds = [];
    this.selectedSessions = [];

    this.selectedCells.forEach(selectedIndexes => {
      this.selectedSessionIds.push(this.classCodeRows[selectedIndexes.rowIndex].sessionId);
      this.selectedSessions.push(this.classCodeRows[selectedIndexes.rowIndex]);
    });

    this.field = this.columnDefs[indexObj.colIndex].field as unknown as ClassNameDefinitions;
    switch (+this.field) {
      case ClassNameDefinitions.Band: {
        this.editClassCodeForm.setValidators([
          Validators.required,
          Validators.maxLength(2)
        ]);
        break;
      }
      case ClassNameDefinitions.ClassNumber: {
        this.editClassCodeForm.setValidators([
          Validators.required,
          Validators.maxLength(2)
        ]);
        break;
      }
      case ClassNameDefinitions.BlockCode: {
        this.editClassCodeForm.setValidators([
          Validators.required,
          Validators.maxLength(3)
        ]);
        break;
      }
      case ClassNameDefinitions.FreeText: {
        this.editClassCodeForm.setValidators([
          Validators.maxLength(15)
        ]);
        break;
      }
      case ClassNameDefinitions.FreeTextLocked: {
        this.editClassCodeForm.setValidators([
          Validators.maxLength(15)
        ]);
        break;
      }
    }

    if (this.selectedCells.length === 0) {
      this.editClassCodeForm.reset();
      this.editClassCodeForm.setValidators([]);
    }

    this.clickedHeaderColIndex = '';
    this.editClassCodeForm.updateValueAndValidity();
  }

  resetToDefaultClassCode() {
    this.classNameDefinitionService
      .resetToDefaultClassCode({ sessionIds: this.selectedSessionIds })
      .pipe(switchMap(() => this.curriculumPlan.getBlock(this.sessionMenuData.blockId)))
      .subscribe(block => {
        this.resetSelections();
        this.openEditClassCodesModal(this.sessionMenuData);
        this.curriculumPlanBlocks.stateChanged$.next();
        this.curriculumPlanBlocks.blockSideStateChanged$.next();
        this.curriculumPlan.currentSelectedBlock$.next(block);
        this.snackbar.saved();
      });
  }

  resetSelections() {
    this.selectedSessionIds = [];
    this.selectedCells = [];
  }

  beforeModalClose(): void {

    switch(this.openedModal) {
      case "Subject": {
        this.onBackToSubjects();
         break;
      }
      case "Staff": {
         this.onBackToStaffs();
         break;
      }
      case "Room": {
         this.onBackToRooms();
         break;
      }
   }

   this.openedModal = null;

  }

  beforEditClassCodesModalClose(): void {
    this.openedModal = null;
    this.gridApi.stopEditing(true);
  }

  onChangeToCurriculumTab(): void {
    this.changeToCurriculumTab.emit(true);
  }

  onChangeToSchedulingTab(): void {
    this.changeToSchedulingTab.emit(true);
  }

  autoAssign(): void {
    this.autoAssignModalRef.show();
  }

  private markPeriodsAndRemoveAMPMPeriods(periodStructure: IWithPeriodStructureResponse) {
    const filtered = {
      ...periodStructure,
      periodStructure: {
        ...periodStructure.periodStructure,
        weeks: periodStructure.periodStructure.weeks.map((week) => {
          week.days = week.days.map((day) => {
            day.periods.forEach((period, index, periods) => {
              period.isAM = index === 0 ? true : (period.periodCode === 'SESSION' ? !(day.periods[index - 1].isAM) : day.periods[index - 1].isAM);
            });
            day.periods = day.periods
              .filter((period) => !(period.periodCode === 'SESSION'));
            return day;
          });
          return week;
        })
      }
    };
    return filtered;
  }

  private newValueHelper(classNameId: ClassNameDefinitions, newValue: string): string {
    switch (classNameId) {
      case ClassNameDefinitions.FreeText: {
        newValue = newValue.replace(/[^a-zA-Z0-9 ]/g, '');
        break;
      }
      case ClassNameDefinitions.FreeTextLocked: {
        newValue = newValue.replace(/[^a-zA-Z0-9 ]/g, '');
        break;
      }
      case ClassNameDefinitions.ClassNumber: {
        newValue = newValue.replace(/[^0-9]/g, '');
        break;
      }
    }
    return newValue;
  }


  private getMaxLengthByClassNameId(classNameId: number): number {
    switch (classNameId) {
      case ClassNameDefinitions.Band: {
        return 2;
      }
      case ClassNameDefinitions.ClassNumber: {
        return 2;
      }
      case ClassNameDefinitions.BlockCode: {
        return 3;
      }
      case ClassNameDefinitions.FreeText: {
        return 15;
      }
      case ClassNameDefinitions.FreeTextLocked: {
        return 15;
      }
      default:
        return 15;
    }
  }

  private afterBackendCall(block: IListOfBlocksResponse, blockData: IListOfBlocksResponse[] | null) {
    blockData?.map((originalBlock) => (originalBlock.id === block.id ? block : originalBlock));
    this.blocksData = this.blocksData!.map(b => {
      return b.id === block.id ? { ...block } : b;
    });

  }

  openCheckForMissingStaffRoom(): void {
    this.checkForMissingComponent.show();
  }

  openClearCurriculum(): void {
    this.clearCurriculumWarningModal.show();
  }

  openOverviewsModal(): void {
    this.overviewModal.show(this.projectId, this.timetableId);
  }

  onClearCurriculum(): void {
    this.curriculumPlanBlocks.clearCurriculumPlan(this.timetableId).subscribe(() => {
      this.bandService.getListOfBandsByYearGroup(this.timetableId, this.selectedYearGroup).subscribe();
      this.curriculumPlanBlocks.stateChanged$.next();
      this.curriculumPlan.selectedYearGroup$.next(this.curriculumPlan.selectedYearGroup$.getValue());
      this.snackbar.success(this.translate.instant('Curriculum Plan was cleared successfully.'));
    })
  }

  findBlocksForSessionIds(sessionIds: number[]): IListOfBlocksResponse[] {
    const sessions = this.blocksData?.map((block) => block.sessions)
      .flat()
      .map((session) => {
        return {
          ...session,
          block: this.blocksData?.find(
            (block) => !!block.sessions?.find((blockSession) => blockSession.id === session?.id)
          )
        };
      }) as ISessionWithBlocks[];

    const blocks = sessions.filter((sessionItem) => sessionIds.indexOf(sessionItem.id) > -1).map(sessionItem => sessionItem.block);
    return blocks;
  }

  updateData() {
    this.curriculumPlanBlocks.blockSideStateChanged$.next();
    this.curriculumPlanBlocks.stateChanged$.next();
    const currentSelectedBlock = this.curriculumPlan.currentSelectedBlock$.getValue();
    if (currentSelectedBlock) {
      this.curriculumPlan.getBlock(currentSelectedBlock.id)
        .subscribe(block => this.curriculumPlan.currentSelectedBlock$.next(block))
    }
  }

  refreshData(): void{
    this.schedulingService.classNameDefinition$.subscribe((flag) => {
        if(flag)
        {
          this.updateData();
          this.schedulingService.classNameDefinition$.next(false);
        }
    });
  }

  importOptions(): void {
    this.importOptionsMessage = this.translateService.instant('Would you like to update the Timetable for Year ') + this.getSelectedYearGroup() + this.translateService.instant(' with the Options pattern?');
    this.importOptionPatternsWarningModal.show();
  }

  onContinueImport(): void {
    this.importOptionPatternsWarningModal.hide();

    const selectedYearGroup = this.curriculumPlan.selectedYearGroup$.getValue();
    const yearGroupIds: number[] = [];
    if (selectedYearGroup != null) {
      yearGroupIds.push(selectedYearGroup);
    }
    const payload: IImportOptionPatterns = { yearGroupIds: yearGroupIds, isPreview: false };
    this.planningRelationships
      .importOptionPatterns(this.timetableId, payload)
      .subscribe({
        next: response => {
          this.message = "";
          if (response.blockNames.length == 0 && response.importedYearGroupIds.length == 0) {
            this.snackbar.info(this.translateService.instant('No Options pattern to import.'));
          } else {

            if (response.blockNames.length > 0) {
              this.message = response.blockNames.join(', ') + this.translateService.instant(" has not been imported because not all of its subjects are active in the project. ");
            }

            if (response.importedYearGroupIds.length > 0) {
              const result: number[] = response.importedYearGroupIds;
              const importedYearGroupIds = this.yearGroups
                .filter(yg => result.includes(yg.id))
                .map(yg => yg.name);
              this.message = this.message + this.translateService.instant('Options pattern imported for Year ' + importedYearGroupIds.join(', ') + '.');
            }


            if (response.blockNames.length > 0) {
              this.snackbar.info(this.message);
            } else {
              this.snackbar.success(this.message);
            }
            this.curriculumPlanBlocks.stateChanged$.next();
          }

        }, error: () => {
          this.snackbar.error();
        }
      })

  }

  getSelectedYearGroup() {
    const year = this.curriculumPlan.selectedYearGroup$.getValue();
    return this.yearGroups
      .filter(yg => yg.id == year)
      .map(yg => yg.name);

  }

  getSelectedMISYearGroup() {
    const year = this.curriculumPlan.selectedYearGroup$.getValue();
    const ncYearGroupId = this.yearGroups
      .filter(yg => yg.id == year)
      .map(yg => yg.ncYearGroupId);

    return this.misYearGroups
      .filter(yg => yg.id == ncYearGroupId[0])
      .map(yg => yg.name)

  }

  onCancelBandUpdate(): void {
    this.planContainer.bandSidePanel.onCancelBandName();
  }
}
