﻿.reasons-container{
  .multiple-reason-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-width: 250px;

    .reason-cell {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      cursor: pointer;
    }

    .reason-cell {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      cursor: pointer;
    }

    .reasons {
      display: flex;
      flex-direction: column;
      word-break: break-word;
    }

    .reason {
      word-break: break-word;
      margin-right: 5px;
    }

    .show-reasons-button-container{
      width: 24px;
      height: 24px;
      margin-right: 5px;
    }

    .show-reasons-button-container .bcm-button {
      width: inherit;
      height: inherit;
    }

    ::ng-deep .show-reasons-button-container .bcm-button__container-kind-solid {
      color: #374151;
      background-color: #F3F4F6;
      border-color: #D1D5DB;
      min-width: 24px;
      min-height: 24px;
    }

    ::ng-deep .show-reasons-button-container .bcm-button__container-kind-solid:disabled {
      color: #6B7280;
      background-color: #D1D5DB;
      border-color: #D1D5DB;
      min-width: 24px;
      min-height: 24px;
    }
  }
}
