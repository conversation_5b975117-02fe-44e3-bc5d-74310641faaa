@import "apps/bromcom-timetables/src/assets/styles/variables";

.list {
  border-radius: 4px;
  border: 1px solid $color-blue-grey-200;
  min-height: 386px;

  .header {
    display: flex;
    justify-content: space-between;
    height: 48px;
    padding-left: 16px;
    padding-right: 16px;
    background: $color-blue-grey-100;
    border-radius: 4px 4px 0 0;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: $color-blue-grey-600;
    min-width: 386px;

    .part-one {
      display: flex;
      align-items: center;
    }

    .part-two {
      display: flex;
      align-items: center;
      font-style: normal;
      font-weight: 500;
      font-size: 12px;
      line-height: 20px;
      color: $color-blue-600;

      .disabled {
        color: $color-blue-grey-300;
      }

      div {
        width: 50px;
      }

      .margin-right {
        margin-right: 14px !important;
      }
    }

    .checkbox {
      width: 16px;
      height: 16px;
      margin-left: 10px;
      margin-right: 16px;
      cursor: pointer;
    }

  }

  .content {
    display: flex;
    flex-direction: column;
    padding: 8px 16px;
    background: $color-white-0;
    overflow-y: auto;
    height: 429px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}

.drag-entered:hover {
  border-radius: 4px;
  border: 2px dashed $color-blue-500;
  background: $color-blue-100;
}

.empty-drag-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 32px 12px;
  height: 114px;
  background: $color-blue-50;
  border: 2px dashed $color-blue-300;
  border-radius: 8px;

  .empty-drag-disabled {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .empty-header {
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      color: $color-blue-grey-400;
      background: $color-blue-50;
    }

    .description {
      font-style: normal;
      font-weight: 500;
      font-size: 12px;
      line-height: 20px;
      display: flex;
      align-items: center;
      color: $color-blue-grey-400;
    }
  }
}

.wrapper {
  width: 100%;
}

bromcom-box-drag {
  width: 100%;
}
