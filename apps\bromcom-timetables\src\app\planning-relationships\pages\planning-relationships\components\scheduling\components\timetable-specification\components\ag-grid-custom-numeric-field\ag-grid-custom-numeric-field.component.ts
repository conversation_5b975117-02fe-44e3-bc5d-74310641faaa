import { AfterViewInit, Component, ElementRef, ViewChild  } from '@angular/core';
import { ICellEditorParams } from 'ag-grid-community';

interface IAgGridNumericCellEditorComponent extends ICellEditorParams {
  placeholder: string
}

@Component({
  selector: 'bromcom-ag-grid-custom-numeric-field',
  templateUrl: './ag-grid-custom-numeric-field.component.html',
  styleUrls: ['./ag-grid-custom-numeric-field.component.scss'],
})
export class AgGridCustomNumericFieldComponent implements AfterViewInit {
  @ViewChild('numeric') numericElement: ElementRef = {} as ElementRef;
  value = 0;
  params!: IAgGridNumericCellEditorComponent;

  get nativeElement() {
    return this.numericElement?.nativeElement;
  }

  get placeholder() {
    return this.params?.placeholder;
  }

  agInit(params: IAgGridNumericCellEditorComponent): void {
    this.params = params;
    this.value = params.value;
  }

  ngAfterViewInit(): void {
    this.nativeElement.set(this.value);
  }

  getValue(): number {
    return +this.value ?? 0;
  }

  integerControl(event: KeyboardEvent): void {
    if ([190, 188, 69, 107, 109, 187, 189].includes(event.keyCode)) {
      event.preventDefault();
    }
  }

  updateValue(): void {
    this.nativeElement.get().then((data: number) => {
      if (data < 0) {
        this.nativeElement.setValue(0)
        this.value = 0
      } else {
        this.value = data;
      }
      
      const newValue = data < 0 ? 0 : data;
      const oldValue = this.value;
      
      const field = this.params.colDef.field;
      if (field) {
        this.params.data[field] = newValue;
      }
      const event = {
        type: 'cellValueChanged',
        data: this.params.data,
        node: this.params.node,
        oldValue,
        newValue,
        colDef: this.params.colDef,
        column: this.params.column,
        columnApi: this.params.columnApi,
        api: this.params.api,
      };
      this.params.api.dispatchEvent(event);
      
    });
    
  }

  clearValue(): void {
    this.nativeElement.set(null)
  }
}
