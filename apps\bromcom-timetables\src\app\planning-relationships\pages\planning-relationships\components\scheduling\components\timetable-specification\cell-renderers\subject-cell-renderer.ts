import { ICellRendererComp, ICellRendererParams } from 'ag-grid-community';
import { ISubject } from '../../../../../../../../_shared/models/ISubject';

export class SubjectCellRenderer implements ICellRendererComp {
  eGui: HTMLSpanElement;
  value: any;
  subject!: ISubject;

  constructor() {
    this.eGui = document.createElement('div');
  }

  init(params: any) {
    this.value = params.value;
    this.subject = params.subject;
    this.updateSubject();
  }

  updateSubject(): void {
    this.eGui.style.display = 'flex';
    this.eGui.style.alignItems = 'center';
    this.eGui.style.width = '100%';
    this.eGui.innerHTML = `
        <div style="width: 20px; height: 20px; background-color: ${this.subject?.color}; border-radius: 50%; margin-right: 16px; border: 1px solid black"></div>
        <div style="width: 82%; font-weight: 400; display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
            ${this.subject?.code} - ${this.subject?.name}
        </div>`;
  }

  getGui() {
    return this.eGui;
  }

  refresh(params: ICellRendererParams) {
    this.value = params.value;

    this.eGui.innerHTML = '';
    this.updateSubject();

    return true;
  }
}
