import { ICellRendererComp, ICellRendererParams } from 'ag-grid-community';

export class YearGroupsCellRenderer implements ICellRendererComp {
  eGui: HTMLSpanElement;
  value: any;
  yearGroups!: string[];

  constructor() {
    this.eGui = document.createElement('div');
  }

  init(params: any) {
    this.value = params.value;
    this.yearGroups = params.yearGroups;
    this.updateYearGroup();
  }

  updateYearGroup(): void {
    this.eGui.style.width = '100%';
    let innerHTML = ''
    this.yearGroups.forEach((year) => {
      const element = `<bcm-chip color="blue" style="margin-right: 8px">${year}</bcm-chip>`
      innerHTML += element;
    });
    this.eGui.innerHTML = `<div style="width: 100%; display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">${innerHTML}</div>`;
  }

  getGui() {
    return this.eGui;
  }

  refresh(params: ICellRendererParams) {
    this.value = params.value;

    this.eGui.innerHTML = '';
    this.updateYearGroup();

    return true;
  }
}
