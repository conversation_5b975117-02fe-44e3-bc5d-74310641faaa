# Mock server

## How to create new mock endpoint:

* add a new json file to the `data` folder *(use flat structure without folders)*
* add the endpoint definition to the `routes.js`
  * usage: `server.<method name>` *(`get`, `post`...etc)*

**JSON response:**
```js
server.get('/api/enpoint', (_, res) => {
    res.json(db.get('file_name_from_data_folder_without_extension').value());
});
```

**JSON response with a custom paramater:**
```js
server.post('/api/enpoint/:id/list', (_, res) => {
    res.json(db.get('file_name_from_data_folder_without_extension').value());
});

server.post('/api/enpoint/:id/list', (req, res) => {
    if (req.params.id === 10) res.json(db.get('file_name_from_data_folder_without_extension').value());
    else res.json(db.get('file_name_from_data_folder_without_extension').value());
});
```

**Download file from the endpoint**
```js
server.post('/api/download/file', (req, res) => {
    const file = `${__dirname}/files/test.pdf`;  
    res.download(file);
});
```

**Response with a 202 http status**
```js
server.post('/api/endpoint', (req, res) => {
  res.status(202).end();
});
```

## How to run mock server

Run `npm run mock`

`MOCK SERVER is running on 9988`


## Official docs
https://github.com/typicode/json-server
