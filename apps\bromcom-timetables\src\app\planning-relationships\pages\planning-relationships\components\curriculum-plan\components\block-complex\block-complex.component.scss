@import '../../../../../../../../assets/styles/variables';
@import '../../../../../../styles/blocks';

.block-container {
  margin: 8px;
  min-height: 168px;
  background: $color-blue-grey-100;
  border-radius: 4px;

  &:not(.active-block) {
    border: 2px solid $color-white-0;
  }

  .icon {
    font-size: 14px;
    cursor: pointer;
  }

  .header {
    @include header;
    display: flex;
    justify-content: space-between;

    .menu-container {
      @include flex;

      .menu {
        @include flex;
        width: 32px;
        height: 24px;
      }

      .full-size {
        @include flex;

        width: 30px;
        height: 24px;
      }
    }
  }

  .subject-code-color-header {
    @include text-style;
    @include flex;
    flex-wrap: wrap;
    border-bottom: 1px solid $color-blue-grey-200;
    width: 100%;
    overflow: hidden;
    max-width: 376px;

    ::ng-deep &.drop-active {
      max-width: 480px !important;
    }

    .code-color-header {
      @include flex;
      min-width: 50px;
      height: 24px;

      flex: 1 1 0;

      .subject-code {
        padding: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .block-type-full-name,
  .period-count {
    @include flex;
    @include text-style;
    @include full-name-style;
  }

  .complex-body-container {
    display: flex;
    flex-direction: row;

    .column {
      @include flex;
      flex-direction: column;

      ::ng-deep .bottom-add-new-subject-row {
        min-height: 0 !important;
        @include flex;
        @include session-info-container;

        .display-none {
          background-color: $color-red-tertiary-600;
          display: none !important;
        }

        &.hide-add-new-block {
          height: 0 !important;
          width: 0;
          visibility: hidden;
        }

        &.hide-add-new-block-three-subject {
          width: 104px;
          visibility: hidden;

          margin-left: 0;
        }

        .set-visible {
          visibility: visible;
        }
      }
    }

    .subject-row-container {

      .class-row-container {
        @include subject-info-container;
        @include session-info-container;

        .subject-info-container {
          height: 40px;

          .selected {
            border: 1px dashed $color-blue-800;
          }
        }

        .session-info-container-center {
          &.left-double {
            border-left: 1px solid $color-blue-grey-200;
            border-right: 1px solid $color-blue-grey-50;
          }

          &.right-double {
            border-left: 1px solid $color-blue-grey-50;
            border-right: 1px solid $color-blue-grey-200;
          }
        }

        .display-none {
          background-color: $color-red-tertiary-600;
          display: none !important;
        }
      }
    }

    ::ng-deep .subject-info-container-side {
      @include flex;
      flex-direction: column;
      width: 32px;

      .icon {
        @include flex;
        width: 32px;
        height: 40px;
        cursor: pointer;
        color: $color-blue-grey-700;
        border-bottom: 1px solid $color-blue-grey-200;

        i.fa-arrow-to-left,
        i.fa-arrow-to-right {
          margin-top: 4px;
          font-size: 16px;
        }
      }

      .class-name {
        @include flex;
        @include text-style;
      }

      .remove-class {
        @include remove-class;
      }
    }

    .session-info-container-side {
      width: 32px;
      min-height: 40px;
      border-bottom: 1px solid $color-blue-grey-200;
      background: $color-blue-grey-100;

      &.dashed {
        border: 2px dashed $color-blue-800 !important;
        pointer-events: auto;

        &:hover {
          background-color: $color-blue-grey-300 !important;
        }
      }
    }
  }

  .bottom {
    width: 100%;
    height: 16px;
    border-radius: 0 0 4px 4px;
  }
}

.hide-add-new-block {
  display: flex;
  width: 0;
  z-index: 1000;

  visibility: hidden;
}

.hide-add-new-block-bottom {
  height: 0;
  visibility: hidden;
  z-index: 1000;
  align-items: flex-start !important;
}

.set-visible {
  visibility: visible;
}

.disable {
  color: $color-blue-grey-300 !important;
  pointer-events: none;
}
