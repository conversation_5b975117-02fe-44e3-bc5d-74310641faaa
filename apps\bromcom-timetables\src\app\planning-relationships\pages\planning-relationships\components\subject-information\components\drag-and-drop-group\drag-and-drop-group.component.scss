@import "apps/bromcom-timetables/src/assets/styles/variables";

.content {
  ::ng-deep .bcm-button__container-text {
    color: $color-blue-grey-600;
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    height: 32px;
    background: $color-blue-grey-200;
    border-radius: 4px;
    margin-bottom: 4px;
    font-style: normal;
    font-size: 14px;
    line-height: 22px;
    color: $color-blue-grey-600;

    ::ng-deep .bcm-caption-area {
      display: none;
    }
  }

  display: flex;
  flex-direction: column;
  background: $color-white-0;
}

.drag-entered:hover {
  border-radius: 4px;
  border: 2px dashed $color-blue-500;
  background: $color-blue-100;
}

.box {
  cursor: move;
  display: flex;
  align-items: center;
  background: $color-blue-grey-100;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: $color-blue-grey-600;

  img {
    margin-right: 14px;
  }
}

.empty-drag-wrapper {
  display: flex;
  text-align: center;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 12px;
  height: 114px;
  background: $color-blue-50;
  border: 2px dashed $color-blue-300;
  border-radius: 8px;
  
  &.has-close-icon {
    padding-top: 5px !important;
    justify-content: initial !important;
  }
  
  .icon-holder {
    display: flex;
    width: 100%;
    justify-content: end;
    
    .icon {
      color: $color-blue-grey-600;
    }
  }

  .empty-drag {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .header {
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      color: $color-blue-grey-600;

      &.disabled {
        color: $color-blue-grey-400;
      }
    }

    .description {
      font-style: normal;
      font-weight: 500;
      font-size: 12px;
      line-height: 20px;
      display: flex;
      align-items: center;
      color: $color-blue-grey-500;

      &.disabled {
        color: $color-blue-grey-400;
      }
    }
  }
}

.wrapper {
  width: 100%;
  margin-bottom: 8px;
  overflow: hidden;
  display: block;
  &:not(.collapsed) {
    min-height: 60px;
  }
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
  0 8px 10px 1px rgba(0, 0, 0, 0.14),
  0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.example-box:last-child {
  border: none;
}

.box {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .first-part {
    display: flex;
    align-items: center;
  }

  img {
    cursor: pointer;
  }
}

.part-one {
  display: flex;
  align-items: center;

  img {
    margin-right: 8px;
  }

  .title {
    display: flex;
    align-items: center;

    ::ng-deep .bcm-input {
      margin-bottom: 0;
      margin-top: 5px !important;
    }
  }

  .info {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 8px;
    cursor: pointer;
  }
}

.cdk-drop-list-dragging .example-box:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.action {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.modal-container {
  padding: 24px;

  label {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: $color-blue-grey-600;
  }

  .input {
    display: flex;
    width: 100%;
    height: 34px;
    padding-left: 10px !important;
    border: 1px solid $color-blue-grey-300;
    border-radius: 4px;
  }
}

.expand-icons {
  margin-right: 8px;
  font-size: 14px;
  padding: 2px;
  cursor: pointer;
}

::ng-deep .linked-block-action-modal {

  button {
    cursor: pointer;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .text {
      color: $color-blue-grey-600;
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }

    &:hover {
      background-color: $color-blue-grey-100;
    }
  }

  .disabled {
    pointer-events: none;
  }

}

