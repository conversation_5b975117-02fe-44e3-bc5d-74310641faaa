import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CurriculumPlanOverviewModalComponent } from './curriculum-plan-overview.component';

describe('CurriculumPlanOverviewModalComponent', () => {
  let component: CurriculumPlanOverviewModalComponent;
  let fixture: ComponentFixture<CurriculumPlanOverviewModalComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [CurriculumPlanOverviewModalComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(CurriculumPlanOverviewModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should correctly calculate the total periods', () => {
    const totalPeriods = 5 * 5; // 5 days with 5 periods each
    expect(component.totalBandCount).toBe(25);
  });


  it('should display tooltip when the band count exceeds the maximum', () => {
    const col = {
      cellType: 'PeriodCount',
      content: '26',
      tooltip: 'Total band count exceeds the allowed maximum.',
    };
    const tooltip = component.showToolTip(col);
    expect(tooltip).toBe('Total band count exceeds the allowed maximum.');
  });

  it('should not display tooltip when the band count equals the maximum', () => {
    const col = {
      cellType: 'PeriodCount',
      content: '25',
      tooltip: '',
    };
    const tooltip = component.showToolTip(col);
    expect(tooltip).toBe('');
  });

  it('should not display tooltip when the band count is less than the maximum', () => {
    const col = {
      cellType: 'PeriodCount',
      content: '20',
      tooltip: '',
    };
    const tooltip = component.showToolTip(col);
    expect(tooltip).toBe('');
  });

  it('should highlight the cell red when the band count exceeds the maximum', () => {
    const col = {
      cellType: 'PeriodCount',
      content: '26',
      color: 'FF0000',
    };
    const className = component.isColorDarkHelper(col.color);
    expect(className).toBe(true);
  });
});
