import { Directive, ElementRef, Renderer2, ViewChild } from '@angular/core';

@Directive()
export abstract class BaseTwoPaneComponent {
  @ViewChild('slider', { static: true }) sliderRef!: ElementRef;
  @ViewChild('left', { static: true }) leftRef!: ElementRef;
  @ViewChild('right', { static: true }) rightRef!: ElementRef;

  private isResizing = false;
  isLeftBoxVisible = true;

  constructor(private renderer: Renderer2) {}

  onMouseDown(event: MouseEvent): void {
    this.isResizing = true;
    const startX = event.clientX;
    const startLeftWidth = this.leftRef.nativeElement.offsetWidth;
    const startRightWidth = this.rightRef.nativeElement.offsetWidth;

    this.renderer?.addClass(this.sliderRef.nativeElement, 'active');

    const onMouseMove = (event: MouseEvent) => {
      if (this.isResizing) {
        const diff = event.clientX - startX;
        const newLeftWidth = startLeftWidth + diff;
        const newRightWidth = startRightWidth - diff;

        this.leftRef.nativeElement.style.flexBasis = `${newLeftWidth}px`;
        this.rightRef.nativeElement.style.flexBasis = `${newRightWidth}px`;
      }
    };

    const onMouseUp = () => {
      this.isResizing = false;
      this.renderer.removeClass(this.sliderRef.nativeElement, 'active');
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }
}
