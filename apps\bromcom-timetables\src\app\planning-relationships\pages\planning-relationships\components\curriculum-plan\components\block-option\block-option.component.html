<div class="block-container"
     [ngClass]="{'active-block': isActiveBlock}"
     (click)="selectActiveSideBlock()"
     (mouseenter)="onMouseEnter()"
     (mouseleave)="onMouseLeave()">
  <div class="header">
    <div class="block-type-short-name">
      <div class="circle">{{BLOCK_TYPE[block.blockTypeId]}}</div>
    </div>
    <div class="block-name">{{block.blockCode}}</div>
    <div class="menu">
      <bcm-icon (click)="openBlockActionsMenu()" class="icon" icon="far fa-ellipsis-h"></bcm-icon>
      <bromcom-curriculum-plan-block-actions [block]="block"
                                             (transformClicked)="transformBlock($event)"
                                             (copyClicked)="copyBlock($event)"
                                             (deleteClicked)="deleteBlock(this.block.id)"
                                             (unScheduleClicked)="unScheduleSessions($event)"
                                             (spreadToBandsClicked)="spreadToBands($event)"
                                             (splitToBandsClicked)="splitToBands($event)"
                                             (openEditClassCodesModalEvent)="openEditClassCodesModal($event)"
                                             (openLinkBlocksModalComponentEvent)="openLinkBlocksModal($event)"
                                             [openBlockActionsMenu$]="openBlockActionsMenu$"></bromcom-curriculum-plan-block-actions>
    </div>
    <div class="full-size">
      <bcm-icon class="icon" icon="far fa-expand-alt" (click)="expandBlock()"></bcm-icon>
    </div>
  </div>

  <div class="block-type-full-name">
    <div class="linked-icon" *ngIf="block.linkedPairID" [matTooltip]="(linkedBlockName$ | async) || '-'"
         [matTooltipPosition]="'above'" (mouseenter)="getLinkedBlockName()">
      <bcm-icon class="icon"
                icon="far fa-link"></bcm-icon>
    </div>

    <div class="name"
         [matTooltipDisabled]="block.blockName.length < 15"
         [matTooltip]="block.blockName"
         [matTooltipPosition]="'above'">
      {{block.blockName}}
    </div>

    <div class="cross-band-icon" *ngIf="block.bandIds.length > 1" [matTooltip]="bandNames"
         [matTooltipPosition]="'above'">X
    </div>
  </div>

  <div class="period-count">{{block.periodCount}}</div>

  <div *ngFor="let subject of block.subjectToYearGroups, index as subjectIndex">
    <div class="subject-info-container">
      <div class="subject-info-container-side"></div>
      <div [matMenuTriggerFor]="subjectMenuRef"
           (menuOpened)="subjectMenuOpened({data: {block, blockId: block.id, subject, showExpandedViewButtons: false}})"
           [class.remove-subject]="subject.code && !isSubjectDraggingActive">
        <div class="subject-info-container-center"
             [class.dashed]="isHovered && isSubjectDraggingActive && !subject.subjectId"
             cdkDropList
             [ngStyle]="{'background-color': '#' + subject?.color, 'color': '#' + subject?.textColor}"
             [id]="subject.subjectId ? block.id.toString() : 'emptySubjectPlaceholder' + block.id"
             (cdkDropListDropped)="dropSubject($event)"
             [cdkDropListData]="block.subjectToYearGroups">
          <div class="subject-name">{{ subject.code ?? '--' }}</div>
          <div class="subject-periods">
            <div class="single-period">S: {{ subject.singlePeriodCount ?? '--' }}</div>
            <div class="double-period">D: {{ subject.doublePeriodCount ?? '--' }}</div>
          </div>
        </div>
      </div>
      <div class="subject-info-container-side"></div>
    </div>

    <div class="session-info-container" *ngFor="let session of transformedSessions[subjectIndex]; let i = index">
      <div class="session-info-container-side class-name"
           [class.remove-class]="transformedSessions[subjectIndex].length > 1"
           [matMenuTriggerFor]="classContextMenuRef"
           [matMenuTriggerData]="{data: {block, blockId: block.id, subjectToYearGroupId: session.subjectToYearGroupId, className: session.className, disableRemoveClass: transformedSessions[subjectIndex].length === 1}}">
        {{session.className}}
      </div>

      <div class="session-info-container-center"
           [matMenuTriggerFor]="sessionMenuRef"
           (menuOpened)="sessionMenuOpened({block, blockId: block.id, blockMenuId: session.id, session})">


        <div class="staff-side"
             [class.dashed]="isHovered && isStaffDraggingActive &&
             ((!shiftKeyPressed && (block.sessions | hasEmptyStaffSessionsClass:session.subjectToYearGroupId:session.className)) || shiftKeyPressed) &&
             (block | getSubjectToSession:session.subjectToYearGroupId).subjectId &&
             (block | getSubjectToSession: session.subjectToYearGroupId).subjectId !== freeSubjectId"
             cdkDropList
             [id]="'staff' + session.id"
             (cdkDropListDropped)="dropStaff($event, subjectIndex)"
             [cdkDropListData]="session.staffIds"
             (mouseenter)="bulkInformation($event, session.className, session.subjectToYearGroupId, block.sessions, !this.shiftKeyPressed, isStaffDraggingActive)"
             (mouseleave)="staffDragLeaveInformation()">

          <bromcom-simple-view-staff-section [sessions]="block.sessions"
                                             [session]="session"></bromcom-simple-view-staff-section>
        </div>

        <div class="room-side"
             [class.dashed]="isHovered && isRoomDraggingActive &&
             (block.sessions | hasEmptyRoomSessionsClass:session.subjectToYearGroupId:session.className) &&
             (block | getSubjectToSession:session.subjectToYearGroupId).subjectId &&
             (block | getSubjectToSession: session.subjectToYearGroupId).subjectId !== freeSubjectId"
             cdkDropList
             [id]="'room' + session.id"
             (cdkDropListDropped)="dropRoom($event, subjectIndex)"
             [cdkDropListData]="session.roomId"
             (mouseenter)="roomDragEnterBulkInformation($event, session.className, session.subjectToYearGroupId, block.sessions)"
             (mouseleave)="roomDragLeaveInformation()">
          <div class="session-room-icon">
            <bcm-icon class="icon" icon="far fa-map-marker-alt"></bcm-icon>
          </div>

          <div class="session-room"
               [matTooltip]="'Room: ' + ((session.allRoomCodes | joinRooms).length > 0 ? (session.allRoomCodes | joinRooms) : '--')"
               [matTooltipPosition]="'above'">{{session.allRoomCodes.includes('--') ? '--' : session.allRoomCodes.length === 1 ? session.allRoomCodes[0] : '**' }}
          </div>
        </div>


      </div>
      <div class="session-info-container-side"></div>
    </div>
  </div>

  <div *ngIf="!!block.subjectToYearGroups[0].subjectId"
       [ngClass]="{'hide-add-new-block': !isHovered || !isSubjectDraggingActive || !block.subjectToYearGroups[0].subjectId}">
    <bromcom-block-new-subject-placeholder [block]="block"
                                           [listIndex]="listIndex"
                                           [emptySubjectPlaceholderId]="'emptySubjectPlaceholder' + this.block.id"
                                           [classNames]="['']"
                                           (addSubjectToBlock)="dropSubject($event)"></bromcom-block-new-subject-placeholder>
  </div>

  <div class="bottom"></div>
</div>
