import { Compo<PERSON>, <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Renderer2 } from '@angular/core';
import { ICellRendererParams } from 'ag-grid-community';

@Component({
  selector: 'bromcom-ncc-staff-issue-cell-renderer',
  template: `
    <div class="cell-wrapper"
         (mouseenter)="showPopup()"
         (mouseleave)="hidePopup()"
    >
      <span class="staff-link">{{ value }}</span>
    </div>
  `,
  styles: [`
    .cell-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
    }
    .staff-link {
      color: #007bff;
      text-decoration: underline;
      cursor: pointer;
      white-space: nowrap;
    }
  `]
})
export class NccStaffIssueCellRendererComponent implements OnDestroy {
  value = '';
  staffNames: string[] = [];

  private popupEl: HTMLElement | null = null;
  private cellEl: HTMLElement | null = null;
  private isPopupHovered = false;
  private isCellHovered = false;

  constructor(private renderer: Renderer2, private host: ElementRef) {}

  agInit(params: ICellRendererParams & {staffNames: string[]}): void {
    this.value = params.value;
    this.staffNames = params.staffNames;
    this.cellEl = params.eGridCell;
  }

  refresh(): boolean {
    return false;
  }

  showPopup() {
    this.isCellHovered = true;
    if (!this.popupEl) {
      this.createPopup();
    }
  }

  hidePopup() {
    this.isCellHovered = false;
    setTimeout(() => {
      if (!this.isPopupHovered && !this.isCellHovered) {
        this.closePopup();
      }
    }, 50);
  }

  createPopup() {
    if (!this.cellEl) return;

    const rect = this.cellEl.getBoundingClientRect();

    this.popupEl = this.renderer.createElement('div');

    this.renderer.setStyle(this.popupEl, 'position', 'fixed');
    this.renderer.setStyle(this.popupEl, 'background', 'white');
    this.renderer.setStyle(this.popupEl, 'border', '1px solid #ccc');
    this.renderer.setStyle(this.popupEl, 'padding', '8px');
    this.renderer.setStyle(this.popupEl, 'z-index', '9999');
    this.renderer.setStyle(this.popupEl, 'box-shadow', '0 2px 8px rgba(0,0,0,0.2)');
    this.renderer.setStyle(this.popupEl, 'borderRadius', '6px');
    this.renderer.setStyle(this.popupEl, 'maxWidth', '257px');
    this.renderer.setStyle(this.popupEl, 'maxHeight', '276px');
    this.renderer.setStyle(this.popupEl, 'overflowY', 'auto');

    for (let i = 0; i < this.staffNames.length; i++) {
      const item = this.staffNames[i];
      const div = this.renderer.createElement('div');
      const text = this.renderer.createText(item);

      this.renderer.setStyle(div, 'height', '40px');
      this.renderer.setStyle(div, 'width', '224px');
      this.renderer.setStyle(div, 'display', 'flex');
      this.renderer.setStyle(div, 'alignItems', 'center');
      this.renderer.setStyle(div, 'padding', '0 12px');
      this.renderer.setStyle(div, 'fontSize', '16px');
      this.renderer.setStyle(div, 'color', '#374151');
      this.renderer.setStyle(div, 'border', '1px solid #D1D5DB');

      if (i === 0) {
        this.renderer.setStyle(div, 'borderTopLeftRadius', '6px');
        this.renderer.setStyle(div, 'borderTopRightRadius', '6px');
      }
      if (i === this.staffNames.length - 1) {
        this.renderer.setStyle(div, 'borderBottomLeftRadius', '6px');
        this.renderer.setStyle(div, 'borderBottomRightRadius', '6px');
      }

      this.renderer.appendChild(div, text);
      this.renderer.appendChild(this.popupEl, div);
    }

    this.renderer.appendChild(document.body, this.popupEl);

    const popupRect = this.popupEl!.getBoundingClientRect();
    const left = rect.left + rect.width / 2 - popupRect.width / 2;
    const top = rect.top + rect.height / 2 + 8;

    this.renderer.setStyle(this.popupEl, 'left', `${left}px`);
    this.renderer.setStyle(this.popupEl, 'top', `${top}px`);

    this.renderer.listen(this.popupEl, 'mouseenter', () => {
      this.isPopupHovered = true;
    });
    this.renderer.listen(this.popupEl, 'mouseleave', () => {
      this.isPopupHovered = false;
      setTimeout(() => {
        if (!this.isCellHovered && !this.isPopupHovered) {
          this.closePopup();
        }
      }, 50);
    });
  }

  closePopup() {
    if (this.popupEl) {
      this.renderer.removeChild(document.body, this.popupEl);
      this.popupEl = null;
    }
  }

  ngOnDestroy(): void {
    this.closePopup();
  }
}
