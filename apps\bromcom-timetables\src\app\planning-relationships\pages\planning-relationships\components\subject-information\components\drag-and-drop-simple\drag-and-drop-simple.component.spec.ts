import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DragAndDropSimpleComponent } from './drag-and-drop-simple.component';

describe('SubjectListComponent', () => {
  let component: DragAndDropSimpleComponent;
  let fixture: ComponentFixture<DragAndDropSimpleComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DragAndDropSimpleComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(DragAndDropSimpleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
