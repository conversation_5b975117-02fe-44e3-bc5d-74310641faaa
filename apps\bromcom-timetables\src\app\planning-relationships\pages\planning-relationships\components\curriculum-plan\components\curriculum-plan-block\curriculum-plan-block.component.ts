import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { v4 as uuidv4 } from "uuid";
import { ISubject } from "../../../../../../../_shared/models/ISubject";
import { distinctUntilChanged, ReplaySubject, Subject, takeUntil } from "rxjs";
import { IBand } from "../../../../../../../_shared/models/IBand";
import { IYearGroup } from "../../../../../../../_shared/models/IYearGroup";
import { CurriculumPlanBlocksService } from "../../../../../../services/curriculum-plan-blocks";
import { ICurriculumPlanBlock } from "../../../../../../../_shared/models/ICurriculumPlanBlock";
import { BLOCK_TYPE_SHORT } from "../../../../../../../_shared/enums/BlockTypeShort";
import { DeleteConfirmationComponent } from '../../../common/delete-confirmation/delete-confirmation.component';
import { SnackbarService } from '@bromcom/ui';
import { TranslateService } from '@ngx-translate/core';
import { BaseBlockSimpleViewActions } from '../../_shared/base-block-simple-view-actions';
import { CurriculumPlanService } from '../../../../../../services/curriculum-plan.service';
import { RelationshipsService } from '../../../../../../services/relationships.service';
import { BLOCK_TYPE } from '../../../../../../../_shared/enums/BlockType';
import { InformationService } from '../../../../../../services/information.service';

@Component({
  selector: 'bromcom-curriculum-plan-block',
  templateUrl: './curriculum-plan-block.component.html',
  styleUrls: ['./curriculum-plan-block.component.scss']
})
export class CurriculumPlanBlockComponent extends BaseBlockSimpleViewActions implements OnInit, OnDestroy {
  @ViewChild('blockModal') blockModalRef!: ElementRef;
  @ViewChild('blockDeleteConfirmationComponent') blockDeleteConfirmationComponent!: DeleteConfirmationComponent;

  @Input() block!: ICurriculumPlanBlock;
  @Input() selectedYearGroup!: IYearGroup | undefined;
  @Input() bands!: IBand[];
  @Output() selectItemEvent = new EventEmitter<number>();
  @Input() allSubject: ISubject[] | undefined;
  blockMenuId = uuidv4();
  tooltipContent = '';
  subjects$ = new ReplaySubject<ISubject[]>();
  freeSubjectId: number | null = null;
  currentBands!: IBand[];
  bandNames = '';
  activeBlockId!: null | number;
  BLOCK_TYPE = BLOCK_TYPE_SHORT;
  openBlockActionsMenu$ = new Subject<number>();

  protected readonly unsubscribe$: Subject<void> = new Subject();

  constructor(
    protected override curriculumPlan: CurriculumPlanService,
    protected override curriculumPlanBlocks: CurriculumPlanBlocksService,
    protected override snackbar: SnackbarService,
    protected override translate: TranslateService,
    private relationshipsService: RelationshipsService,
    protected override informationService: InformationService
  ) {
    super(curriculumPlan, curriculumPlanBlocks, snackbar, translate, informationService)
  }

  ngOnInit(): void {
    this.subjects$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(subjects => {
        this.updateTooltipContent(subjects);
      });

    this.relationshipsService.freeSubjectId$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(id => {
        this.freeSubjectId = id;
      })

    this.curriculumPlanBlocks.selectedBlockId$
      .pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$))
      .subscribe(value => this.activeBlockId = value);

    const tmp = this.block.subjectToYearGroups.map(sub => {
      return this.allSubject?.find(item => sub.subjectId === item.id);
    });

    const filtered = tmp.filter(item => item !== undefined && !(this.block.blockTypeId === BLOCK_TYPE.Complex && item.id === this.freeSubjectId)) as ISubject[];
    this.subjects$.next(filtered);
    this.currentBands = [];
    this.block.bandIds?.forEach(band => {
      const tmp = this.bands?.find(b => b.id === band);
      if (tmp) {
        this.currentBands.push(tmp);
      }
    })
    if (this.selectedYearGroup) {
      this.bandNames = this.selectedYearGroup.name + this.block.bandIds.map(bandId => {
        const bandName = this.bands?.find(band => band.id === bandId)?.bandName;
        return `${bandName}`
      }).join('');
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  openBlockActionsMenu(): void {
    this.openBlockActionsMenu$.next(this.block.id);
  }

  select() {
    this.selectItemEvent.emit(this.block.id);
  }

  updateTooltipContent(subjects: ISubject[]) {
    this.tooltipContent = (this.block.blockName || '') + '<br /><div class="block-tooltip-wrapper">';
    subjects.forEach(item => {
      const part1 = '<div class="block-tooltip-line"><div class="block-tooltip-circle" style="background-color: #';
      const part2 = '">'
      const part3 = '</div>' + item.code + ' - ' + item.name + '</div>';
      this.tooltipContent = this.tooltipContent.concat(part1.concat(item.color || 'fff').concat(part2).concat(part3));
    });
    this.tooltipContent.concat('</div>');
  }
}
