import { IListOption, NOOP, transformToAGGridConfig } from '@bromcom/core';
import { AgGridNoDataComponent } from '@bromcom/ui';
import { CheckboxSelectionCallbackParams, ICellEditorParams, ICellRendererParams, IsFullWidthRowParams } from 'ag-grid-community';
import { AddNewRowComponent } from '../../../../../../../../../projects/pages/new-project-wizard/components/add-new-row/add-new-row.component';
import { TtsGridActionsComponent } from '../tts-grid-actions/tts-grid-actions.component';
import { AgGridPeriodDropdownComponent } from '../ag-grid-period-dropdown/ag-grid-period-dropdown.component';
import { PeriodsCellRenderer } from '../../cell-renderers/periods-cell-renderer';
import { TtsPartTimeStaffComponent } from './tts-part-time-staff.component';
import { SubjectCellRenderer } from '../../cell-renderers/subject-cell-renderer';
import {
    AgGridSubjectDropdownComponentComponent
  } from '../ag-grid-subject-dropdown-component/ag-grid-subject-dropdown-component.component';
import {
    AgGridDepartmentSubjectDropDownComponent
  } from '../ag-grid-department-subject-drop-down/ag-grid-department-subject-drop-down.component';
import { AgGridSelectDropdownComponent } from '../ag-grid-select-dropdown/ag-grid-select-dropdown.component';
import { AgGridCustomNumericFieldComponent } from '../ag-grid-custom-numeric-field/ag-grid-custom-numeric-field.component';
import { AgGridNumericComponent } from '@bromcom/ui';
import { SubjectsCellRenderer } from '../../cell-renderers/subjects-cell-renderer';

export function gridOptions(this: TtsPartTimeStaffComponent, config: any) {
  const {
    onAddNewRow = NOOP,
    onAcceptNewRow = NOOP,
    onCancelAddingNewRow = NOOP,
    onEditRow = NOOP,
    onDeleteRow = NOOP,
    onAcceptRow = NOOP,
    onCancelEditRow = NOOP,
    onExcludeRow = NOOP,
    onIncludeRow = NOOP
  } = config

  return transformToAGGridConfig({
    getRowId: params => params.data.id,
    animateRows: false,
    suppressRowClickSelection: true,
    suppressClickEdit: true,
    suppressCellFocus: true,
    domLayout: 'autoHeight',
    editType: 'fullRow',
    rowSelection: 'multiple',
    noRowsOverlayComponent: AgGridNoDataComponent,
    noRowsOverlayComponentParams: {
      style: 'display: flex; justify-content: center; align-items: center; height: 100%; width: 100%; min-height: 162px; padding: 48px 0 0px 0',
      noRowsMessageFunc: () => this.translate.instant(`No data available! No Part Time Staff were found.`)
    },
    pinnedBottomRowData: [{}],
    fullWidthCellRenderer: AddNewRowComponent,
    fullWidthCellRendererParams: {
      onAddNewRow,
      label: this.translate.instant('Add New Row')
    },
    tooltipShowDelay: 500,
    isFullWidthRow: (params: IsFullWidthRowParams) => {
      return !params.rowNode.data.id;
    },
    onSelectionChanged: () => {
      this.isRemoveBulkDisabled = !this.gridApi.getSelectedRows().length;
    },
    rowHeight: 56,
    columnDefs: [
      {
        minWidth: 48,
        width: 48,
        headerCheckboxSelection: true,
        checkboxSelection: (params: CheckboxSelectionCallbackParams<IListOption>) => {
          return !!params.data;
        }
      },
      {
        field: 'staffId',
        headerName: this.translate.instant('Staff'),
        minWidth: 178,
        flex: 1.5,
        editable: true,
        menuTabs: ['filterMenuTab'],
        tooltipValueGetter: params => params.data.staffId ? this.staffOptions.find(staff => staff.id === params.data.staffId)?.firstName + ' ' + this.staffOptions.find(staff => staff.id === params.data.staffId)?.lastName : null,
        valueGetter: params => {
          return this.staffOptions.find(staff => staff.id === params.data.staffId)?.firstName + ' ' + this.staffOptions.find(staff => staff.id === params.data.staffId)?.lastName
        },
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            staff: this.staffOptions.find(staff => staff.id === params.data.staffId)
          }
        },
        cellEditor: AgGridSelectDropdownComponent,
        cellEditorParams: (params: ICellEditorParams) => {
          return {
            placeholder: this.translate.instant('Staff'),
            checkboxes: false,
            values: this.GetFilterdStaffList(params.data.staffId),
            value: params.data.staffId,
            gridApi: this.gridApi,
            searchable: true,
            params
          }
        },
        cellStyle: {
          fontWeight: '400',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }
      },
      {
        field: 'daysPerWeek',
        headerName: this.translate.instant('Days per Week'),
        minWidth: 178,
        flex: 1.5,
        editable: true,
        menuTabs: ['filterMenuTab'],
        valueGetter: params => params.data.daysPerWeek,
        valueParser: (params) => {
          const value = parseFloat(params.newValue);
          if (!isNaN(value) && value > 0) {
            return value;
          } else {
            return params.oldValue;
          }
        },
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            daysPerWeek: params.data.daysPerWeek
          };
        },
        cellEditorFramework: AgGridCustomNumericFieldComponent,
        cellEditorParams: {
          placeholder: this.translate.instant('Days per Week'),
        }
      },
      {
        field: 'actions',
        headerName: this.translate.instant('Actions'),
        minWidth: 128,
        filter: false,
        sortable: false,
        menuTabs: [],
        flex: 0.8,
        headerClass: 'text-center',
        cellStyle: { display: 'flex', justifyContent: 'center' },
        cellRenderer: TtsGridActionsComponent,
        cellRendererParams: (params: ICellRendererParams) => {
          return {
            gridId: 'tts-ag-grid',
            type: 'parttimestaff',
            onAcceptNewRow,
            onCancelAddingNewRow,
            onEditRow,
            onDeleteRow,
            onAcceptRow,
            onCancelEditRow,
            onExcludeRow,
            onIncludeRow
          }
        },
        editable: false
      }
    ]
  })
}

