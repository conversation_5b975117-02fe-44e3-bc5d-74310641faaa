@import "apps/bromcom-timetables/src/assets/styles/variables";

.modal-container {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 16px;
  height: 37vh;
  border-radius: 4px;
  border: 1px solid $color-blue-200;

  ul {
    padding-left: 0;
    list-style: none;
  }

  .header-menu {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid $color-blue-grey-900;
  }

  .line {
    display: flex;
    align-items: center;
    margin-bottom: 6px;

    .circle {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      width: 20px;
      height: 20px;
      border-radius: 100px;
      margin-right: 8px;
      border: 1px solid $color-black-0;
    }
  }
}

.list {
  width: 348px;
}

.expand-icons {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
}

.tree-invisible {
  display: none;
}

.tree ul,
.tree li {
  margin-top: 0;
  margin-bottom: 0;
  list-style-type: none;
}

.tree div[role=group] > .mat-tree-node {
  padding-left: 40px;
}

.auto-assign-modal {
  z-index: 10;

  .modal-body {
    padding: 24px;
  }
}

.footer {
  display: flex;
  justify-content: space-between;
}

.line {
  display: flex;

  div {
    margin-right: 16px;
  }

  .input {
    display: flex;
    align-items: center;
    width: 256px;
    height: 32px;
    border: 1px solid $color-blue-grey-300;
    border-radius: 4px;

    &.error {
      color: $color-red-tertiary-600;
      border: 1px solid $color-red-tertiary-600 !important;
      background-color: $color-red-tertiary-50;
    }
  }
}

.hidden {
  display: none !important;
}

.icon {
  cursor: pointer;
}

::ng-deep .mdc-checkbox__ripple, ::ng-deep .mat-mdc-checkbox-ripple {
  display: none;
}

::ng-deep .mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control:enabled:checked ~ .mdc-checkbox__background, .mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate ~ .mdc-checkbox__background, .mat-mdc-checkbox .mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled ~ .mdc-checkbox__background {
  background-color: $color-primary-blue-700 !important;
  border-color: $color-primary-blue-700 !important;
}
