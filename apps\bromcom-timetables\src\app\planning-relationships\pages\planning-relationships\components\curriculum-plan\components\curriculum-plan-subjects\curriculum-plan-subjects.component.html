<div class="subject-container">
  <bromcom-input-field [formControl]="searchControl"
                       [icon]="'fal fa-search'"
                       [placeholder]="'Search' | translate"
  ></bromcom-input-field>

  <bcm-button class="action-button" kind="ghost" icon="far fa-pen" (click)="onEditSubjects()">
    {{'Edit Subjects' | translate }}
  </bcm-button>

  <div class="subject-header">
    <div [matMenuTriggerFor]="menu" class="drag-icon">
      <i class="far fa-filter"></i>
      <div *ngIf="isFilterApplied" class="dot"></div>
    </div>
    <div class="code" (click)="sort('code')">
      {{'Code' | translate }}
      <i bromcomSortIcon [sortIcon]="sorting['code']" class="fa"></i>
    </div>
    <div class="name" (click)="sort('name')">
      {{'Name' | translate }}
      <i bromcomSortIcon [sortIcon]="sorting['name']" class="fa"></i>
    </div>
    <div class="short-name" (click)="sort('shortName')">
      {{'Short Name' | translate }}
      <i bromcomSortIcon [sortIcon]="sorting['shortName']" class="fa"></i>
    </div>
    <div class="department" (click)="sort('departmentName')">
      {{'Department' | translate }}
      <i bromcomSortIcon [sortIcon]="sorting['departmentName']" class="fa"></i>
    </div>
  </div>

  <div class="subject-cards-container"
       cdkDropList
       [cdkDropListData]="visibleData"
       [cdkDropListConnectedTo]="droppableBlockIds">
    <bcm-empty *ngIf="!visibleData.length && !this.searchControl.value && !this.filterControl.value" class="no-data"
               icon="fad fa-folder-open">{{'No data available! Subject not added.' | translate }}</bcm-empty>

    <bcm-empty *ngIf="!visibleData.length && (this.searchControl.value || this.filterControl.value)" class="no-data"
               icon="fad fa-folder-open">{{'Sorry no results found. Please amend your search criteria.' | translate }}</bcm-empty>

    <div *ngFor="let subject of visibleData" #viewport>
      <bromcom-subject-card [subject]="subject"></bromcom-subject-card>
    </div>
  </div>
</div>

<mat-menu #menu="matMenu" class="filter-popup">
  <ng-template matMenuContent>
    <bromcom-input-field [formControl]="filterControl"
                         [icon]="'fal fa-search'"
                         [placeholder]="'Search' | translate"
                         (click)="$event.stopPropagation()"
    ></bromcom-input-field>

    <div (click)="$event.stopPropagation()"
         class="side-panel-filter-box" [formGroup]="filterGroup">
      <bromcom-checkbox [text]="'Code' | translate"
                        [formControlName]="'code'"></bromcom-checkbox>
      <bromcom-checkbox [text]="'Name' | translate"
                        [formControlName]="'name'"></bromcom-checkbox>
      <bromcom-checkbox [text]="'Short Name' | translate"
                        [formControlName]="'shortName'"></bromcom-checkbox>
      <bromcom-checkbox [text]="'Department' | translate"
                        [formControlName]="'departmentName'"></bromcom-checkbox>
    </div>
  </ng-template>
</mat-menu>
