import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { CdkDragDrop, CdkDragEnter, CdkDragExit } from "@angular/cdk/drag-drop";
import { ISubject } from "../../../../../../../_shared/models/ISubject";
import { AbstractControl, FormControl, ValidationErrors, Validators } from "@angular/forms";
import { IListOfBlocksResponse } from "../../../../../../../_shared/models/IListOfBlocksResponse";
import { BLOCK_TYPE } from "../../../../../../../_shared/enums/BlockType";
import { IAddToGroupEvent } from "../../../../../../../_shared/models/IAddToGroupEvent";
import { v4 as uuidv4 } from 'uuid';
import { IUpdateBlockPeriodCountEvent } from "../../../../../../../_shared/models/IUpdateBlockPeriodCountEvent";
import { SubjectInformationService } from "../../../../../../services/subject-information.service";
import { TranslateService } from "@ngx-translate/core";
import { AsyncBlockNameValidator } from "../../../../validators/async-block-name-validator";
import { IUpdateSubjectToYearGroupEvent } from "../../../../../../../_shared/models/IUpdateSubjectToYearGroupEvent";
import { Subject } from "rxjs";
import { DRAG_AND_DROP_GROUP_TYPE } from "../../../../../../../_shared/enums/DragAndDropGroupType";
import {
  IRemoveSubjectToYearGroupFromGroupEvent
} from "../../../../../../../_shared/models/IRemoveSubjectToYearGroupFromGroupEvent";
import { SnackbarService } from "@bromcom/ui";
import { GeneralModalComponent } from '../../../../../../../_shared/components/general-modal/general-modal.component';

@Component({
  selector: 'bromcom-drag-and-drop-group',
  templateUrl: './drag-and-drop-group.component.html',
  styleUrls: ['./drag-and-drop-group.component.scss']
})
export class DragAndDropGroupComponent {
  @ViewChild('editPeriodAndNoOfClassModalComponent') editPeriodAndNoOfClassModalComponent!: ElementRef;
  @ViewChild('updatePeriodCountWarningModal') updatePeriodCountWarningModal!: GeneralModalComponent;

  @Input() set title(value: string | undefined) {
    this._title = value;
    this.titleForm.patchValue(value || '');
  }

  @Input() id!: string;
  @Input() blockId!: number;
  @Input() type!: DRAG_AND_DROP_GROUP_TYPE;
  @Input() selectedSubjects!: IListOfBlocksResponse[][];
  @Input() linearGroups!: IListOfBlocksResponse[];
  @Input() optionGroups!: IListOfBlocksResponse[];
  @Input() dropListConnectedTo!: string[];
  @Input() index!: number;
  @Input() subjects!: ISubject[];
  @Input() data!: IListOfBlocksResponse;
  @Input() selectedYearGroupId!: number;
  @Input() timetableId!: number;
  @Input() updateClassAndPeriodCount$!: Subject<void>;
  @Output() removeItemEvent = new EventEmitter<IRemoveSubjectToYearGroupFromGroupEvent>();
  @Output() editNoClass = new EventEmitter();
  @Output() updateTitleEvent: EventEmitter<string> = new EventEmitter();
  @Output() deleteBlockEvent: EventEmitter<IListOfBlocksResponse> = new EventEmitter();
  @Output() addSubjectToYearGroupToBlockEvent = new EventEmitter<IAddToGroupEvent>();
  @Output() updateBlockClassCountEvent = new EventEmitter<IUpdateBlockPeriodCountEvent>();
  @Output() updateBlockPeriodCountEvent = new EventEmitter<IUpdateBlockPeriodCountEvent>();
  @Output() updateSubjectToYearGroupEvent = new EventEmitter<IUpdateSubjectToYearGroupEvent>();
  @Output() updateSubjectInfoEvent = new EventEmitter<void>();
  @Output() removeNewLinearGroupEvent = new EventEmitter<IListOfBlocksResponse>();
  @Output() removeNewOptionGroupEvent = new EventEmitter<IListOfBlocksResponse>();

  noOfCLassOrPeriodCount = 0;
  noOfCLassOrPeriodCountForm = new FormControl<number | undefined>(undefined);
  modalId = uuidv4();
  isModalOpen = false;

  _title: string | undefined = '';
  editTitle = false;
  titleForm = new FormControl('', Validators.required);
  DRAG_AND_DROP_GROUP_TYPE_ENUM = DRAG_AND_DROP_GROUP_TYPE;
  isCdkDragging = false;
  isExpanded = true;
  warningHeader!: string;
  warningDescription!: string;
  payloadData!: {value: number, destinationBlock: IListOfBlocksResponse};

  constructor(private subjectInformationService: SubjectInformationService,
              private snackbarService: SnackbarService,
              private translateService: TranslateService) {
  }

  drop(event: CdkDragDrop<any[]>) {
    if (event.previousContainer !== event.container && event.isPointerOverContainer) {
      let isExists = false;
      const blockAndId = event.container.id.split(':');
      const previousContainerData = event.previousContainer.data[event.previousIndex];
      const targetBlockType = blockAndId[0];
      const targetBlockId = +blockAndId[1];
      const subject = previousContainerData?.blockTypeId ? previousContainerData.subjectToYearGroups[0] : previousContainerData;
      const filteredGroups = [...this.linearGroups, ...this.optionGroups]
        .filter(group => group.id === targetBlockId);
      filteredGroups.forEach(group => {
        const tmp = group.subjectToYearGroups.map(item => item.subjectId);
        if (tmp.includes(subject.subjectId)) {
          isExists = true;
        }
      });

      if (isExists) {
        this.snackbarService.warning(this.translateService.instant('This subject is already added to the selected block.'));
        return;
      }

      let targetGroupType;
      if (targetBlockType === DRAG_AND_DROP_GROUP_TYPE.Option) {
        targetGroupType = this.optionGroups.find(group => group.id === targetBlockId)?.blockTypeId;
      }

      const payload: IAddToGroupEvent = {
        subject,
        previousContainerData,
        targetBlockId,
        type: targetGroupType && targetGroupType === BLOCK_TYPE.Complex ? BLOCK_TYPE.Complex : (targetBlockType === DRAG_AND_DROP_GROUP_TYPE.Linear ? BLOCK_TYPE.Linear : BLOCK_TYPE.Options),
        currentIndex: event.currentIndex
      };
      this.addSubjectToYearGroupToBlockEvent.emit(payload);
    }
    this.isCdkDragging = false;
  }

  cdkDragEntered(evt: CdkDragEnter) {
    this.isCdkDragging = true;
  }

  cdkDropExited(evt: CdkDragExit) {
    this.isCdkDragging = false;
  }

  removeItem(item: IRemoveSubjectToYearGroupFromGroupEvent): void {
    this.removeItemEvent.emit(item);
  }

  clickMenuItem(action: string): void {
    switch (action) {
      case 'rename': {
        this.addValidatorsToForm();
        if (this._title) {
          this.titleForm.setValue(this._title);
        }
        this.editTitle = true;
        break;
      }
      case 'delete': {
        this.deleteBlockEvent.emit(this.data);
        break;
      }
      case 'update': {
        this.openEditModal();
        break;
      }
    }
  }

  cancelEditTitle(): void {
    this.editTitle = false;
    this.titleForm.clearValidators();
    this.titleForm.clearAsyncValidators();
    this.titleForm.addValidators([Validators.required])
  }

  saveEditTitle(): void {
    this.editTitle = false;
    this.updateTitleEvent.emit(this.titleForm.value || '');
    this.titleForm.clearValidators();
    this.titleForm.clearAsyncValidators();
    this.titleForm.addValidators([Validators.required])
  }

  openEditModal(): void {
    this.isModalOpen = true;
    this.subjectInformationService.getBlock(this.data.id!).subscribe(block => {
      this.data.previewDefaultPeriodCount = block.previewDefaultPeriodCount;
      if (this.data.blockTypeId === BLOCK_TYPE.Options || this.data.blockTypeId === BLOCK_TYPE.Complex) {
        this.noOfCLassOrPeriodCountForm.patchValue(this.data.previewDefaultPeriodCount);
      }
    });
    setTimeout(() => {
      if (this.data.blockTypeId === BLOCK_TYPE.Linear) {
        this.noOfCLassOrPeriodCountForm.patchValue(this.data.classCount);
      }
      this.editPeriodAndNoOfClassModalComponent.nativeElement.show();
    },1000)
  }

  saveNoOfClassOrPeriodCount(): void {
    const payload = {
      value: this.getValue(this.noOfCLassOrPeriodCountForm.value),
      destinationBlock: this.data
    }
    if (this.data.blockTypeId === BLOCK_TYPE.Linear) {
      this.updateBlockClassCountEvent.emit(payload);
      this.editPeriodAndNoOfClassModalComponent.nativeElement.hide();
      this.isModalOpen = false;
    } else if (this.data.blockTypeId === BLOCK_TYPE.Options || this.data.blockTypeId === BLOCK_TYPE.Complex) {
      this.editPeriodAndNoOfClassModalComponent.nativeElement.hide();
      this.payloadData = payload;
      this.warningHeader = this.translateService.instant("Please be aware that all subject periods within ") + this.data.blockName + this.translateService.instant(" will be updated accordingly");
      this.updatePeriodCountWarningModal.show();
    }
    this.noOfCLassOrPeriodCount = this.getValue(this.noOfCLassOrPeriodCountForm.value);
  }

  updatePeriodCount() {
    this.updatePeriodCountWarningModal.hide();
    this.updateBlockPeriodCountEvent.emit(this.payloadData);
    if (this.editPeriodAndNoOfClassModalComponent) {
      this.editPeriodAndNoOfClassModalComponent.nativeElement.hide();
    }
    this.isModalOpen = false;
  }

  showEditPeriodCount() {
    this.isModalOpen = true;
    setTimeout(() => {
      this.editPeriodAndNoOfClassModalComponent.nativeElement.show();
    }, 100);
  }

  updateSubjectToYearGroup(event: IUpdateSubjectToYearGroupEvent): void {
    this.updateSubjectToYearGroupEvent.emit(event);
  }

  updateSubjectInfo(): void {
    this.updateSubjectInfoEvent.emit();
  }

  onRemoveEmptySubjectGroup(data: IListOfBlocksResponse, type: string) {
    if (DRAG_AND_DROP_GROUP_TYPE.Linear === type) {
      this.removeNewLinearGroupEvent.emit(data);
    } else if (DRAG_AND_DROP_GROUP_TYPE.Option === type) {
      this.removeNewOptionGroupEvent.emit(data);
    }
  }

  onModalClose() {
    this.isModalOpen = false;
  }

  toggleExpand() {
    this.isExpanded = !this.isExpanded;
  }

  private uniqueBlockNameValidator = () => {
    return (control: AbstractControl): ValidationErrors | null => {
      const currentBlockNames = [
        ...this.linearGroups.map(group => group.blockName),
        ...this.optionGroups.map(group => group.blockName)
      ];
      return currentBlockNames.includes(control.value) && this._title !== control.value ? { notUnique: true } : null;
    };
  }

  private addValidatorsToForm(): void {
    this.titleForm.addAsyncValidators([
      AsyncBlockNameValidator.createValidator(this.subjectInformationService, this.translateService,
        'There is a Block name with the same name. This has to be unique.', this.selectedYearGroupId, this.timetableId, this.data.id)])
    this.titleForm.addValidators(this.uniqueBlockNameValidator());
    this.titleForm.updateValueAndValidity();
  }

  private getValue(value: null | undefined | number): number {
    return !value || value <= 0 ? 1 : value;
  }

}
