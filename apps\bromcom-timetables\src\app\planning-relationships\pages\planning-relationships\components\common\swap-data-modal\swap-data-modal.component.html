<ng-container *ngIf="isOpen">
  <bcm-modal size="xlarge" class="swap-data-modal" (bcm-modal-before-close)="onClose()" #swapDataModal>
    <bcm-modal-header>{{ swapModalHeader }}</bcm-modal-header>

    <div class="modal-body">
      <ng-container *ngIf="!showFeedbackView; else feedbackView">
        <div class="action-bar">
          <div class="left-side">
            <div class="mr-15">
              <bcm-button class="action-button dropdown" kind="ghost" [matMenuTriggerFor]="menu">
                <bcm-icon class="icon" icon="far fa-filter"></bcm-icon>
                <span>{{ 'Filters' | translate }}</span>
                <span *ngIf="isSubjectFilterApplied || isDepartmentFilterApplied || isStaffFilterApplied"
                      class="dot"></span>
                <bcm-icon class="icon" icon="fas fa-angle-down"></bcm-icon>
              </bcm-button>

              <mat-menu #menu="matMenu" class="block-action-modal">
                <ng-template matMenuContent>
                  <div class="block-vertical" (click)="$event.stopPropagation()">
                    <button [matMenuTriggerFor]="subjectsFilterMenu" mat-menu-item class="button mat-button"
                            (mouseenter)="openSubjectFilter()">
                      <div class="mat-button">
                        <div>
                          {{ 'Subjects' | translate }}
                        </div>
                        <div *ngIf="isSubjectFilterApplied" class="dot"></div>
                      </div>
                    </button>

                    <button [matMenuTriggerFor]="departmentsFilterMenu" [disabled]="!departmentOptions.length"
                            mat-menu-item class="button"
                            (mouseenter)="openDepartmentFilter()">
                      <div class="mat-button">
                        <div>{{ 'Departments' | translate }}</div>
                        <div *ngIf="isDepartmentFilterApplied" class="dot"></div>
                      </div>
                    </button>

                    <button [matMenuTriggerFor]="staffTypesFilterMenu" [disabled]="!isSwapStaffView" mat-menu-item
                            (mouseenter)="openStaffTypeFilter()">
                      <div class="mat-button">
                        <div>
                          {{ 'Staff Type' | translate }}
                        </div>
                        <div *ngIf="isStaffFilterApplied" class="dot"></div>
                      </div>
                    </button>
                  </div>
                </ng-template>
              </mat-menu>

              <mat-menu #subjectsFilterMenu (mouseenter)="onSubjectFilterBlur()" class="block-action-modal">
                <div (click)="$event.stopPropagation()">
                  <bromcom-filter [dataItems]="{items: subjectOptions, startState: true}" [isOpen]="isSubjectFilterOpen"
                                  [resetFilter]="resetSubjectFilter"
                                  (filterData)="onApplyFilter(filterTypes.Subjects, $event)"
                                  (closeFilter)="onCancelFilter(filterTypes.Subjects, $event)" [appearance]="'switch'"
                                  [enableApplyFilterWithDefaultSelection]="true"  [selectAllOnClearedApply]="false"></bromcom-filter>
                </div>
              </mat-menu>
              <mat-menu #departmentsFilterMenu class="block-action-modal"
                        (mouseenter)="onDepartmentFilterBlur()">
                <div (click)="$event.stopPropagation()">
                  <bromcom-filter [dataItems]="{items: departmentOptions, startState: true}"
                                  [isOpen]="isDepartmentFilterOpen" [resetFilter]="resetDepartmentFilter"
                                  (filterData)="onApplyFilter(filterTypes.Departments, $event)"
                                  (closeFilter)="onCancelFilter(filterTypes.Departments, $event)"
                                  [appearance]="'switch'"
                                  [enableApplyFilterWithDefaultSelection]="true"  [selectAllOnClearedApply]="false"></bromcom-filter>
                </div>
              </mat-menu>
              <mat-menu #staffTypesFilterMenu class="block-action-modal" (mouseenter)="onStaffTypeFilterBlur()">
                <div (click)="$event.stopPropagation()">
                  <bromcom-filter [dataItems]="{items: staffTypeOptions, startState: false}"
                                  [selection]="defaultStaffType"
                                  [isOpen]="isStaffTypeFilterOpen" [resetFilter]="resetStaffTypeFilter"
                                  (filterData)="onApplyFilter(filterTypes.StaffTypes, $event)"
                                  (closeFilter)="onCancelFilter(filterTypes.StaffTypes, $event)" [appearance]="'switch'"
                                  [enableApplyFilterWithDefaultSelection]="true"  [selectAllOnClearedApply]="false"></bromcom-filter>
                </div>
              </mat-menu>
            </div>

            <bromcom-input-field class="search-field" [formControl]="searchControl" [icon]="'fal fa-search'"
                                 [placeholder]="'Search' | translate "></bromcom-input-field>
          </div>
        </div>

        <div class="table-container">
          <ag-grid-angular style="width: 100%; height:100%;"
                           class="ag-theme-alpine"
                           domLayout="normal"
                           [rowData]="rowData"
                           [tooltipInteraction]="true"
                           [gridOptions]="gridOptions"
                           (gridReady)="onGridReady($event)">
          </ag-grid-angular>
        </div>
      </ng-container>
      <ng-template #feedbackView>
        <div class="swap-feedback-holder">
          <div class="info-holder" [ngClass]="{'swap-not-allowed-warning': !isSwapAllowed}">
            <bcm-icon class="icon info-icon" icon="far fa-info-circle"></bcm-icon>
            <label class="swap-feadback-header">{{swapMessage}}</label>
          </div>
          <div *ngIf="showWarning && isSwapAllowed" class="warning-holder">
            <bcm-icon icon="far fa-exclamation-triangle fa-5"></bcm-icon>
            <span class="warning-text">
              {{ isSwapStaffView ? staffWarning : originalRoomWarning }}
            </span>
          </div>
          <div *ngIf="feedbackData?.length" class="table-holder">
            <table>
              <thead>
              <tr>
                <th [style.width.px]="233" style="text-align: left;">Type</th>
                <th style="text-align: left;">Description</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let feedback of feedbackData">
                <td>{{ feedbackType }}</td>
                <td>{{ feedback }}</td>
              </tr>
              </tbody>
            </table>
          </div>
          <div *ngIf="isSwapStaffView" class="swap-selection-holder">
            <bromcom-checkbox [text]="'Swap Room Allocation as well' | translate" [formControl]="swapSelectionControl">
            </bromcom-checkbox>
          </div>
        </div>
      </ng-template>
    </div>

    <bcm-modal-footer>
      <ng-container *ngIf="!showFeedbackView; else feedbackViewFooter">
        <bcm-button kind="ghost" data-dismiss (click)="onCancel()">{{'Cancel' | translate }}</bcm-button>
        <bcm-button *ngIf="isSwapStaffView" [disabled]="!selectedStaffData"
                    (click)="getSwapStaffFeedback()">{{'Swap Staff' | translate }}</bcm-button>
        <bcm-button *ngIf="!isSwapStaffView" [disabled]="!selectedRoomData"
                    (click)="getSwapRoomFeedback()">{{'Swap Room' | translate }}</bcm-button>
      </ng-container>
      <ng-template #feedbackViewFooter>
        <div class="swap-feedback-butons">
          <div>
            <bcm-button kind="ghost" icon="far fa-arrow-left"
                        (click)="onBackClick()">{{'Back' | translate }}</bcm-button>
          </div>
          <div class="feedback-action-buttons">
            <bcm-button kind="ghost" data-dismiss (click)="onCancel()">{{'Cancel' | translate }}</bcm-button>
            <bcm-button [disabled]="!isSwapAllowed" icon="far fa-save"
                        (click)="isSwapStaffView? swapStaff(): swapRoom()">{{'Save' | translate }}</bcm-button>
          </div>
        </div>
      </ng-template>
    </bcm-modal-footer>
  </bcm-modal>
</ng-container>

