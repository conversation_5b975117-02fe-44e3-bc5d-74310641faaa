<div class="blocks-container">
  <div class="blocks-header">
    <div>
      <bcm-button class="action-button filter"
                  kind="ghost"
                  icon="far fa-filter"
                  [matMenuTriggerFor]="menu"
                  (click)="showBlocksFilterModal()">
        <span *ngIf="isBandFilterApplied" class="dot"></span>&nbsp;
      </bcm-button>

      <bcm-button class="action-button" kind="ghost" (click)="openViewDetails()">
        {{'View Details' | translate }}
      </bcm-button>

      <bromcom-view-details-modal #viewDetailsModal [timetableId]="timetableId"
                                  [projectId]="projectId"></bromcom-view-details-modal>
    </div>
  </div>

  <div class="blocks-body" id="blocksBody">
    <bcm-empty *ngIf="!blocks?.length" class="no-data"
               icon="fad fa-folder-open">{{'No data available! Block not added.' | translate }}</bcm-empty>

    <bromcom-curriculum-plan-block class="block"
                                   *ngFor="let block of blocks"
                                   [block]="block"
                                   [bands]="bands"
                                   [allSubject]="subjects"
                                   [selectedYearGroup]="selectedYearGroupObject"
                                   (selectItemEvent)="selectActiveBlock($event)"
                                   (openEditClassCodesModalEvent)="openEditClassCodesModal($event)">
    </bromcom-curriculum-plan-block>
  </div>

  <mat-menu class="blocks-filter-popup"
            #menu="matMenu">
    <ng-template matMenuContent>
      <div (click)="$event.stopPropagation()">
        <bromcom-filter [dataItems]="{items: bands, startState: true}"
                        [isOpen]="blocksFilterModalOpened"
                        [selectAllOnClearedApply]="false"
                        (filterData)="filterIds($event)"
                        [appearance]="'switch'"
                        (closeFilter)="closeFilterMenu()"
                        [selectedItemIds]="selectedBandIds"></bromcom-filter>
      </div>
    </ng-template>
  </mat-menu>
</div>
