<ng-container *ngIf="isOpen">
  <bcm-modal size="large" class="display-timetable-modal"
             (bcm-modal-before-close)="onClose()"
             #displayTimetableModal>
    <bcm-modal-header>{{isTeacherTimetable ? ('Teacher Timetable' | translate) : ('Room Timetable' | translate) }}
      - {{ isTeacherTimetable ? selectedTeacher : selectedRoom }}</bcm-modal-header>

    <div class="modal-body">
      <div class="action-bar">
        <div class="left-side">
          <div class="data-selector">
            <label>{{isTeacherTimetable ? ('Teacher Selector' | translate) : ('Room Selector' | translate) }}</label>
            <bromcom-list
              listType="select"
              [formControl]="selectedTeacherOrRoomControl"
              [data]="dropDownData"
              [clearable]="false">
            </bromcom-list>
          </div>

        </div>
      </div>
      <div #displayTimetable class="display-timetable" (contextmenu)="eventHandler($event)">
        <div class="timetable-holder" *ngFor="let week of weeks">
          <label class="week-label">Week {{week.weekDisplayName}}</label>
          <table class="timetable">
            <thead>
            <tr>
              <th class="settings-cog" (click)="openContextMenuForWeek($event, week, menuEl)">
                <i class="fa-regular fa-gear"></i>
                <bromcom-context-menu [trigger]="{}" [data]="menuList" [isRootNode]="true"
                                      #menuEl></bromcom-context-menu>
              </th>

              <th *ngFor="let period of getPeriodHeaders(week); let index = index; trackBy:identify"
                  [ngClass]="{ 'isBreak': period.isBreak }"
                  class="period-header"
                  (click)="openContextMenuForPeriod($event, period, week, menuEl)">
                  <span>{{ period.name }}</span>
                  <div class="cus-tooltip" *ngIf="period.isLunch">
                    <span class="cus-tooltip-icon">L</span>
                    <span class="cus-tooltiptext">Lunch Period</span>
                  </div>
                <bromcom-context-menu [trigger]="{}" [data]="menuList" [isRootNode]="true"
                                      #menuEl></bromcom-context-menu>
              </th>
            </tr>
            </thead>

            <tbody>
            <tr *ngFor="let day of getDays(week); let index = index; trackBy:identify">
              <td class="day-header" (click)="openContextMenuForDay($event, day, menuEl)">
                {{ day.name }}
                <bromcom-context-menu [trigger]="{}" [data]="menuList" [isRootNode]="true"
                                      #menuEl></bromcom-context-menu>
              </td>

              <td class="period-cell" *ngFor="let period of getPeriods(day); let index = index; trackBy:identify"
                  [ngClass]="{ 'isBreak': period.isBreak, 'isGreyedOut': period.isGreyedOut }">
                <div class="flex-column period-holder"
                     [ngClass]="{ 'isNonContactCodeHolder': isNonContactCodeHolder(period), 'isConflict': getSessions(period)!.length > 1}"
                     [style.height]="getMaxSessionHeight(day)">
                  <ng-container *ngFor="let session of getSessions(period); let index = index; trackBy:identify">
                    <div class="sessionClass" (click)="openContextMenuForSession($event, session, period, menuEl)">
                      <span class="session"
                            [ngStyle]="{ 'background-color': getColor(session), 'color': textColor }">
                          <div class="row">
                              <div class="session-name">{{session.sessionName}}</div>
                          </div>
                          <div class="row">
                              <div *ngIf="isTeacherTimetable" class="session-location"><i
                                class="fal fa-map-marker-alt"></i>
                                <span [matTooltip]="session.sessionRoomName || '--'"
                                [matTooltipDisabled]="session.sessionRoomName && session.sessionRoomName.length! < 5"
                                [matTooltipPosition]="'above'">{{session.sessionRoomName || '--'}}</span></div>
                              <div *ngIf="!isTeacherTimetable" class="session-staff"><i
                                class="fal fa-users-class"></i>
                                <span [matTooltip]="session.sessionStaffName || '--'"
                                [matTooltipDisabled]="session.sessionStaffName && session.sessionStaffName.length! < 5"
                                [matTooltipPosition]="'above'">{{session.sessionStaffName || '--'}}</span></div>
                              <div class="session-subject" [matTooltip]="getSubject(session) ?? ''"
                              [matTooltipDisabled]="getSubject(session) && getSubject(session)?.length! < 5"
                              [matTooltipPosition]="'above'">{{getSubject(session)}}</div>
                          </div>
                      </span>
                      <bromcom-context-menu [trigger]="{}" [data]="menuList" [isRootNode]="true"
                                            #menuEl></bromcom-context-menu>
                    </div>
                  </ng-container>

                  <div class="non-contact-class"
                       *ngFor="let nonContactCode of getNonContactCodes(period); let index = index; trackBy:identify"
                       (mousedown)="openContextMenuForEmptyArea($event, period, menuEl, nonContactCode)">
                    <span class="non-contact-holder" [ngClass]="{ 'showRoomDetails': isTeacherTimetable}" [matTooltip]="nonContactCode.codeName ?? ''"
                      [matTooltipDisabled]="nonContactCode.codeName?.length! < 5"
                      [matTooltipPosition]="'above'">
                      {{nonContactCode.codeName}}
                    </span>
                    <div *ngIf="isTeacherTimetable" class="row room-detail-holder">
                      <div class="nc-location"><i
                        class="fal fa-map-marker-alt"></i>
                        <span [matTooltip]="nonContactCode.roomId ? getRoomName(nonContactCode.roomId) : '--'"
                        [matTooltipDisabled]="getRoomName(nonContactCode.roomId!).length! < 5"
                        [matTooltipPosition]="'above'">{{nonContactCode.roomId ? getRoomName(nonContactCode.roomId) : '--'}}</span></div>
                      </div>
                    <bromcom-context-menu [trigger]="{}" [data]="menuList" [isRootNode]="true"
                                            #menuEl></bromcom-context-menu>
                  </div>

                  <div *ngIf="period.isEmpty" class="empty-cell"
                       [ngClass]="{ 'isSelected': selectedIds.includes(period.id) }"
                       (mousedown)="openContextMenuForEmptyArea($event, period, menuEl)">
                    <bromcom-context-menu [trigger]="{}" [data]="menuList" [isRootNode]="true"
                                          #menuEl></bromcom-context-menu>
                  </div>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </bcm-modal>
</ng-container>
