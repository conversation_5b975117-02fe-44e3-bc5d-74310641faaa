@import "apps/bromcom-timetables/src/assets/styles/variables";

.check-availability-modal {
  z-index: 10;

  .modal-body { 
    padding: 24px;
    max-height: 76vh;
    overflow: hidden;
  }
}

.tab-content {
  padding: 16px;
}

.check-availability-container {
  width: 100%;
  overflow: auto;
}

.table-container {
  overflow-x: auto;
  max-height: 62vh;
}

.availability-table {
  width: 100%;
  border-collapse: collapse;

}

.availability-table th,
.availability-table td {
  border: 1px solid #ccc;
  text-align: center;
  box-sizing: border-box;
  width: 48px;
  min-width: 48px;
  max-width: 48px;
  height: 48px;
  min-height: 48px;
  max-height: 48px;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.top-left-cell {
  background-color: $color-blue-grey-100;
  width: 200px !important;
  min-width: 200px !important;
  max-width: 200px !important;
}

.day-header {
  background-color: $color-blue-grey-100;
  font-weight: bold;
  text-align: center;
}

.period-header {
  background-color: $color-blue-grey-100;
  font-weight: normal;
  width: 48px;
  min-width: 48px;
  max-width: 48px;
  height: 48px;
  line-height: 48px;
  text-align: center;
  border: 1px solid $color-blue-grey-200;
  padding: 0px !important;
}

.staff-holder {
  background-color: $color-blue-grey-100;
  text-align: left;
  font-weight: bold;
  width: 200px;
  min-width: 200px;
  max-width: 200px;
}

.period-cell {
  position: relative;
  vertical-align: top;
  width: 48px;
  min-width: 48px;
  max-width: 48px;
  height: 48px;
  text-align: center;
  vertical-align: middle;
  border: 1px solid $color-blue-grey-200;
  padding: 0px !important;
}

.period-holder {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.period-cell .period-holder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.session-class {
  width: 100%;
  height: 100%;
}

.session {
  display: flex;
  padding: 4px 6px;
  font-size: 14px;
  line-height: 1.2;
  width: 100%;
  height: 48px;
  justify-content: center;
  align-items: center;

  .session-name {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.non-contact-class {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 4px 10px 2px 10px;
  justify-content: center;
  align-items: center;
  background: $color-grey-600;
  position: relative;
  
  .indicator {
    position: absolute;
    top: 0px;
    right: 0px;
    font-size: 12px;
    font-weight: bold;
    color: $color-white-0;
    padding: 0px 6px 2px 6px;
    z-index: 1;
  }

  .non-contact-holder {
    height: 100%;
    width: 100%;
    display: flex;
    color: $color-white-0;
    font-size: 14px;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    .non-contact-name {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.day-header:last-child {
  border-right: 0;
}

.day-separator {
    border-right: 2px solid $color-blue-grey-800 !important;
}

.circle {
  width: 15px;
  height: 15px;
  line-height: 15px;
  border-radius: 50%;
  font-size: 10px;
  color: #fff;
  text-align: center;
  background: #374151;
  float: right;
  margin: 3px;
}