﻿import { Component } from '@angular/core';
import { ICellRendererParams } from 'ag-grid-community';

export interface IAutoScheduleReasons extends ICellRendererParams {
  onOpenReasons: (params: ICellRendererParams) => {},
}

@Component({
  selector: 'bromcom-auto-schedule-reasons',
  templateUrl: './auto-schedule-reasons.component.html',
  styleUrls: ['./auto-schedule-reasons.component.scss'],
})
export class AutoScheduleReasonsComponent {
  protected params!: IAutoScheduleReasons;

  agInit(params: IAutoScheduleReasons): void {
    this.params = params;
  }

  periodCountMap: { [key: string]: number } = {};

  ngOnInit() {
    this.params.data.issueDetails.forEach((item: { period: string; }) => {
      if (this.periodCountMap[item.period]) {
        this.periodCountMap[item.period]++;
      } else {
        this.periodCountMap[item.period] = 1;
      }
    });
  }

  onShowReasons(): void {
    this.params.onOpenReasons(this.params);
  }
}
