import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AgGridModule } from 'ag-grid-angular';
import { ReportsSubjectTimetablesPreviewComponent } from './reports-subject-timetables-preview.component';
import { IReportPreview } from '../../../../../../../../../_shared/models/IStaffReportEntity';
import { IStaffToSubjectRelationData } from '../../../../../../../../../_shared/models/IStaffToSubjectRelationData';

describe('ReportsSubjectTimetablesPreviewComponent', () => {
  let component: ReportsSubjectTimetablesPreviewComponent;
  let fixture: ComponentFixture<ReportsSubjectTimetablesPreviewComponent>;

  const mockPreviewData: IReportPreview = {
    staffReportEntities: [
      { staffId: 1, staffName: 'Staff 1', teacherCode: 'S1' },
      { staffId: 2, staffName: 'Staff 2', teacherCode: 'S2' },
      { staffId: 3, staffName: 'Staff 3', teacherCode: 'S3' },
      { staffId: 4, staffName: 'Staff 4', teacherCode: 'S4' }
    ],
    periodNames: {
      1: 'P1',
      2: 'P2',
      3: 'B',
      4: 'P3',
      5: 'P4',
      6: 'L',
      7: 'P5'
    }
  };

  const mockStaffList: IStaffToSubjectRelationData[] = [
    {
      id: 1,
      firstName: 'John',
      lastName: 'Doe',
      code: 'S1',
      name: 'Staff 1',
      prefRoom: null,
      prefRoomId: null,
      genderId: 1,
      isExcluded: false,
      middleName: '',
      originalId: 1,
      projectId: 1,
      titleId: 1,
      totalContactTime: 25,
      typeId: 1,
      departments: [{ subjectId: 1, departmentId: 1, departmentName: 'Department' }]
    },
    {
      id: 2,
      firstName: 'Jane',
      lastName: 'Smith',
      code: 'S2',
      name: 'Staff 2',
      prefRoom: null,
      prefRoomId: null,
      genderId: 2,
      isExcluded: false,
      middleName: '',
      originalId: 2,
      projectId: 1,
      titleId: 1,
      totalContactTime: 25,
      typeId: 1,
      departments: [{ subjectId: 1, departmentId: 1, departmentName: 'Department' }]
    }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ReportsSubjectTimetablesPreviewComponent],
      imports: [AgGridModule]
    }).compileComponents();

    fixture = TestBed.createComponent(ReportsSubjectTimetablesPreviewComponent);
    component = fixture.componentInstance;
    
    // Set required inputs
    component.previewData = mockPreviewData;
    component.staffList = mockStaffList;
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should build grid data correctly', () => {
    component.buildGridData();

    expect(component.columnDefs).toBeDefined();
    expect(component.columnDefs.length).toBeGreaterThan(0);
    expect(component.allDepartmentData).toBeDefined();
    expect(component.allDepartmentData.length).toBeGreaterThan(0);
    expect(component.currentPageData).toBeDefined();
  });

  it('should setup pagination correctly', () => {
    component.buildGridData();

    expect(component.currentPage).toBe(1);
    expect(component.totalPages).toBeGreaterThanOrEqual(1);
    expect(component.currentPageData.length).toBeGreaterThanOrEqual(0);
  });

  it('should navigate pages correctly', () => {
    component.buildGridData();

    // Test going to next page if there are multiple pages
    if (component.totalPages > 1) {
      component.goToNextPage();
      expect(component.currentPage).toBe(2);

      component.goToPreviousPage();
      expect(component.currentPage).toBe(1);

      component.goToLastPage();
      expect(component.currentPage).toBe(component.totalPages);

      component.goToFirstPage();
      expect(component.currentPage).toBe(1);
    }
  });

  it('should generate correct color class for year groups', () => {
    expect(component.getColorClass('7-A-EN-01')).toBe('year-7');
    expect(component.getColorClass('8-B-MA-01')).toBe('year-8');
    expect(component.getColorClass('9-C-SC-01')).toBe('year-9');
    expect(component.getColorClass('10-D-HI-01')).toBe('year-10');
    expect(component.getColorClass('11-E-EN-01')).toBe('year-11');
    expect(component.getColorClass('invalid')).toBe('default-color');
  });

  it('should render timetable cell correctly', () => {
    const mockParams = {
      value: {
        classCode: '9-A-EN-01',
        room: 'E4',
        type: 'class'
      }
    };

    const result = component.timetableCellRenderer(mockParams);
    expect(result).toContain('timetable-cell');
    expect(result).toContain('year-9');
    expect(result).toContain('9-A-EN-01');
    expect(result).toContain('E4');
  });

  it('should render planning cell correctly', () => {
    const mockParams = {
      value: {
        type: 'PLN'
      }
    };

    const result = component.timetableCellRenderer(mockParams);
    expect(result).toContain('planning-cell');
    expect(result).toContain('PLN');
  });

  it('should find department for staff correctly', () => {
    const department = component.findDepartmentForStaff(1);
    expect(department).toBe('Department');
    
    const unknownDepartment = component.findDepartmentForStaff(999);
    expect(unknownDepartment).toBe('Department');
  });
});
