import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseDetailedBlock } from "../../_shared/base-detailed-block";
import { CdkDragDrop } from "@angular/cdk/drag-drop";
import { SnackbarService } from "@bromcom/ui";
import { CurriculumPlanBlocksService } from "../../../../../../services/curriculum-plan-blocks";
import { CurriculumPlanService } from "../../../../../../services/curriculum-plan.service";
import { TranslateService } from "@ngx-translate/core";
import { RelationshipsService } from "../../../../../../services/relationships.service";
import { BehaviorSubject, takeUntil } from 'rxjs';
import { ICurriculumSessions } from '../../../../../../../_shared/models/ICurriculumSessions';
import { PlanningRelationshipsService } from '../../../../../../services/planning-relationships.service';
import { InformationService } from '../../../../../../services/information.service';
import { ICurriculumPlanAddStaffToBlock } from '../../../../../../../_shared/models/ICurriculumPlanAddStaffToBlock';
import { ICurriculumPlanAddRoomToBlock } from '../../../../../../../_shared/models/ICurriculumPlanAddRoomToBlock';

@Component({
  selector: 'bromcom-expanded-detailed-complex-block',
  templateUrl: './expanded-detailed-complex-block.component.html',
  styleUrls: ['./expanded-detailed-complex-block.component.scss']
})
export class ExpandedDetailedComplexBlockComponent extends BaseDetailedBlock implements OnInit {
  selectedSubjectSessionIds$ = new BehaviorSubject<number[]>([]);
  selectedSubjectSessionIds: number[] = [];

  constructor(
    snackbarService: SnackbarService,
    curriculumPlanBlocks: CurriculumPlanBlocksService,
    changeDetectorRef: ChangeDetectorRef,
    curriculumPlanService: CurriculumPlanService,
    curriculumPlanBlocksService: CurriculumPlanBlocksService,
    translate: TranslateService,
    snackbar: SnackbarService,
    relationshipsService: RelationshipsService,
    planningRelationShipsService: PlanningRelationshipsService,
    informationService: InformationService
  ) {
    super(snackbarService, curriculumPlanBlocks, changeDetectorRef, curriculumPlanService, curriculumPlanBlocksService, translate, snackbar, relationshipsService, planningRelationShipsService, informationService);
  }

  override ngOnInit() {
    super.ngOnInit();

    this.selectedSubjectSessionIds$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((selectedSubjectSessionIds) => {
        this.selectedSubjectSessionIds = selectedSubjectSessionIds ?? [];
      })
  }

  dropSubjectHelper(event: CdkDragDrop<any>) {
    if (!event.isPointerOverContainer) {
      return;
    }

    const sessionId = event.container.id.split(':')[2];
    const session = this.selectedBlock?.sessions.find(session => +session.id === +sessionId);

    const isVertical = event.container.id.split(':')[1] === 'v';
    const freeSessionId = this.selectedBlock?.subjectToYearGroups[0].id;
    const formValues: {
      periodCount: number,
      classCount: number,
      singlePeriodCount: number,
      doublePeriodCount: number
    } = this.form.value;

    if (freeSessionId) {
      const sessionIds: number[] = [];
      if (isVertical) {
        formValues.classCount = +formValues.classCount + 1;
      } else {
        formValues.periodCount = +formValues.periodCount + 1;
        formValues.singlePeriodCount = +formValues.singlePeriodCount + 1;
      }

      this.curriculumPlanService
        .updateAnExistingSubjectToYearGroup(freeSessionId, formValues)
        .subscribe(() => {
          if (this.selectedBlock?.id) {
            this.curriculumPlanService
              .getBlock(this.selectedBlock.id)
              .subscribe(res => {
                if (!isVertical) {
                  // Right
                  const sessions = res.sessions.filter(item => item.className.split('-')[0] === session?.className.split('-')[0] && item.periodIndex === res.periodCount);
                  sessionIds.push(...sessions.map(session => session.id));
                } else {
                  // Under
                  const sessions = res.sessions.filter(item => item.className === res.sessions[res.sessions.length - 1].className);
                  sessionIds.push(sessions[session?.periodIndex! - 1].id);
                }
                this.dropSubject(event, sessionIds);
              })
          }
        });
    }
  }

  dropSubjectNewFullRowOrColumn(event: CdkDragDrop<any>, type: string): void {
    if (!event.isPointerOverContainer) {
      return;
    }

    const freeSessionId = this.selectedBlock?.subjectToYearGroups[0].id;
    const formValues: {
      periodCount: number,
      classCount: number,
      singlePeriodCount: number,
      doublePeriodCount: number
    } = this.form.value;

    if (freeSessionId) {
      if (type === 'class') {
        formValues.classCount = +formValues.classCount + 1;
      } else if (type === 'period') {
        formValues.periodCount = +formValues.periodCount + 1;
        formValues.singlePeriodCount = +formValues.singlePeriodCount + 1;
      }

      this.curriculumPlanService
        .updateAnExistingSubjectToYearGroup(freeSessionId, formValues)
        .subscribe(() => {
          if (this.selectedBlock?.id) {
            this.curriculumPlanService
              .getBlock(this.selectedBlock.id)
              .subscribe(res => {
                let sessionIds: number[] = [];
                if (type === 'class') {
                  sessionIds = res.sessions.filter(item => item.classIndex === formValues.classCount).map(session => session.id);
                } else if (type === 'period') {
                  sessionIds = res.sessions.filter(item => item.periodIndex === formValues.periodCount).map(session => session.id);
                }
                this.dropSubject(event, sessionIds);
              })
          }
        });
    }
  }

  dropToPeriodOrClass(event: CdkDragDrop<any>, type: string, index: number): void {
    if (!event.isPointerOverContainer || !this.selectedBlock) {
      return;
    }
    const classNamePrefix = this.selectedBlock.sessions.find(session => session.classIndex === index)?.className.split('-')[0];
    const freeSessionId = this.selectedBlock?.subjectToYearGroups[0].id;
    const sessionsWithSubject = this.selectedBlock.sessions
      .filter(session => (type === 'class' ? session.className.split('-')[0] : session.periodIndex) === (type === 'class' ? classNamePrefix : index) && session.subjectToYearGroupId !== freeSessionId);
    const sessionsWithoutSubject = this.selectedBlock.sessions
      .filter(session => (type === 'class' ? session.className.split('-')[0] : session.periodIndex) === (type === 'class' ? classNamePrefix : index) && session.subjectToYearGroupId === freeSessionId);
    let sessionIds: number[] = [];

    if (event.item.data.staffId) {
      if (type === 'class') {
        sessionIds = sessionsWithSubject
          .filter(session => !this.shiftKeyPressed
            ? !session.mainStaffId && !session.additionalStaffIds.includes(event.item.data.staffId) && session.classIndex == index
            : session.mainStaffId !== event.item.data.staffId && !session.additionalStaffIds.includes(event.item.data.staffId) && session.classIndex == index)
          .map(session => session.id);
      } else if (type === 'period') {
        return
      }

      if (!sessionIds.length) {
        this.snackbar.error(this.translate.instant('Staff already assigned to all the sessions.'))
        return
      }

      this.addStaffToBlock({
        isAdditionalStaff: this.shiftKeyPressed,
        listIndex: 0,
        staffId: event.item.data.staffId,
        sessionIds: sessionIds
      } as ICurriculumPlanAddStaffToBlock);
    } else if (event.item.data.roomId) {
      if (type === 'class') {
        sessionIds = sessionsWithSubject.filter(session => !session.roomId && session.classIndex == index).map(session => session.id);
      } else if (type === 'period') {
        return
      }

      if (!sessionIds.length) {
        this.snackbar.error(this.translate.instant('All sessions in this class has assigned room.'));
        return
      }

      this.addRoomToBlock({
        sessionIds,
        roomId: event.item.data.roomId,
        listIndex: 0
      } as ICurriculumPlanAddRoomToBlock);
    } else {
      if (type === 'class') {
        sessionIds = sessionsWithoutSubject.map(session => session.id);
      } else if (type === 'period') {
        sessionIds = sessionsWithoutSubject.map(session => session.id);
      }

      if (!sessionIds.length) {
        type === 'class'
          ? this.snackbar.error(this.translate.instant('All sessions in this class has assigned subject.'))
          : this.snackbar.error(this.translate.instant('All sessions in this period has assigned subject.'));
        return
      }

      this.dropSubject(event, sessionIds);
    }
  }

  dropToExistingSubject(event: CdkDragDrop<any>, session: ICurriculumSessions): void {
    const sessionRow = session.className.split('-')[0];
    const periodIndex = session.periodIndex;

    const sessionIds = this.selectedBlock?.sessions.filter(session => session.className.split('-')[0] === sessionRow && session.periodIndex === periodIndex).map(session => session.id);
    this.dropSubject(event, sessionIds);
  }


  addToSelectedSubjectSessions(sessionId: number): void {
    let selectedSubjectSessionIds = this.selectedSubjectSessionIds$.getValue();

    if (selectedSubjectSessionIds.includes(sessionId)) {
      selectedSubjectSessionIds = selectedSubjectSessionIds.filter(id => id !== sessionId);
    } else {
      selectedSubjectSessionIds.push(sessionId);
    }

    this.selectedSubjectSessionIds$.next(selectedSubjectSessionIds);
    this.selectedSubjectSessionIds = selectedSubjectSessionIds;
  }
}
