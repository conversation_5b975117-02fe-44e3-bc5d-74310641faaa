@import 'apps/bromcom-timetables/src/assets/styles/variables';

.filter-popup {
  padding: 12px;
  background-color: white;
  border: 1px solid lightgray;

  .filter-header {
    display: flex;
    height: 25px;
    justify-content: center;
    align-items: center;
    border-bottom: 2px solid $color-primary-blue-600;
    margin-bottom: 8px;

    .filter-icon {
      margin-bottom: 6px;

      i {
        font-size: 14px;
        color: $color-blue-500;
      }
    }
  }

  .search {
    margin-bottom: 3px;
  }

  .filter-options {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    align-self: stretch;
    font-size: 14px;
    color: $color-primary-blue-600;
    border-bottom: 1px solid $color-blue-200;

    span {
      margin-bottom: 8px;
      cursor: pointer;
    }
  }

  .vertical {
    margin-top: 4px;
    max-height: 168px;
    overflow-y: auto;
    gap: 2px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }

  .filter-buttons {
    display: flex;
    height: 32px;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    margin-top: 16px;

    .button {
      width: auto !important;
    }
  }

  .data-item {
    display: flex;
  }
}

::ng-deep .ag-popup-child {
  box-shadow: none !important;
}

::ng-deep .ag-popup-editor {
  border: none !important;
}

::ng-deep .mat-expansion-panel-content {
  z-index: 100000;
  position: fixed;
}

::ng-deep .mat-expansion-panel {
  box-shadow: none !important;
  border: 1px solid $color-blue-grey-300;
  color: $color-blue-grey-600 !important;
}

::ng-deep .mat-expansion-panel:hover {
  border: 1px solid $color-blue-500;
}

::ng-deep .mat-expansion-panel-header,
::ng-deep .mat-expansion-panel-header.mat-expanded {
  height: 30px !important;
  transition: none !important;
  padding: 0 0 0 12px !important;

  .mat-content.mat-content-hide-toggle {
    margin: 0 !important;
  }

  .list-items {
    padding-right: 8px;
    max-width: 187px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    ::ng-deep .chip {
      margin-right: 8px;

      ::ng-deep .bcm-chip__size-medium {
        font-size: 12px !important;
      }
    }
  }
}

::ng-deep .mat-expansion-panel-header-title {
  color: $color-grey-300 !important;
  font-family: "Inter", sans-serif;
  flex-basis: unset !important;
}

::ng-deep .mat-expansion-panel-header:hover {
  background-color: transparent !important;
}

::ng-deep .mat-expansion-panel-body {
  margin-top: 1px;
  padding: 0 !important;
}

::ng-deep .mat-expansion-panel-header-description {
  justify-content: flex-end;
  margin: 0;

  .icon {
    font-size: 16px;
    transform: rotate(360deg);
    transition: transform 0.2s ease-in-out;
  }

  .icon.is-open {
    transform: rotate(180deg);
  }
}

::ng-deep .mat-expansion-panel .mat-expansion-panel-header.cdk-program-focused:not([aria-disabled=true]) {
  background-color: $color-white-0;
}

::ng-deep .mdc-tooltip__surface {
  max-height: 500px !important;
  overflow-y: auto !important;
}
