import { ICellRendererComp, ICellRendererParams } from 'ag-grid-community';

export class BlocksCellRenderer implements ICellRendererComp {
  eGui: HTMLSpanElement;
  value: any;
  blocksData!: string[];

  constructor() {
    this.eGui = document.createElement('div');
  }

  init(params: any) {
    this.value = params.value;
    this.blocksData = params.blocksData;
    this.updateBlocks();
  }

  updateBlocks(): void {
    this.eGui.style.width = '100%';
    let innerHTML = ''
    this.blocksData.forEach((block) => {
      const element = `<bcm-chip color="blue" style="margin-right: 8px">${block}</bcm-chip>`
      innerHTML += element;
    });
    this.eGui.innerHTML = `<div style="width: 100%; display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">${innerHTML}</div>`;
  }

  getGui() {
    return this.eGui;
  }

  refresh(params: ICellRendererParams) {
    this.value = params.value;

    this.eGui.innerHTML = '';
    this.updateBlocks();

    return true;
  }
}
