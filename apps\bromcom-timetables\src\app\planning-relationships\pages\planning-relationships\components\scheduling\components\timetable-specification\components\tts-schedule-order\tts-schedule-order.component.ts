import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { CellValueChangedEvent, GridOptions, GridReadyEvent } from 'ag-grid-community';
import { GridApi } from 'ag-grid-community/dist/lib/gridApi';
import { Subject, takeUntil } from 'rxjs';
import { RelationshipsService } from '../../../../../../../../services/relationships.service';
import { TranslateService } from '@ngx-translate/core';
import { gridOptions } from './tts-schedule-order-gridoptions';
import { ISubject } from '../../../../../../../../../_shared/models/ISubject';
import { TimetableSpecificationsService } from '../../../../../../../../services/timetable-specifications.service';
import { SnackbarService } from '@bromcom/ui';

@Component({
  selector: 'bromcom-tts-schedule-order',
  templateUrl: './tts-schedule-order.component.html',
  styleUrls: ['./tts-schedule-order.component.scss']
})
export class TtsScheduleOrderComponent implements OnInit, OnDestroy {
  @Input() timetableId!: number;
  params!: GridReadyEvent;
  gridApi!: GridApi;
  searchControl = new FormControl(null);
  gridOptions!: GridOptions;
  gridData: any[] = [];
  subjectOptions: Partial<ISubject>[] = [];
  gridActivated = true;
  selectedGroupItemIds: number[] = [];
  filterValues: { text: string, id: number }[] = [];
  rowGroupFilterOptions = [
    { text: this.translate.instant('Year Groups'), id: 1 },
    { text: this.translate.instant('Departments'), id: 2 },
    { text: this.translate.instant('Subjects'), id: 3 }
  ]

  readonly unsubscribe$: Subject<void> = new Subject();

  constructor(
    protected timetableSpecificationsService: TimetableSpecificationsService,
    protected relationshipsService: RelationshipsService,
    protected translate: TranslateService,
    private snackbar: SnackbarService
  ) {
  }

  ngOnInit(): void {
    this.relationshipsService.subjectsData$
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(subjects => this.subjectOptions = subjects.map(subject => ({
        id: subject.id,
        name: subject.name,
        text: subject.name,
        code: subject.code,
        color: '#' + subject.color
      })))

    this.searchControl.valueChanges
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(searchValue => {
        this.gridApi.setQuickFilter(searchValue ?? '');
      })

    this.gridOptions = gridOptions.call(this);
    this.timetableSpecificationsService.getHierarchy(this.timetableId).subscribe(hierarchy => {
      this.selectedGroupItemIds = hierarchy.groupHierarchy;
      hierarchy.groupHierarchy.forEach(groupId => {
        const option = this.rowGroupFilterOptions.find(option => option.id === groupId)
        if (option) {
          this.filterValues.push(option)
        }
      });

      if (hierarchy.groupHierarchy.length === 0) {
        this.clearFilters();
      } else {
        this.addFilters();
      }
    })
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  onGridReady(params: GridReadyEvent): void {
    this.params = params;
    this.gridApi = params.api;
    this.gridApi.setDomLayout('normal');
  }

  onCellValueChanged(event: CellValueChangedEvent<any>) {
    const { id, order, isGroup } = event.data

    this.timetableSpecificationsService.updateOrder({ id, order, isGroup })
      .subscribe({
        next: () => {
          this.getOrderList();
          this.snackbar.saved();
        },
        error: ({ error }) => {
          if (error?.validationErrors && error?.validationErrors[0]) {
            this.snackbar.error(error.validationErrors[0].errorMessage);
          }
        }
      })
  }

  rowGroupFilterChanged(rowGroupFilterIds: { selectedItemIds: number[] }) {
    if (this.selectedGroupItemIds?.length === rowGroupFilterIds.selectedItemIds.length) {
      return;
    } else if (this.selectedGroupItemIds?.length > 1 && rowGroupFilterIds.selectedItemIds.length === 0) {
      // when 2-3 was selected and user used clear all action
      this.filterValues = []
    } else if (this.selectedGroupItemIds?.length < 2 && rowGroupFilterIds.selectedItemIds.length === 3) {
      // when 0-1 was selected and user used select all action
      this.rowGroupFilterOptions.forEach(option => {
        if (!this.filterValues.map(option => option.id).includes(option.id)) {
          this.filterValues.push(option);
        }
      })
    } else if (rowGroupFilterIds.selectedItemIds.length > this.filterValues.length) {
      // when user adds one more manually
      const difference = rowGroupFilterIds.selectedItemIds.filter(id => !this.filterValues.find(value => value.id === id));
      const option = this.rowGroupFilterOptions.find(option => option.id === difference[0]);
      if (option) {
        this.filterValues.push(option)
      }
    } else {
      // user removes one manually
      const difference = this.filterValues.find(value => !rowGroupFilterIds.selectedItemIds.includes(value.id))
      if (difference) {
        this.filterValues = this.filterValues.filter(option => option.id !== difference.id)
      }
    }

    const data = { groupHierarchy: Object.values(this.filterValues).map(value => value.id) }
    this.timetableSpecificationsService.setHierarchy(this.timetableId, data).subscribe(() => {
      if (rowGroupFilterIds.selectedItemIds.length === 0) {
        this.clearFilters();
      } else {
        this.addFilters();
      }
    })

    this.selectedGroupItemIds = rowGroupFilterIds.selectedItemIds
  }

  getOrderList(): void {
    this.timetableSpecificationsService.getScheduleOrderList(this.timetableId).subscribe(data => {
      this.gridData = data;
      this.setRowData();
    })
  }

  clearFilters() {
    this.gridActivated = false;
    this.gridOptions.autoGroupColumnDef = {};
    this.gridOptions.treeData = false;
    this.gridOptions.getDataPath = (data: any) => {
      return data.orgHierarchy;
    };
    setTimeout(() => {
      this.gridActivated = true;
    }, 0);

    setTimeout(() => {
      this.getOrderList();
    }, 50)
  }

  addFilters() {
    this.gridActivated = false;
    this.gridOptions.autoGroupColumnDef = {
      headerName: '',
      minWidth: 300,
      menuTabs: [],
      cellRendererParams: {
        suppressCount: true

      }
    };
    this.gridOptions.groupDefaultExpanded = -1;
    this.gridOptions.treeData = true;
    this.gridOptions.getDataPath = (data: any) => {
      return data.orgHierarchy;
    };
    setTimeout(() => {
      this.gridActivated = true;
    }, 0);

    setTimeout(() => {
      this.getOrderList();
    }, 50)
  }

  setRowData(): void {
    this.gridApi?.setRowData(this.gridData);
    this.gridApi.redrawRows();
  }
}
