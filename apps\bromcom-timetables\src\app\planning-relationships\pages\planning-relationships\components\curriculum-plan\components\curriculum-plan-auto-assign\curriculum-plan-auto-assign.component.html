<ng-container *ngIf="isOpen">
<bcm-modal size="medium" class="auto-assign-modal"
          (bcm-modal-before-close)="onClose()"
           #autoAssignModal>
  <bcm-modal-header>{{ 'Auto Assign' | translate }}</bcm-modal-header>

  <div class="modal-body">
    <div class="line">
      <div>
        <label>{{ 'Select Year Group(s)*' | translate }}</label><br>
        <bromcom-list class="list"
                      [placeholder]="'Select Year Group(s)' | translate"
                      [checkboxes]="true"
                      [data]="selectYearGroups"
                      [formControl]="selectYearGroupsForm"
                      [checkAll]="true"
        ></bromcom-list>
      </div>

      <div>
        <label>{{ 'Staff / Room*' | translate }}</label><br>
        <bromcom-list [placeholder]="'Staff / Room' | translate"
                      [data]="staffAndRoom"
                      [formControl]="staffRoomForm"
        ></bromcom-list>
      </div>
    </div>

    <div class="line">
      <div>
        <label>{{ 'List by' | translate }}</label><br>
        <bromcom-list [placeholder]="'List by' | translate"
                      [data]="listBy"
                      [formControl]="listByForm"
        ></bromcom-list>
      </div>
    </div>

    <label>{{ 'Select Subject(s) / Department(s)*' | translate }}</label>
    <div class="modal-container">
      <bromcom-input-field [formControl]="searchControl"
                           [icon]="'fal fa-search'"
                           style="width: 100%"
                           [iconPosition]="'prefix'"
                           [placeholder]="'Search' | translate"
      ></bromcom-input-field>

      <div class="header-menu">
        <mat-checkbox disableRipple
                      [formControl]="selectAllSubjects">{{ 'Select All' | translate }}
        </mat-checkbox>
        <bcm-button kind="link" (click)="clearAll()">
          {{ 'Clear All' | translate }}
        </bcm-button>
      </div>

      <div [formGroup]="subjectsForm">
        <ul formArrayName="subjectList">
          <ng-container *ngIf="listByForm.value && +listByForm.value === 1">
            <li *ngFor="let subject of subjectList; let i = index">
              <div class="line"
                   [ngClass]="{
                   'hidden': searchControl.value &&
                    (!subject.name.toLowerCase().includes(searchControl.value.toLowerCase()) &&
                      !subject.code.toLowerCase().includes(searchControl.value.toLowerCase()))}">
                <mat-checkbox disableRipple
                              [formControlName]="i" (click)="onChange()"></mat-checkbox>
                <div class="circle" [style]="{'background-color': '#' + subject?.color}"></div>
                {{ subject.code }} - {{ subject.name }}
              </div>
            </li>
          </ng-container>

          <ng-container *ngIf="listByForm.value && +listByForm.value === 2">
            <li>
              <mat-tree [dataSource]="dataSource" [treeControl]="treeControl" class="tree">
                <mat-tree-node *matTreeNodeDef="let node"
                               matTreeNodeToggle
                               [ngClass]="{
                               'hidden': searchControl.value &&
                                (!node.name.toLowerCase().includes(searchControl.value.toLowerCase()) &&
                                  !node.code.toLowerCase().includes(searchControl.value.toLowerCase()))}">
                  <div class="line">
                    <mat-checkbox [formControlName]="node.name | getSubjectIndex:subjectList" (click)="onChange()"></mat-checkbox>
                    <div class="circle" [style]="{'background-color': '#' + node?.color}"></div>
                    {{ node.code }} - {{ node.name }}
                  </div>
                </mat-tree-node>

                <mat-nested-tree-node *matTreeNodeDef="let node; when: hasChild">
                  <div class="mat-tree-node">
                    <div matTreeNodeToggle
                         [attr.aria-label]="'Toggle ' + node.name">
                      <div class="expand-icons">
                        <bcm-icon class="icon"
                                  *ngIf="!treeControl.isExpanded(node)"
                                  icon="far fa-chevron-right">
                        </bcm-icon>

                        <bcm-icon class="icon"
                                  *ngIf="treeControl.isExpanded(node)"
                                  icon="far fa-chevron-down">
                        </bcm-icon>
                      </div>
                    </div>
                    {{ node.name }}
                  </div>

                  <div [class.tree-invisible]="!treeControl.isExpanded(node)" role="group">
                    <ng-container matTreeNodeOutlet></ng-container>
                  </div>
                </mat-nested-tree-node>
              </mat-tree>
            </li>
          </ng-container>
        </ul>
      </div>
    </div>


    <mat-checkbox disableRipple
                  [formControl]="useAnyStaffWithASubjectRelationshipForm">
      {{ 'Use any staff with a subject relationship' | translate }}
    </mat-checkbox>
  </div>

  <bcm-modal-footer class="footer">
    <div>
      <bcm-button kind="ghost"
                  data-dismiss>
        {{ 'Cancel' | translate }}
      </bcm-button>
    </div>

    <div>
      <bcm-button
        [disabled]="this.staffRoomForm.invalid || this.selectYearGroupsForm.invalid || !(this.subjectsForm.controls.subjectList.value | hasSelected)"
        (click)="removeAssignment()"
        kind="ghost"
        style="margin-right: 16px;">
        {{ 'Remove Assignments' | translate }}
      </bcm-button>

      <bcm-button
        [disabled]="this.staffRoomForm.invalid || this.selectYearGroupsForm.invalid || !(this.subjectsForm.controls.subjectList.value | hasSelected)"
        (click)="autoAssignment()">
        {{ 'Auto Assign' | translate }}
      </bcm-button>
    </div>
  </bcm-modal-footer>
</bcm-modal>
</ng-container>
<bromcom-general-modal #removeStaffAndRoom
                       icon="far fa-exclamation-triangle"
                       type="warningAmber"
                       [header]="'Are you sure you want to continue?' | translate"
                       [cancelText]="'Cancel' | translate"
                       [nextText]="'Proceed' | translate"
                       [description]="removeStaffAndRoomDescription"
                       (nextEvent)="onRemoveStaffAndRoom()">
</bromcom-general-modal>

<bromcom-general-modal #warningModal
                       icon="far fa-exclamation-triangle"
                       type="warningAmber"
                       [header]="'Would you like to continue?' | translate"
                       [cancelText]="'Cancel' | translate"
                       [nextText]="'Continue' | translate"
                       [description]="'Relationship and staffing information may not be complete, which can affect the auto-assignment.' | translate"
                       (nextEvent)="autoAssignmentHelper()">
</bromcom-general-modal>

<bromcom-general-modal #conflictsAutoAssignModalComponent icon="far fa-times-circle" type="warning"
                       [header]="'Unresolved conflicts!' | translate"
                       [cancelText]="'Close' | translate" [nextText]="'Go to Conflicts' | translate"
                       [description]="'Conflicts have been found in sessions preventing the auto assigning process from running. Please review and resolve these and try again' | translate"
                       (nextEvent)="onShowConflicts()"  [dismiss]="false">
</bromcom-general-modal>

<bromcom-conflicts-modal #conflictsModalComponent
[projectName]="projectName"
[timetableName]="timetableName"
></bromcom-conflicts-modal>