<div cdkDropList
     class="list"
     [id]="id"
     [cdkDropListData]="data"
     [cdkDropListConnectedTo]="dropListConnectedTo"
     (cdkDropListDropped)="drop($event)"
     (cdkDropListEntered)="cdkDragEntered($event)"
     (cdkDropListExited)="cdkDropExited($event)">
  <div class="header">
    <div class="part-one">
      {{'Subjects' | translate}}
    </div>

    <div class="part-two">
      <div [class.disabled]="selectedSubjects | isDisabled">{{ 'No. of Classes'| translate}}</div>
      <div [class.disabled]="selectedSubjects | isDisabled"
           [ngClass]="{'margin-right': scrollbarVisible()}">{{ 'Period Count'| translate}}</div>
    </div>
  </div>

  <div class="content" #contentId>
    <div class="empty-drag-wrapper" [ngClass]="{'drag-entered': isCdkDragging}" *ngIf="data.length <= 0">
      <div class="empty-drag-disabled">
        <div class="empty-header">{{'Subjects Not Available' | translate}}</div>
        <div class="description">{{ 'Add subjects to create timetable' | translate}}</div>
      </div>
    </div>

    <div class="drg-and-drop-holder" [ngClass]="{'drag-entered': isCdkDragging}">
      <ng-container class="box" *ngFor="let item of data; let dragAndDropIndex = index">
        <bromcom-box-drag [item]="item"
                          [index]="dragAndDropIndex"
                          [linearGroups]="linearGroups"
                          [optionsGroups]="optionsGroups"
                          [subjects]="subjects"
                          (updateSubjectToYearGroupEvent)="updateSubjectToYearGroup($event)"
                          (addSubjectToYearGroupToLinearGroupEvent)="addToLinearGroup($event)"
                          (addSubjectToYearGroupToOptionsGroupEvent)="addToOptionsGroup($event)"
                          (updateSubjectInfoEvent)="updateSubjectInfo()"
                          (copyBlockEvent)="copyBlock($event)"
                          (removeEvent)="removeSubject($event)">
        </bromcom-box-drag>
      </ng-container>
    </div>
  </div>
</div>
