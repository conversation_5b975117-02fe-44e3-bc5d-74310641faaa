import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BaseModalComponent } from '../../../../../../_shared/components/BaseModalComponent';

@Component({
  selector: 'bromcom-delete-confirmation',
  templateUrl: './delete-confirmation.component.html',
  styleUrls: ['./delete-confirmation.component.scss']
})
export class DeleteConfirmationComponent  extends BaseModalComponent {
  @ViewChild('confirmationModal', { static: false }) confirmationModal!: ElementRef;
  @Input() type: 'info' | 'warning' | 'success' = 'info'
  @Input() headerText = "";
  @Input() mainText = "";
  @Input() dismiss = true;
  @Input() buttonTitle = this.translate.instant('Confirm');
  @Input() buttonColor = 'blue';
  @Output() deleteClicked: EventEmitter<boolean> = new EventEmitter();

  constructor(
    private translate: TranslateService
  ){
    super();
  }

  show(): void {
    this.isOpen = true;
    setTimeout(() => {
      this.confirmationModal.nativeElement.show();
    }, 100);

  }

  close(): void {
    this.isOpen = false;
    this.confirmationModal.nativeElement.hide();
  }

  onDeleteClicked(isDelete: boolean): void {
    this.deleteClicked.emit(isDelete);
  }
}
