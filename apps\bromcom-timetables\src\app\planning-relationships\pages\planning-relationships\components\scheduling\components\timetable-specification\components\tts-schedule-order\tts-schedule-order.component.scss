@import 'apps/bromcom-timetables/src/assets/styles/variables';
@import 'apps/bromcom-timetables/src/app/planning-relationships/pages/planning-relationships/components/scheduling/components/timetable-specification/styles/tts-ag-grid';

.tts-schedule-order-container {
  padding: 24px 0 0;

  .action-bar {
    @include action-bar;
    align-items: flex-start !important;
  }

  .table-container {
    height: 45vh;
    position: relative;

    .flex-row {
      display: flex;
      flex-wrap: wrap;

      .chip-container {
        margin: 0 4px 8px 0;

        .icon {
          margin-right: 4px;
        }
      }
    }

    .ag-grid,
    .ag-grid-shadow {
      position: absolute;
    }

    ::ng-deep bromcom-ag-grid-priorities-dropdown {
      width: 100% !important;
    }
  }
}

::ng-deep .tts-schedule-order-container .table-container .ag-cell {
  height: 48px !important;
  padding-top: 4px;
}

::ng-deep .tts-schedule-order-container .table-container .ag-cell.order-cell {
  height: 48px !important;
  display: flex !important;
  align-items: end !important;
  margin-top: 0;

  .bcm-list__input {
    height: 32px !important;
  }
}

::ng-deep .ag-cell-not-inline-editing {
  border-right: 1px solid $color-blue-grey-200 !important;
}

::ng-deep .ag-cell-wrapper.ag-row-group {
  padding-left: 0;
  padding-right: 0;
  height: auto;
  display: flex;
  flex-direction: unset;
  align-items: unset;
  justify-content: unset;
  gap: unset;
}

::ng-deep .ag-ltr .ag-row-group-indent-1 {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

::ng-deep .create-row-groups .block-vertical {
  padding: 4px !important;

  .filter-header {
    margin-bottom: 16px;
  }

  .filter-options {
    font-weight: 500;
    border-bottom: 0 !important;
  }

  .switch-container {
    width: 100%;
    padding: 0 8px;

    .bcm-label__text {
      font-size: 16px !important;
      color: $color-blue-grey-700 !important;
    }
  }
}
